<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Event Ticket - {{ $booking->booking_reference }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .ticket {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin: 0 auto;
            max-width: 600px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 2px dashed #e9ecef;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #4f46e5;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #4f46e5;
            margin-bottom: 10px;
        }
        .ticket-title {
            font-size: 18px;
            color: #6b7280;
        }
        .event-info {
            margin-bottom: 30px;
        }
        .event-title {
            font-size: 22px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 15px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        .info-label {
            font-weight: bold;
            color: #6b7280;
            width: 40%;
        }
        .info-value {
            color: #1f2937;
            width: 60%;
            text-align: right;
        }
        .booking-details {
            background-color: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .booking-ref {
            font-size: 20px;
            font-weight: bold;
            color: #4f46e5;
            text-align: center;
            margin-bottom: 15px;
        }
        .qr-section {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
        }
        .qr-placeholder {
            width: 120px;
            height: 120px;
            background-color: #f3f4f6;
            border: 2px solid #d1d5db;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #6b7280;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 12px;
            color: #6b7280;
        }
        .important-note {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="ticket">
        <!-- Header -->
        <div class="header">
            <div class="logo">EventManager</div>
            <div class="ticket-title">Event Ticket</div>
        </div>

        <!-- Event Information -->
        <div class="event-info">
            <div class="event-title">{{ $booking->event->title }}</div>
            
            <div class="info-row">
                <span class="info-label">Date & Time:</span>
                <span class="info-value">{{ \Carbon\Carbon::parse($booking->event->start_date)->format('l, F j, Y \a\t g:i A') }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Venue:</span>
                <span class="info-value">{{ $booking->event->venue_name }}</span>
            </div>
            
            @if($booking->event->venue_address)
            <div class="info-row">
                <span class="info-label">Address:</span>
                <span class="info-value">{{ $booking->event->venue_address }}</span>
            </div>
            @endif
            
            <div class="info-row">
                <span class="info-label">Ticket Type:</span>
                <span class="info-value">{{ $booking->ticketType->name }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Quantity:</span>
                <span class="info-value">{{ $booking->quantity }} ticket{{ $booking->quantity > 1 ? 's' : '' }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Total Amount:</span>
                <span class="info-value">₹{{ number_format($booking->total_amount) }}</span>
            </div>
        </div>

        <!-- Booking Details -->
        <div class="booking-details">
            <div class="booking-ref">{{ $booking->booking_reference }}</div>
            
            <div class="info-row">
                <span class="info-label">Attendee Name:</span>
                <span class="info-value">{{ $booking->attendee_name }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Email:</span>
                <span class="info-value">{{ $booking->attendee_email }}</span>
            </div>
            
            @if($booking->attendee_phone)
            <div class="info-row">
                <span class="info-label">Phone:</span>
                <span class="info-value">{{ $booking->attendee_phone }}</span>
            </div>
            @endif
            
            <div class="info-row">
                <span class="info-label">Booking Date:</span>
                <span class="info-value">{{ \Carbon\Carbon::parse($booking->booked_at)->format('F j, Y \a\t g:i A') }}</span>
            </div>
        </div>

        <!-- QR Code Section -->
        <div class="qr-section">
            <div class="qr-placeholder">
                QR Code<br>
                (Scan at venue)
            </div>
            <div style="font-size: 12px; color: #6b7280;">
                Present this ticket at the venue entrance
            </div>
        </div>

        <!-- Important Notes -->
        <div class="important-note">
            <strong>Important:</strong> Please bring a valid photo ID along with this ticket. 
            This ticket is non-transferable and must be presented by the named attendee. 
            Screenshots or photocopies will not be accepted.
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>Generated on {{ $generated_at }}</p>
            <p>For support, contact <NAME_EMAIL></p>
            <p>Thank you for choosing EventManager!</p>
        </div>
    </div>
</body>
</html>
