import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { eventsAPI } from '@/services/api'

export const useEventsStore = defineStore('events', () => {
  // State
  const events = ref([])
  const currentEvent = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const filters = ref({
    search: '',
    category: '',
    location: '',
    dateFrom: '',
    dateTo: '',
    priceMin: '',
    priceMax: ''
  })
  const pagination = ref({
    currentPage: 1,
    perPage: 12,
    total: 0,
    lastPage: 1
  })

  // Getters
  const filteredEvents = computed(() => {
    let filtered = events.value

    if (filters.value.search) {
      filtered = filtered.filter(event =>
        event.title.toLowerCase().includes(filters.value.search.toLowerCase()) ||
        event.description.toLowerCase().includes(filters.value.search.toLowerCase())
      )
    }

    if (filters.value.category) {
      filtered = filtered.filter(event => event.category === filters.value.category)
    }

    if (filters.value.location) {
      filtered = filtered.filter(event =>
        event.location.toLowerCase().includes(filters.value.location.toLowerCase())
      )
    }

    return filtered
  })

  const upcomingEvents = computed(() => {
    const now = new Date()
    return events.value.filter(event => new Date(event.start_date) > now)
  })

  const featuredEvents = computed(() => {
    return events.value.filter(event => event.is_featured)
  })

  // Actions
  const fetchEvents = async (params = {}) => {
    isLoading.value = true
    error.value = null

    try {
      let response
      if (params.featured) {
        response = await eventsAPI.getFeatured(params)
      } else {
        response = await eventsAPI.getAll(params)
      }

      if (response.success) {
        if (params.featured) {
          // For featured events, just set the events array
          events.value = response.data || []
        } else {
          // For paginated results
          events.value = response.data || []

          // Update pagination if provided
          if (response.current_page !== undefined) {
            pagination.value = {
              currentPage: response.current_page,
              perPage: response.per_page,
              total: response.total,
              lastPage: response.last_page
            }
          }
        }
      } else {
        throw new Error(response.message || 'Failed to fetch events')
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to fetch events'
      console.error('Fetch events error:', err)

      // Fallback to mock data for demo
      if (params.featured) {
        events.value = [
          {
            id: 1,
            title: 'Tech Conference 2025',
            slug: 'tech-conference-2025',
            description: 'Join us for the biggest tech conference of the year with industry leaders',
            start_date: '2025-02-15T09:00:00Z',
            venue_name: 'Convention Center',
            location: 'Downtown',
            price: 2500,
            category: { name: 'Conference' },
            is_featured: true
          },
          {
            id: 2,
            title: 'Music Festival',
            slug: 'music-festival',
            description: 'Experience amazing live music performances from top artists',
            start_date: '2025-03-20T18:00:00Z',
            venue_name: 'City Park',
            location: 'Central Park',
            price: 1500,
            category: { name: 'Festival' },
            is_featured: true
          },
          {
            id: 3,
            title: 'Art Exhibition',
            slug: 'art-exhibition',
            description: 'Discover contemporary art from local and international artists',
            start_date: '2025-04-10T10:00:00Z',
            venue_name: 'Art Gallery',
            location: 'Arts District',
            price: 500,
            category: { name: 'Exhibition' },
            is_featured: true
          }
        ]
      }
    } finally {
      isLoading.value = false
    }
  }

  const fetchEvent = async (id) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await axios.get(`/events/${id}`)
      currentEvent.value = response.data
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to fetch event'
      console.error('Fetch event error:', err)
      return null
    } finally {
      isLoading.value = false
    }
  }

  const createEvent = async (eventData) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await axios.post('/events', eventData)
      events.value.unshift(response.data)
      return { success: true, event: response.data }
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to create event'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const updateEvent = async (id, eventData) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await axios.put(`/events/${id}`, eventData)
      const index = events.value.findIndex(event => event.id === id)
      if (index !== -1) {
        events.value[index] = response.data
      }
      if (currentEvent.value?.id === id) {
        currentEvent.value = response.data
      }
      return { success: true, event: response.data }
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to update event'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const deleteEvent = async (id) => {
    isLoading.value = true
    error.value = null

    try {
      await axios.delete(`/events/${id}`)
      events.value = events.value.filter(event => event.id !== id)
      if (currentEvent.value?.id === id) {
        currentEvent.value = null
      }
      return { success: true }
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to delete event'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const bookEvent = async (eventId, bookingData) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await axios.post(`/events/${eventId}/book`, bookingData)
      return { success: true, booking: response.data }
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to book event'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {
      search: '',
      category: '',
      location: '',
      dateFrom: '',
      dateTo: '',
      priceMin: '',
      priceMax: ''
    }
  }

  const setCurrentPage = (page) => {
    pagination.value.currentPage = page
  }

  return {
    // State
    events,
    currentEvent,
    isLoading,
    error,
    filters,
    pagination,

    // Getters
    filteredEvents,
    upcomingEvents,
    featuredEvents,

    // Actions
    fetchEvents,
    fetchEvent,
    createEvent,
    updateEvent,
    deleteEvent,
    bookEvent,
    setFilters,
    clearFilters,
    setCurrentPage
  }
})
