<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">My Tickets</h1>
        <p class="text-gray-600">Manage your event bookings and tickets</p>
      </div>

      <!-- Filter Tabs -->
      <div class="mb-8">
        <nav class="flex space-x-8">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            @click="activeTab = tab.key"
            :class="[
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === tab.key
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            {{ tab.label }}
            <span
              v-if="tab.count > 0"
              :class="[
                'ml-2 py-0.5 px-2 rounded-full text-xs',
                activeTab === tab.key
                  ? 'bg-indigo-100 text-indigo-600'
                  : 'bg-gray-100 text-gray-900'
              ]"
            >
              {{ tab.count }}
            </span>
          </button>
        </nav>
      </div>

      <!-- Loading State -->
      <div v-if="bookingStore.isLoading" class="flex justify-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>

      <!-- Tickets List -->
      <div v-else-if="filteredTickets.length > 0" class="space-y-6">
        <div
          v-for="booking in filteredTickets"
          :key="booking.id"
          class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
        >
          <div class="p-6">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                  <h3 class="text-lg font-semibold text-gray-900">
                    {{ booking.event.title }}
                  </h3>
                  <span
                    :class="[
                      'px-2 py-1 rounded-full text-xs font-medium',
                      getStatusColor(booking.status)
                    ]"
                  >
                    {{ booking.status.charAt(0).toUpperCase() + booking.status.slice(1) }}
                  </span>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4">
                  <div class="flex items-center">
                    <CalendarIcon class="w-4 h-4 mr-2" />
                    <span>{{ formatDate(booking.event.start_date) }}</span>
                  </div>
                  <div class="flex items-center">
                    <MapPinIcon class="w-4 h-4 mr-2" />
                    <span>{{ booking.event.venue_name || booking.event.location }}</span>
                  </div>
                  <div class="flex items-center">
                    <TicketIcon class="w-4 h-4 mr-2" />
                    <span>{{ booking.quantity }} ticket{{ booking.quantity > 1 ? 's' : '' }}</span>
                  </div>
                </div>

                <div class="text-sm text-gray-600">
                  <p><strong>Booking ID:</strong> {{ booking.booking_reference }}</p>
                  <p><strong>Total Amount:</strong> ₹{{ booking.total_amount.toLocaleString() }}</p>
                  <p v-if="booking.ticket_type"><strong>Ticket Type:</strong> {{ booking.ticket_type.name }}</p>
                </div>
              </div>

              <div class="ml-6 flex flex-col space-y-2">
                <!-- QR Code -->
                <div v-if="booking.status === 'confirmed'" class="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center">
                  <QrCodeIcon class="w-12 h-12 text-gray-400" />
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col space-y-2">
                  <button
                    v-if="booking.status === 'confirmed'"
                    @click="downloadTicket(booking)"
                    class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors"
                  >
                    Download Ticket
                  </button>
                  
                  <router-link
                    :to="`/events/${booking.event.slug || booking.event.id}`"
                    class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors text-center"
                  >
                    View Event
                  </router-link>
                  
                  <button
                    v-if="canCancel(booking)"
                    @click="cancelBooking(booking)"
                    class="bg-red-100 text-red-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-red-200 transition-colors"
                  >
                    Cancel Booking
                  </button>
                </div>
              </div>
            </div>

            <!-- Additional Details (Expandable) -->
            <div v-if="booking.showDetails" class="mt-6 pt-6 border-t border-gray-200">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 class="font-medium text-gray-900 mb-2">Booking Details</h4>
                  <dl class="space-y-1 text-sm">
                    <div class="flex justify-between">
                      <dt class="text-gray-600">Booked on:</dt>
                      <dd class="text-gray-900">{{ formatDate(booking.created_at) }}</dd>
                    </div>
                    <div class="flex justify-between">
                      <dt class="text-gray-600">Payment Method:</dt>
                      <dd class="text-gray-900">{{ booking.payment_method || 'Card' }}</dd>
                    </div>
                    <div v-if="booking.special_requests" class="flex justify-between">
                      <dt class="text-gray-600">Special Requests:</dt>
                      <dd class="text-gray-900">{{ booking.special_requests }}</dd>
                    </div>
                  </dl>
                </div>
                
                <div v-if="booking.attendees && booking.attendees.length > 0">
                  <h4 class="font-medium text-gray-900 mb-2">Attendees</h4>
                  <ul class="space-y-1 text-sm">
                    <li v-for="attendee in booking.attendees" :key="attendee.id" class="text-gray-600">
                      {{ attendee.name }} ({{ attendee.email }})
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Toggle Details Button -->
            <button
              @click="booking.showDetails = !booking.showDetails"
              class="mt-4 text-indigo-600 hover:text-indigo-700 text-sm font-medium"
            >
              {{ booking.showDetails ? 'Hide Details' : 'Show Details' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <TicketIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">
          {{ getEmptyStateMessage() }}
        </h3>
        <p class="text-gray-600 mb-6">
          {{ getEmptyStateDescription() }}
        </p>
        <router-link
          to="/events"
          class="bg-indigo-600 text-white px-6 py-3 rounded-md font-medium hover:bg-indigo-700 transition-colors"
        >
          Browse Events
        </router-link>
      </div>
    </div>

    <!-- Cancel Confirmation Modal -->
    <div v-if="showCancelModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Cancel Booking</h3>
        <p class="text-gray-600 mb-6">
          Are you sure you want to cancel this booking? This action cannot be undone.
        </p>
        <div class="flex space-x-4">
          <button
            @click="confirmCancel"
            class="flex-1 bg-red-600 text-white px-4 py-2 rounded-md font-medium hover:bg-red-700 transition-colors"
          >
            Yes, Cancel
          </button>
          <button
            @click="showCancelModal = false"
            class="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-200 transition-colors"
          >
            Keep Booking
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useBookingStore } from '@/stores/booking'

const bookingStore = useBookingStore()
const activeTab = ref('all')
const showCancelModal = ref(false)
const bookingToCancel = ref(null)

const tabs = computed(() => [
  { key: 'all', label: 'All Tickets', count: bookingStore.userBookings.length },
  { key: 'upcoming', label: 'Upcoming', count: upcomingBookings.value.length },
  { key: 'past', label: 'Past Events', count: pastBookings.value.length },
  { key: 'cancelled', label: 'Cancelled', count: cancelledBookings.value.length }
])

const upcomingBookings = computed(() => {
  const now = new Date()
  return bookingStore.userBookings.filter(booking =>
    new Date(booking.event.start_date) > now && booking.status !== 'cancelled'
  )
})

const pastBookings = computed(() => {
  const now = new Date()
  return bookingStore.userBookings.filter(booking =>
    new Date(booking.event.start_date) <= now && booking.status !== 'cancelled'
  )
})

const cancelledBookings = computed(() => {
  return bookingStore.userBookings.filter(booking => booking.status === 'cancelled')
})

const filteredTickets = computed(() => {
  switch (activeTab.value) {
    case 'upcoming':
      return upcomingBookings.value
    case 'past':
      return pastBookings.value
    case 'cancelled':
      return cancelledBookings.value
    default:
      return bookings.value
  }
})

const getStatusColor = (status) => {
  switch (status) {
    case 'confirmed':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'cancelled':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const canCancel = (booking) => {
  const eventDate = new Date(booking.event.start_date)
  const now = new Date()
  const hoursDiff = (eventDate - now) / (1000 * 60 * 60)
  
  return booking.status === 'confirmed' && hoursDiff > 24
}

const downloadTicket = async (booking) => {
  const result = await bookingStore.downloadTicket(booking.id)
  if (!result.success) {
    alert('Failed to download ticket. Please try again.')
  }
}

const cancelBooking = (booking) => {
  bookingToCancel.value = booking
  showCancelModal.value = true
}

const confirmCancel = async () => {
  const result = await bookingStore.cancelBooking(bookingToCancel.value.id)
  if (result.success) {
    showCancelModal.value = false
    bookingToCancel.value = null
  } else {
    alert('Failed to cancel booking. Please try again.')
  }
}

const getEmptyStateMessage = () => {
  switch (activeTab.value) {
    case 'upcoming':
      return 'No upcoming events'
    case 'past':
      return 'No past events'
    case 'cancelled':
      return 'No cancelled bookings'
    default:
      return 'No tickets found'
  }
}

const getEmptyStateDescription = () => {
  switch (activeTab.value) {
    case 'upcoming':
      return 'You don\'t have any upcoming events. Book some tickets to see them here.'
    case 'past':
      return 'You haven\'t attended any events yet.'
    case 'cancelled':
      return 'You don\'t have any cancelled bookings.'
    default:
      return 'You haven\'t booked any tickets yet. Start exploring events to book your first ticket.'
  }
}

onMounted(async () => {
  await bookingStore.fetchUserBookings()
})
</script>
