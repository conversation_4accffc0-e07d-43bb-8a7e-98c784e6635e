<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\Payment;
use App\Services\PaymentService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Create payment order
     */
    public function createOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'booking_id' => 'required|exists:bookings,id',
            'gateway' => 'required|in:razorpay,stripe,phonepe,payu',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $booking = Booking::findOrFail($request->booking_id);

        // Check if user owns this booking
        if ($booking->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check if booking is in valid state for payment
        if ($booking->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Booking is not in valid state for payment'
            ], 422);
        }

        // Check if payment already exists
        $existingPayment = Payment::where('booking_id', $booking->id)
                                 ->where('status', 'pending')
                                 ->first();

        if ($existingPayment) {
            return response()->json([
                'success' => true,
                'message' => 'Payment order already exists',
                'data' => [
                    'payment' => $existingPayment,
                    'order_data' => $existingPayment->gateway_response,
                ]
            ]);
        }

        $result = $this->paymentService->createPaymentOrder($booking, $request->gateway);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Payment order created successfully',
            'data' => [
                'payment' => $result['payment'],
                'order_data' => $result['order_data'],
            ]
        ]);
    }

    /**
     * Verify payment
     */
    public function verifyPayment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'gateway' => 'required|in:razorpay,stripe,phonepe,payu',
            'payment_data' => 'required|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->paymentService->verifyPayment($request->payment_data, $request->gateway);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 422);
        }

        return response()->json([
            'success' => true,
            'message' => 'Payment verified successfully',
            'data' => [
                'payment' => $result['payment'],
                'booking' => $result['booking'],
            ]
        ]);
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus(Request $request, Payment $payment)
    {
        // Check if user owns this payment
        if ($payment->booking->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $result = $this->paymentService->getPaymentStatus(
            $payment->gateway_payment_id, 
            $payment->gateway
        );

        return response()->json($result);
    }

    /**
     * Request refund
     */
    public function requestRefund(Request $request, Payment $payment)
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:500',
            'amount' => 'nullable|numeric|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if user owns this payment
        if ($payment->booking->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check if payment can be refunded
        if ($payment->status !== 'completed') {
            return response()->json([
                'success' => false,
                'message' => 'Payment cannot be refunded'
            ], 422);
        }

        // Check refund amount
        $refundAmount = $request->amount ?? $payment->amount;
        if ($refundAmount > $payment->amount) {
            return response()->json([
                'success' => false,
                'message' => 'Refund amount cannot exceed payment amount'
            ], 422);
        }

        $result = $this->paymentService->processRefund($payment, $refundAmount);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Refund processed successfully',
            'data' => [
                'payment' => $result['payment'],
                'refund_data' => $result['refund_data'],
            ]
        ]);
    }

    /**
     * Get available payment gateways
     */
    public function getGateways()
    {
        $gateways = $this->paymentService->getAvailableGateways();

        // Filter only enabled gateways
        $enabledGateways = array_filter($gateways, function ($gateway) {
            return $gateway['enabled'];
        });

        return response()->json([
            'success' => true,
            'data' => $enabledGateways
        ]);
    }

    /**
     * Handle payment webhook
     */
    public function webhook(Request $request, string $gateway)
    {
        try {
            Log::info('Payment webhook received', [
                'gateway' => $gateway,
                'payload' => $request->all(),
                'headers' => $request->headers->all(),
            ]);

            // Handle webhook based on gateway
            switch ($gateway) {
                case 'razorpay':
                    return $this->handleRazorpayWebhook($request);
                case 'stripe':
                    return $this->handleStripeWebhook($request);
                default:
                    return response()->json(['message' => 'Unsupported gateway'], 400);
            }

        } catch (\Exception $e) {
            Log::error('Payment webhook error', [
                'gateway' => $gateway,
                'error' => $e->getMessage(),
            ]);

            return response()->json(['message' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Handle Razorpay webhook
     */
    protected function handleRazorpayWebhook(Request $request)
    {
        $payload = $request->getContent();
        $signature = $request->header('X-Razorpay-Signature');
        $webhookSecret = config('services.razorpay.webhook_secret');

        // Verify webhook signature
        $razorpayService = new \App\Services\PaymentGateways\RazorpayService();
        if (!$razorpayService->verifyWebhookSignature($payload, $signature, $webhookSecret)) {
            return response()->json(['message' => 'Invalid signature'], 401);
        }

        $data = json_decode($payload, true);
        $event = $data['event'];

        // Handle different events
        switch ($event) {
            case 'payment.captured':
                $this->handlePaymentCaptured($data['payload']['payment']['entity']);
                break;
            case 'payment.failed':
                $this->handlePaymentFailed($data['payload']['payment']['entity']);
                break;
            case 'refund.processed':
                $this->handleRefundProcessed($data['payload']['refund']['entity']);
                break;
        }

        return response()->json(['message' => 'Webhook processed successfully']);
    }

    /**
     * Handle payment captured event
     */
    protected function handlePaymentCaptured(array $paymentData)
    {
        $payment = Payment::where('gateway_payment_id', $paymentData['id'])->first();
        
        if ($payment && $payment->status === 'pending') {
            $payment->update([
                'status' => 'completed',
                'paid_at' => now(),
                'gateway_response' => array_merge($payment->gateway_response ?? [], $paymentData),
            ]);

            $payment->booking->update([
                'status' => 'confirmed',
                'payment_status' => 'completed',
            ]);
        }
    }

    /**
     * Handle payment failed event
     */
    protected function handlePaymentFailed(array $paymentData)
    {
        $payment = Payment::where('gateway_payment_id', $paymentData['id'])->first();
        
        if ($payment) {
            $payment->update([
                'status' => 'failed',
                'failure_reason' => $paymentData['error_description'] ?? 'Payment failed',
                'gateway_response' => array_merge($payment->gateway_response ?? [], $paymentData),
            ]);
        }
    }

    /**
     * Handle refund processed event
     */
    protected function handleRefundProcessed(array $refundData)
    {
        $payment = Payment::where('gateway_payment_id', $refundData['payment_id'])->first();
        
        if ($payment) {
            $payment->update([
                'status' => 'refunded',
                'refund_amount' => $refundData['amount'] / 100, // Convert from paise
                'refunded_at' => now(),
                'gateway_response' => array_merge($payment->gateway_response ?? [], $refundData),
            ]);

            $payment->booking->update([
                'status' => 'refunded',
                'payment_status' => 'refunded',
            ]);
        }
    }
}
