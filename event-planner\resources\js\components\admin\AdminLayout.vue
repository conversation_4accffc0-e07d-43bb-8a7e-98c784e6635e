<template>
  <div class="min-h-screen bg-gray-50 flex">
    <!-- Sidebar -->
    <div :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'" 
         class="fixed inset-y-0 left-0 z-50 w-64 bg-gray-900 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0">
      
      <!-- Logo -->
      <div class="flex items-center justify-center h-16 px-4 bg-gray-800">
        <div class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 2L3 7v11a1 1 0 001 1h3v-6h6v6h3a1 1 0 001-1V7l-7-5z"/>
            </svg>
          </div>
          <span class="text-xl font-bold text-white">Admin Panel</span>
        </div>
      </div>

      <!-- Navigation -->
      <nav class="mt-8 px-4">
        <div class="space-y-2">
          <router-link
            v-for="item in navigation"
            :key="item.name"
            :to="item.href"
            :class="[
              $route.path.startsWith(item.href) 
                ? 'bg-gray-800 text-white' 
                : 'text-gray-300 hover:bg-gray-700 hover:text-white',
              'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors'
            ]"
          >
            <component :is="item.icon" class="mr-3 h-5 w-5 flex-shrink-0" />
            {{ item.name }}
            <span v-if="item.badge" 
                  class="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">
              {{ item.badge }}
            </span>
          </router-link>
        </div>

        <!-- User Role Section -->
        <div class="mt-8 pt-6 border-t border-gray-700">
          <div class="px-3 py-2">
            <p class="text-xs font-semibold text-gray-400 uppercase tracking-wider">Role</p>
            <p class="mt-1 text-sm text-gray-300">{{ userRole }}</p>
          </div>
        </div>
      </nav>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col lg:pl-0">
      <!-- Top Bar -->
      <div class="sticky top-0 z-40 flex h-16 bg-white border-b border-gray-200 lg:border-none">
        <!-- Mobile menu button -->
        <button @click="sidebarOpen = !sidebarOpen"
                class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 lg:hidden">
          <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"/>
          </svg>
        </button>

        <!-- Top bar content -->
        <div class="flex-1 px-4 flex justify-between items-center">
          <!-- Page title and breadcrumb -->
          <div class="flex-1">
            <nav class="flex" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-2">
                <li>
                  <div class="flex items-center">
                    <router-link to="/admin" class="text-gray-400 hover:text-gray-500">
                      <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                      </svg>
                    </router-link>
                  </div>
                </li>
                <li v-for="(crumb, index) in breadcrumbs" :key="index">
                  <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <span class="ml-2 text-sm font-medium text-gray-500">{{ crumb }}</span>
                  </div>
                </li>
              </ol>
            </nav>
            <h1 class="mt-1 text-2xl font-semibold text-gray-900">{{ pageTitle }}</h1>
          </div>

          <!-- Right side items -->
          <div class="ml-4 flex items-center space-x-4">
            <!-- Notifications -->
            <button class="relative p-2 text-gray-400 hover:text-gray-500">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.07 2.82l-.908.908a1.5 1.5 0 000 2.121l9.192 9.192a1.5 1.5 0 002.121 0l.908-.908a1.5 1.5 0 000-2.121L12.191 2.82a1.5 1.5 0 00-2.121 0z"/>
              </svg>
              <span v-if="notificationCount > 0" 
                    class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                {{ notificationCount }}
              </span>
            </button>

            <!-- Quick Actions -->
            <div class="relative">
              <button @click="showQuickActions = !showQuickActions"
                      class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                Quick Actions
              </button>
              
              <!-- Quick Actions Dropdown -->
              <div v-if="showQuickActions" 
                   class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                <router-link to="/admin/events/create" 
                             class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Create Event
                </router-link>
                <router-link to="/admin/users/create" 
                             class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Add User
                </router-link>
                <router-link to="/admin/reports" 
                             class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  View Reports
                </router-link>
              </div>
            </div>

            <!-- Profile dropdown -->
            <div class="relative">
              <button @click="showProfileMenu = !showProfileMenu"
                      class="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <div class="h-8 w-8 bg-indigo-600 rounded-full flex items-center justify-center">
                  <span class="text-white font-medium">{{ userInitials }}</span>
                </div>
                <span class="hidden md:block text-gray-700 font-medium">{{ userName }}</span>
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>

              <!-- Profile dropdown menu -->
              <div v-if="showProfileMenu" 
                   class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                <router-link to="/admin/profile" 
                             class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Your Profile
                </router-link>
                <router-link to="/admin/settings" 
                             class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Settings
                </router-link>
                <button @click="handleLogout" 
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Sign out
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Page Content -->
      <main class="flex-1 overflow-y-auto">
        <div class="py-6">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <slot />
          </div>
        </div>
      </main>
    </div>

    <!-- Mobile sidebar overlay -->
    <div v-if="sidebarOpen" 
         @click="sidebarOpen = false"
         class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

const sidebarOpen = ref(false)
const showQuickActions = ref(false)
const showProfileMenu = ref(false)
const notificationCount = ref(3)

// Navigation items based on user role
const navigation = computed(() => {
  const baseItems = [
    {
      name: 'Dashboard',
      href: '/admin',
      icon: 'svg',
    },
    {
      name: 'Events',
      href: '/admin/events',
      icon: 'svg',
    },
    {
      name: 'Bookings',
      href: '/admin/bookings',
      icon: 'svg',
      badge: '12'
    },
    {
      name: 'Users',
      href: '/admin/users',
      icon: 'svg',
    },
    {
      name: 'Reports',
      href: '/admin/reports',
      icon: 'svg',
    }
  ]

  // Add admin-only items
  if (authStore.hasRole(['admin'])) {
    baseItems.push(
      {
        name: 'Roles',
        href: '/admin/roles',
        icon: 'svg',
      },
      {
        name: 'Settings',
        href: '/admin/settings',
        icon: 'svg',
      }
    )
  }

  return baseItems
})

const userRole = computed(() => authStore.user?.role || 'Admin')
const userName = computed(() => authStore.user?.name || 'Admin User')
const userInitials = computed(() => {
  const name = userName.value
  return name.split(' ').map(n => n[0]).join('').toUpperCase()
})

const pageTitle = computed(() => {
  const routeName = route.name
  const titles = {
    'admin-dashboard': 'Dashboard',
    'admin-events': 'Events Management',
    'admin-bookings': 'Bookings Management',
    'admin-users': 'User Management',
    'admin-reports': 'Reports & Analytics',
    'admin-roles': 'Role Management',
    'admin-settings': 'System Settings'
  }
  return titles[routeName] || 'Admin Panel'
})

const breadcrumbs = computed(() => {
  const path = route.path
  const segments = path.split('/').filter(Boolean)
  return segments.slice(1).map(segment => 
    segment.charAt(0).toUpperCase() + segment.slice(1).replace('-', ' ')
  )
})

const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}

// Close dropdowns when clicking outside
const handleClickOutside = (event) => {
  if (!event.target.closest('.relative')) {
    showQuickActions.value = false
    showProfileMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
