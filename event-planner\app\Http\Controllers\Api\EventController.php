<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Booking;
use App\Models\TicketType;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class EventController extends Controller
{
    /**
     * Get paginated list of events with filtering
     */
    public function index(Request $request)
    {
        $query = Event::with(['category', 'ticketTypes'])
            ->where('status', 'published')
            ->where('start_date', '>', now());

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('venue_name', 'like', "%{$search}%");
            });
        }

        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        if ($request->filled('location')) {
            $query->where('venue_name', 'like', "%{$request->location}%");
        }

        if ($request->filled('date_from')) {
            $query->where('start_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('start_date', '<=', $request->date_to);
        }

        if ($request->filled('price_min') || $request->filled('price_max')) {
            $query->whereHas('ticketTypes', function ($q) use ($request) {
                if ($request->filled('price_min')) {
                    $q->where('price', '>=', $request->price_min);
                }
                if ($request->filled('price_max')) {
                    $q->where('price', '<=', $request->price_max);
                }
            });
        }

        if ($request->filled('featured')) {
            $query->where('is_featured', true);
        }

        // Sort by start date
        $query->orderBy('start_date', 'asc');

        $perPage = $request->get('per_page', 12);
        $events = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $events->items(),
            'current_page' => $events->currentPage(),
            'last_page' => $events->lastPage(),
            'per_page' => $events->perPage(),
            'total' => $events->total()
        ]);
    }

    /**
     * Get featured events
     */
    public function featured(Request $request)
    {
        $limit = $request->get('limit', 6);
        
        $events = Event::with(['category', 'ticketTypes'])
            ->where('status', 'published')
            ->where('is_featured', true)
            ->where('start_date', '>', now())
            ->orderBy('start_date', 'asc')
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $events
        ]);
    }

    /**
     * Get single event details
     */
    public function show(Event $event)
    {
        $event->load(['category', 'ticketTypes' => function ($query) {
            $query->where('is_active', true)->orderBy('price', 'asc');
        }]);

        // Add available tickets count for each ticket type
        $event->ticketTypes->each(function ($ticketType) {
            $sold = Booking::where('ticket_type_id', $ticketType->id)
                ->where('status', 'confirmed')
                ->sum('quantity');
            
            $ticketType->quantity_sold = $sold;
            $ticketType->quantity_remaining = max(0, $ticketType->quantity_available - $sold);
        });

        return response()->json([
            'success' => true,
            'data' => $event
        ]);
    }

    /**
     * Book an event
     */
    public function book(Request $request, Event $event)
    {
        $validator = Validator::make($request->all(), [
            'ticket_type_id' => 'required|exists:ticket_types,id',
            'quantity' => 'required|integer|min:1|max:10',
            'attendee_name' => 'required|string|max:255',
            'attendee_email' => 'required|email|max:255',
            'attendee_phone' => 'nullable|string|max:20',
            'special_requests' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $ticketType = TicketType::findOrFail($request->ticket_type_id);

        // Check if ticket type belongs to this event
        if ($ticketType->event_id !== $event->id) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid ticket type for this event'
            ], 422);
        }

        // Check availability
        $sold = Booking::where('ticket_type_id', $ticketType->id)
            ->where('status', 'confirmed')
            ->sum('quantity');
        
        $available = $ticketType->quantity_available - $sold;
        
        if ($request->quantity > $available) {
            return response()->json([
                'success' => false,
                'message' => "Only {$available} tickets available"
            ], 422);
        }

        // Check max quantity per order
        if ($request->quantity > $ticketType->max_quantity_per_order) {
            return response()->json([
                'success' => false,
                'message' => "Maximum {$ticketType->max_quantity_per_order} tickets per order"
            ], 422);
        }

        DB::beginTransaction();
        try {
            // Create booking
            $booking = Booking::create([
                'user_id' => auth()->id(),
                'event_id' => $event->id,
                'ticket_type_id' => $ticketType->id,
                'quantity' => $request->quantity,
                'unit_price' => $ticketType->price,
                'total_amount' => $ticketType->price * $request->quantity,
                'attendee_name' => $request->attendee_name,
                'attendee_email' => $request->attendee_email,
                'attendee_phone' => $request->attendee_phone,
                'special_requests' => $request->special_requests,
                'booking_reference' => 'BK' . strtoupper(uniqid()),
                'status' => 'confirmed', // Auto-confirm for demo
                'booked_at' => now(),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Booking confirmed successfully',
                'booking' => $booking->load(['event', 'ticketType'])
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'Booking failed. Please try again.'
            ], 500);
        }
    }

    /**
     * Get event bookings (for organizers/admins)
     */
    public function bookings(Request $request, Event $event)
    {
        // Check if user can view bookings for this event
        $user = $request->user();
        if (!$user->hasRole(['Super Admin', 'Manager']) && $event->created_by !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $bookings = Booking::with(['user', 'ticketType'])
            ->where('event_id', $event->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $bookings->items(),
            'current_page' => $bookings->currentPage(),
            'last_page' => $bookings->lastPage(),
            'per_page' => $bookings->perPage(),
            'total' => $bookings->total()
        ]);
    }
}
