<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\EventCategory;
use App\Models\TicketType;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class EventController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Event::with(['category', 'creator', 'ticketTypes']);

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('venue_name', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $events = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $events,
            'categories' => EventCategory::active()->ordered()->get(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'required|exists:event_categories,id',
            'venue_name' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'venue_latitude' => 'nullable|numeric|between:-90,90',
            'venue_longitude' => 'nullable|numeric|between:-180,180',
            'start_date' => 'required|date|after:now',
            'end_date' => 'required|date|after:start_date',
            'booking_start_date' => 'required|date|before:start_date',
            'booking_end_date' => 'required|date|after:booking_start_date|before:start_date',
            'max_capacity' => 'required|integer|min:1',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'status' => 'required|in:draft,published,cancelled,completed',
            'seo_meta' => 'nullable|array',
            'custom_fields' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $request->all();
        $data['created_by'] = auth()->id();

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $data['featured_image'] = $request->file('featured_image')->store('events/featured', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            $galleryPaths = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryPaths[] = $image->store('events/gallery', 'public');
            }
            $data['gallery_images'] = $galleryPaths;
        }

        $event = Event::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Event created successfully',
            'data' => $event->load(['category', 'creator'])
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Event $event)
    {
        $event->load(['category', 'creator', 'ticketTypes', 'bookings.user']);

        return response()->json([
            'success' => true,
            'data' => $event
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Event $event)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'category_id' => 'required|exists:event_categories,id',
            'venue_name' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'venue_latitude' => 'nullable|numeric|between:-90,90',
            'venue_longitude' => 'nullable|numeric|between:-180,180',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'booking_start_date' => 'required|date',
            'booking_end_date' => 'required|date|after:booking_start_date',
            'max_capacity' => 'required|integer|min:1',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'status' => 'required|in:draft,published,cancelled,completed',
            'seo_meta' => 'nullable|array',
            'custom_fields' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $request->all();

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($event->featured_image) {
                Storage::disk('public')->delete($event->featured_image);
            }
            $data['featured_image'] = $request->file('featured_image')->store('events/featured', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            // Delete old gallery images
            if ($event->gallery_images) {
                foreach ($event->gallery_images as $image) {
                    Storage::disk('public')->delete($image);
                }
            }

            $galleryPaths = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryPaths[] = $image->store('events/gallery', 'public');
            }
            $data['gallery_images'] = $galleryPaths;
        }

        $event->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Event updated successfully',
            'data' => $event->load(['category', 'creator'])
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Event $event)
    {
        // Check if event has bookings
        if ($event->bookings()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete event with existing bookings'
            ], 422);
        }

        // Delete associated images
        if ($event->featured_image) {
            Storage::disk('public')->delete($event->featured_image);
        }

        if ($event->gallery_images) {
            foreach ($event->gallery_images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $event->delete();

        return response()->json([
            'success' => true,
            'message' => 'Event deleted successfully'
        ]);
    }

    /**
     * Duplicate an event
     */
    public function duplicate(Event $event)
    {
        $newEvent = $event->replicate();
        $newEvent->title = $event->title . ' (Copy)';
        $newEvent->slug = Str::slug($newEvent->title);
        $newEvent->status = 'draft';
        $newEvent->is_published = false;
        $newEvent->created_by = auth()->id();
        $newEvent->save();

        // Duplicate ticket types
        foreach ($event->ticketTypes as $ticketType) {
            $newTicketType = $ticketType->replicate();
            $newTicketType->event_id = $newEvent->id;
            $newTicketType->quantity_sold = 0;
            $newTicketType->save();
        }

        return response()->json([
            'success' => true,
            'message' => 'Event duplicated successfully',
            'data' => $newEvent->load(['category', 'creator', 'ticketTypes'])
        ]);
    }

    /**
     * Get event analytics
     */
    public function analytics(Event $event)
    {
        $analytics = [
            'total_bookings' => $event->bookings()->count(),
            'confirmed_bookings' => $event->bookings()->confirmed()->count(),
            'total_tickets_sold' => $event->tickets()->count(),
            'total_revenue' => $event->bookings()->paid()->sum('final_amount'),
            'capacity_utilization' => ($event->tickets()->count() / $event->max_capacity) * 100,
            'booking_trends' => $event->bookings()
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
            'ticket_type_sales' => $event->ticketTypes()
                ->withCount('tickets')
                ->get()
                ->map(function($ticketType) {
                    return [
                        'name' => $ticketType->name,
                        'sold' => $ticketType->tickets_count,
                        'available' => $ticketType->quantity_available,
                        'revenue' => $ticketType->tickets_count * $ticketType->price
                    ];
                })
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics
        ]);
    }
}
