<template>
  <AdminLayout>
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
          <p class="text-gray-600">Comprehensive insights into your event management platform</p>
        </div>
        <div class="flex space-x-3">
          <select v-model="dateRange" @change="loadReports"
                  class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
            <option value="1y">Last Year</option>
            <option value="custom">Custom Range</option>
          </select>
          <button @click="exportReport" 
                  class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors">
            Export Report
          </button>
          <button @click="scheduleReport" 
                  class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            Schedule Report
          </button>
        </div>
      </div>

      <!-- Key Metrics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Revenue</p>
              <p class="text-3xl font-bold text-gray-900">₹{{ totalRevenue.toLocaleString() }}</p>
              <p class="text-sm text-green-600 mt-1">+{{ revenueGrowth }}% from last period</p>
            </div>
            <div class="p-3 bg-green-100 rounded-full">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Total Bookings</p>
              <p class="text-3xl font-bold text-gray-900">{{ totalBookings.toLocaleString() }}</p>
              <p class="text-sm text-blue-600 mt-1">+{{ bookingGrowth }}% from last period</p>
            </div>
            <div class="p-3 bg-blue-100 rounded-full">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Active Events</p>
              <p class="text-3xl font-bold text-gray-900">{{ activeEvents }}</p>
              <p class="text-sm text-purple-600 mt-1">{{ upcomingEvents }} upcoming</p>
            </div>
            <div class="p-3 bg-purple-100 rounded-full">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
              </svg>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Avg. Ticket Price</p>
              <p class="text-3xl font-bold text-gray-900">₹{{ avgTicketPrice }}</p>
              <p class="text-sm text-orange-600 mt-1">{{ conversionRate }}% conversion</p>
            </div>
            <div class="p-3 bg-orange-100 rounded-full">
              <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Revenue Chart -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Revenue Trends</h3>
            <p class="text-sm text-gray-500">Daily revenue over selected period</p>
          </div>
          <div class="p-6">
            <div class="h-64">
              <BookingTrendsChart :data="revenueChartData" :time-range="dateRange" />
            </div>
          </div>
        </div>

        <!-- Event Performance -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Top Performing Events</h3>
            <p class="text-sm text-gray-500">By revenue and bookings</p>
          </div>
          <div class="p-6">
            <div class="h-64">
              <EventPerformanceChart :data="eventPerformanceData" />
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Reports -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Event Categories Performance -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Category Performance</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div v-for="category in categoryPerformance" :key="category.name" 
                   class="flex items-center justify-between">
                <div class="flex items-center">
                  <div :class="category.color" class="w-3 h-3 rounded-full mr-3"></div>
                  <span class="text-sm font-medium text-gray-900">{{ category.name }}</span>
                </div>
                <div class="text-right">
                  <div class="text-sm font-semibold text-gray-900">₹{{ category.revenue.toLocaleString() }}</div>
                  <div class="text-xs text-gray-500">{{ category.events }} events</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- User Engagement -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">User Engagement</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">New Users</span>
                <span class="text-sm font-semibold text-gray-900">{{ newUsers }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Returning Users</span>
                <span class="text-sm font-semibold text-gray-900">{{ returningUsers }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Avg. Session Duration</span>
                <span class="text-sm font-semibold text-gray-900">{{ avgSessionDuration }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Bounce Rate</span>
                <span class="text-sm font-semibold text-gray-900">{{ bounceRate }}%</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Mobile Users</span>
                <span class="text-sm font-semibold text-gray-900">{{ mobileUsers }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Methods -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Payment Methods</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div v-for="method in paymentMethods" :key="method.name" 
                   class="flex items-center justify-between">
                <div class="flex items-center">
                  <div :class="method.color" class="w-3 h-3 rounded-full mr-3"></div>
                  <span class="text-sm font-medium text-gray-900">{{ method.name }}</span>
                </div>
                <div class="text-right">
                  <div class="text-sm font-semibold text-gray-900">{{ method.percentage }}%</div>
                  <div class="text-xs text-gray-500">₹{{ method.amount.toLocaleString() }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Transactions -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">Recent Transactions</h3>
          <button @click="exportTransactions" 
                  class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
            Export All
          </button>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="transaction in recentTransactions" :key="transaction.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                  {{ transaction.id }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ transaction.event_name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ transaction.customer_name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                  ₹{{ transaction.amount.toLocaleString() }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ transaction.payment_method }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="getTransactionStatusClass(transaction.status)" 
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                    {{ transaction.status }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(transaction.created_at) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import AdminLayout from '@/components/admin/AdminLayout.vue'
import BookingTrendsChart from '@/components/admin/charts/BookingTrendsChart.vue'
import EventPerformanceChart from '@/components/admin/charts/EventPerformanceChart.vue'

const dateRange = ref('30d')

// Key Metrics
const totalRevenue = ref(2456780)
const revenueGrowth = ref(15.2)
const totalBookings = ref(1247)
const bookingGrowth = ref(8.5)
const activeEvents = ref(24)
const upcomingEvents = ref(12)
const avgTicketPrice = ref(1970)
const conversionRate = ref(3.2)

// User Engagement
const newUsers = ref(156)
const returningUsers = ref(891)
const avgSessionDuration = ref('4m 32s')
const bounceRate = ref(24.5)
const mobileUsers = ref(68)

// Category Performance
const categoryPerformance = ref([
  { name: 'Conferences', revenue: 856000, events: 8, color: 'bg-blue-500' },
  { name: 'Festivals', revenue: 642000, events: 6, color: 'bg-green-500' },
  { name: 'Workshops', revenue: 458000, events: 7, color: 'bg-purple-500' },
  { name: 'Exhibitions', revenue: 321000, events: 3, color: 'bg-yellow-500' },
  { name: 'Sports', revenue: 179780, events: 2, color: 'bg-red-500' }
])

// Payment Methods
const paymentMethods = ref([
  { name: 'UPI', percentage: 45, amount: 1105551, color: 'bg-green-500' },
  { name: 'Credit Card', percentage: 28, amount: 687898, color: 'bg-blue-500' },
  { name: 'Debit Card', percentage: 18, amount: 442220, color: 'bg-purple-500' },
  { name: 'Net Banking', percentage: 9, amount: 221111, color: 'bg-orange-500' }
])

// Recent Transactions
const recentTransactions = ref([
  {
    id: 'TXN-2025-001234',
    event_name: 'Tech Conference 2025',
    customer_name: 'John Doe',
    amount: 2500,
    payment_method: 'UPI',
    status: 'completed',
    created_at: '2025-01-12T10:30:00Z'
  },
  {
    id: 'TXN-2025-001235',
    event_name: 'Music Festival',
    customer_name: 'Jane Smith',
    amount: 3500,
    payment_method: 'Credit Card',
    status: 'completed',
    created_at: '2025-01-12T09:15:00Z'
  },
  {
    id: 'TXN-2025-001236',
    event_name: 'Art Exhibition',
    customer_name: 'Mike Johnson',
    amount: 1200,
    payment_method: 'UPI',
    status: 'pending',
    created_at: '2025-01-12T08:45:00Z'
  }
])

// Chart Data
const revenueChartData = computed(() => ({
  labels: ['Jan 1', 'Jan 5', 'Jan 10', 'Jan 15', 'Jan 20', 'Jan 25', 'Jan 30'],
  datasets: [{
    label: 'Revenue (₹)',
    data: [45000, 52000, 48000, 61000, 58000, 67000, 63000],
    borderColor: 'rgb(99, 102, 241)',
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    fill: true,
    tension: 0.4
  }]
}))

const eventPerformanceData = ref({
  labels: ['Tech Conference', 'Music Festival', 'Art Exhibition', 'Food Festival', 'Sports Event'],
  datasets: [{
    label: 'Revenue (₹000s)',
    data: [856, 642, 458, 321, 180],
    backgroundColor: [
      'rgba(99, 102, 241, 0.8)',
      'rgba(16, 185, 129, 0.8)',
      'rgba(245, 158, 11, 0.8)',
      'rgba(239, 68, 68, 0.8)',
      'rgba(139, 92, 246, 0.8)'
    ]
  }]
})

const loadReports = () => {
  // Load reports based on date range
  console.log('Loading reports for:', dateRange.value)
}

const exportReport = () => {
  alert('Exporting comprehensive report...')
}

const scheduleReport = () => {
  alert('Opening report scheduling dialog...')
}

const exportTransactions = () => {
  alert('Exporting transaction data...')
}

const getTransactionStatusClass = (status) => {
  const classes = {
    completed: 'bg-green-100 text-green-800',
    pending: 'bg-yellow-100 text-yellow-800',
    failed: 'bg-red-100 text-red-800',
    refunded: 'bg-gray-100 text-gray-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(() => {
  loadReports()
})
</script>
