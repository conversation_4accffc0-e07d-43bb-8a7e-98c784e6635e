<template>
  <div class="bg-gray-800 rounded-xl overflow-hidden hover:shadow-xl hover:shadow-pink-500/20 transition-all duration-300 group">
    <div class="flex flex-col md:flex-row">
      <!-- Event Image -->
      <div class="relative w-full md:w-80 h-48 md:h-auto overflow-hidden">
        <img v-if="event.featured_image" 
             :src="event.featured_image" 
             :alt="event.title"
             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
        <div v-else class="w-full h-full bg-gradient-to-br from-pink-500 to-cyan-500 flex items-center justify-center">
          <span class="text-white text-3xl font-bold">{{ event.title.charAt(0) }}</span>
        </div>
        
        <!-- Category Badge -->
        <div class="absolute top-4 left-4">
          <span class="bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm"
                :style="{ backgroundColor: event.category?.color_code + '80' }">
            {{ event.category?.name }}
          </span>
        </div>
        
        <!-- Featured Badge -->
        <div v-if="event.is_featured" class="absolute top-4 right-4">
          <span class="bg-gradient-to-r from-pink-500 to-cyan-500 text-white px-3 py-1 rounded-full text-sm font-bold">
            ⭐ Featured
          </span>
        </div>

        <!-- Availability Indicator -->
        <div class="absolute bottom-4 right-4">
          <div v-if="availabilityStatus === 'available'" class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
            Available
          </div>
          <div v-else-if="availabilityStatus === 'limited'" class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
            Limited
          </div>
          <div v-else-if="availabilityStatus === 'sold_out'" class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
            Sold Out
          </div>
        </div>
      </div>

      <!-- Event Details -->
      <div class="flex-1 p-6">
        <div class="flex flex-col h-full">
          <!-- Header -->
          <div class="flex-1">
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <h3 class="text-2xl font-bold text-white mb-2 group-hover:text-pink-400 transition-colors">
                  {{ event.title }}
                </h3>
                <p class="text-gray-400 mb-4 line-clamp-3">
                  {{ event.description || event.short_description }}
                </p>
              </div>
              <div class="ml-4 text-right">
                <div class="text-2xl font-bold text-green-400 mb-1">₹{{ getMinPrice() }}</div>
                <div class="text-sm text-gray-500">Starting from</div>
              </div>
            </div>

            <!-- Event Info Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div class="flex items-center text-gray-300">
                <svg class="w-5 h-5 mr-3 text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <div>
                  <div class="font-medium">{{ formatDate(event.start_date) }}</div>
                  <div class="text-sm text-gray-500">{{ formatTime(event.start_date) }}</div>
                </div>
              </div>
              
              <div class="flex items-center text-gray-300">
                <svg class="w-5 h-5 mr-3 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <div>
                  <div class="font-medium">{{ event.venue_name }}</div>
                  <div class="text-sm text-gray-500 truncate">{{ event.venue_address }}</div>
                </div>
              </div>
              
              <div class="flex items-center text-gray-300">
                <svg class="w-5 h-5 mr-3 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                <div>
                  <div class="font-medium">{{ getCapacityInfo() }}</div>
                  <div class="text-sm text-gray-500">{{ getTicketsSold() }} tickets sold</div>
                </div>
              </div>
            </div>

            <!-- Ticket Types Preview -->
            <div v-if="event.ticket_types && event.ticket_types.length > 0" class="mb-6">
              <h4 class="text-sm font-semibold text-gray-400 mb-3">Ticket Types</h4>
              <div class="flex flex-wrap gap-2">
                <div v-for="ticket in event.ticket_types.slice(0, 3)" :key="ticket.id"
                     class="bg-gray-700 text-white px-3 py-1 rounded-full text-sm">
                  {{ ticket.name }} - ₹{{ ticket.price.toLocaleString() }}
                </div>
                <div v-if="event.ticket_types.length > 3" 
                     class="bg-gray-600 text-gray-300 px-3 py-1 rounded-full text-sm">
                  +{{ event.ticket_types.length - 3 }} more
                </div>
              </div>
            </div>

            <!-- Social Proof -->
            <div v-if="event.rating || event.reviews_count" class="flex items-center mb-6">
              <div class="flex text-yellow-400 mr-2">
                <svg v-for="i in 5" :key="i" 
                     :class="i <= (event.rating || 0) ? 'text-yellow-400' : 'text-gray-600'"
                     class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
              </div>
              <span class="text-sm text-gray-400">{{ event.rating || 0 }}/5 ({{ event.reviews_count || 0 }} reviews)</span>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-3">
            <router-link :to="`/events/${event.slug}`" 
                         class="flex-1 bg-transparent border-2 border-pink-500 text-pink-500 text-center py-3 px-6 rounded-lg font-semibold hover:bg-pink-500 hover:text-white transition-all">
              View Details
            </router-link>
            <button @click="quickBook" 
                    :disabled="availabilityStatus === 'sold_out'"
                    class="flex-1 bg-gradient-to-r from-pink-500 to-cyan-500 text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg hover:shadow-pink-500/25 transition-all disabled:opacity-50 disabled:cursor-not-allowed">
              {{ availabilityStatus === 'sold_out' ? 'Sold Out' : 'Book Now' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'EventListItem',
  props: {
    event: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const formatTime = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const getMinPrice = () => {
      if (!props.event.ticket_types || props.event.ticket_types.length === 0) return '0'
      return Math.min(...props.event.ticket_types.map(t => t.price)).toLocaleString()
    }

    const getTicketsSold = () => {
      if (!props.event.ticket_types) return 0
      return props.event.ticket_types.reduce((total, type) => total + (type.quantity_sold || 0), 0)
    }

    const getCapacityInfo = () => {
      const sold = getTicketsSold()
      const capacity = props.event.max_capacity || 0
      return `${capacity - sold} / ${capacity} available`
    }

    const availabilityStatus = computed(() => {
      if (!props.event.ticket_types || props.event.ticket_types.length === 0) return 'sold_out'
      
      const totalAvailable = props.event.ticket_types.reduce((total, type) => total + (type.quantity_available || 0), 0)
      const totalSold = getTicketsSold()
      const remaining = totalAvailable - totalSold
      
      if (remaining <= 0) return 'sold_out'
      if (remaining <= totalAvailable * 0.2) return 'limited'
      return 'available'
    })

    const quickBook = () => {
      if (availabilityStatus.value === 'sold_out') return
      router.push(`/booking/${props.event.id}`)
    }

    return {
      formatDate,
      formatTime,
      getMinPrice,
      getTicketsSold,
      getCapacityInfo,
      availabilityStatus,
      quickBook
    }
  }
}
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
