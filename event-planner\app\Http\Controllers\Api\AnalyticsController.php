<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\AnalyticsService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class AnalyticsController extends Controller
{
    protected $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    /**
     * Get dashboard overview
     */
    public function overview(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        $data = $this->analyticsService->getDashboardOverview($dateRange);

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get revenue analytics
     */
    public function revenue(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'group_by' => 'nullable|in:hour,day,week,month,year',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $dateRange = $this->getDateRange($request);
        $groupBy = $request->get('group_by', 'day');
        
        $data = $this->analyticsService->getRevenueAnalytics($dateRange, $groupBy);

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get event analytics
     */
    public function events(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        $data = $this->analyticsService->getEventAnalytics($dateRange);

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get booking analytics
     */
    public function bookings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'group_by' => 'nullable|in:hour,day,week,month,year',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $dateRange = $this->getDateRange($request);
        $groupBy = $request->get('group_by', 'day');
        
        $data = $this->analyticsService->getBookingAnalytics($dateRange, $groupBy);

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get user analytics
     */
    public function users(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        $data = $this->analyticsService->getUserAnalytics($dateRange);

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get payment analytics
     */
    public function payments(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        $data = $this->analyticsService->getPaymentAnalytics($dateRange);

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get comprehensive analytics report
     */
    public function report(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sections' => 'nullable|array',
            'sections.*' => 'in:overview,revenue,events,bookings,users,payments',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $dateRange = $this->getDateRange($request);
        $sections = $request->get('sections', ['overview', 'revenue', 'events', 'bookings', 'users', 'payments']);

        $report = [
            'date_range' => [
                'start' => $dateRange['start']->format('Y-m-d'),
                'end' => $dateRange['end']->format('Y-m-d'),
            ],
            'generated_at' => now()->toISOString(),
        ];

        if (in_array('overview', $sections)) {
            $report['overview'] = $this->analyticsService->getDashboardOverview($dateRange);
        }

        if (in_array('revenue', $sections)) {
            $report['revenue'] = $this->analyticsService->getRevenueAnalytics($dateRange);
        }

        if (in_array('events', $sections)) {
            $report['events'] = $this->analyticsService->getEventAnalytics($dateRange);
        }

        if (in_array('bookings', $sections)) {
            $report['bookings'] = $this->analyticsService->getBookingAnalytics($dateRange);
        }

        if (in_array('users', $sections)) {
            $report['users'] = $this->analyticsService->getUserAnalytics($dateRange);
        }

        if (in_array('payments', $sections)) {
            $report['payments'] = $this->analyticsService->getPaymentAnalytics($dateRange);
        }

        return response()->json([
            'success' => true,
            'data' => $report
        ]);
    }

    /**
     * Export analytics data
     */
    public function export(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'format' => 'required|in:json,csv,excel',
            'sections' => 'nullable|array',
            'sections.*' => 'in:overview,revenue,events,bookings,users,payments',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $format = $request->get('format');
        $dateRange = $this->getDateRange($request);
        $sections = $request->get('sections', ['overview', 'revenue', 'events', 'bookings', 'users', 'payments']);

        // Generate report data
        $reportData = [];
        
        if (in_array('overview', $sections)) {
            $reportData['overview'] = $this->analyticsService->getDashboardOverview($dateRange);
        }

        if (in_array('revenue', $sections)) {
            $reportData['revenue'] = $this->analyticsService->getRevenueAnalytics($dateRange);
        }

        if (in_array('events', $sections)) {
            $reportData['events'] = $this->analyticsService->getEventAnalytics($dateRange);
        }

        if (in_array('bookings', $sections)) {
            $reportData['bookings'] = $this->analyticsService->getBookingAnalytics($dateRange);
        }

        if (in_array('users', $sections)) {
            $reportData['users'] = $this->analyticsService->getUserAnalytics($dateRange);
        }

        if (in_array('payments', $sections)) {
            $reportData['payments'] = $this->analyticsService->getPaymentAnalytics($dateRange);
        }

        $filename = 'analytics-report-' . $dateRange['start']->format('Y-m-d') . '-to-' . $dateRange['end']->format('Y-m-d');

        switch ($format) {
            case 'json':
                return response()->json($reportData)
                    ->header('Content-Disposition', "attachment; filename=\"{$filename}.json\"");

            case 'csv':
                // Convert to CSV format (simplified)
                $csvData = $this->convertToCSV($reportData);
                return response($csvData)
                    ->header('Content-Type', 'text/csv')
                    ->header('Content-Disposition', "attachment; filename=\"{$filename}.csv\"");

            case 'excel':
                // For Excel export, you would typically use a package like Laravel Excel
                // For now, return JSON with Excel headers
                return response()->json($reportData)
                    ->header('Content-Type', 'application/vnd.ms-excel')
                    ->header('Content-Disposition', "attachment; filename=\"{$filename}.xlsx\"");

            default:
                return response()->json([
                    'success' => false,
                    'message' => 'Unsupported export format'
                ], 422);
        }
    }

    /**
     * Get date range from request
     */
    protected function getDateRange(Request $request): array
    {
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        $startDate = $dateFrom ? Carbon::parse($dateFrom) : Carbon::now()->subDays(30);
        $endDate = $dateTo ? Carbon::parse($dateTo) : Carbon::now();

        return [
            'start' => $startDate,
            'end' => $endDate,
        ];
    }

    /**
     * Convert data to CSV format
     */
    protected function convertToCSV(array $data): string
    {
        $csv = '';
        
        foreach ($data as $section => $sectionData) {
            $csv .= strtoupper($section) . " SECTION\n";
            
            if (is_array($sectionData)) {
                foreach ($sectionData as $key => $value) {
                    if (is_array($value) || is_object($value)) {
                        $csv .= $key . "," . json_encode($value) . "\n";
                    } else {
                        $csv .= $key . "," . $value . "\n";
                    }
                }
            }
            
            $csv .= "\n";
        }

        return $csv;
    }
}
