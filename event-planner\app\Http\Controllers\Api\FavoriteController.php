<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\Favorite;

class FavoriteController extends Controller
{
    /**
     * Get user's favorite events
     */
    public function index()
    {
        $favorites = Favorite::where('user_id', auth()->id())
                           ->with('event')
                           ->get()
                           ->pluck('event')
                           ->filter(); // Remove null events

        return response()->json([
            'success' => true,
            'data' => $favorites
        ]);
    }

    /**
     * Toggle favorite status for an event
     */
    public function toggle(Event $event)
    {
        $favorite = Favorite::where('user_id', auth()->id())
                          ->where('event_id', $event->id)
                          ->first();

        if ($favorite) {
            // Remove from favorites
            $favorite->delete();
            $isFavorite = false;
            $message = 'Event removed from favorites';
        } else {
            // Add to favorites
            Favorite::create([
                'user_id' => auth()->id(),
                'event_id' => $event->id,
            ]);
            $isFavorite = true;
            $message = 'Event added to favorites';
        }

        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => [
                'is_favorite' => $isFavorite,
                'event_id' => $event->id,
            ]
        ]);
    }
}
