import{r as v,B as q,o as S,u as D,l as M,w as c,h as e,t as a,p as B,m as F,e as I,C as A,x as l,y as n,z as x,f as m,F as h,k as L,A as p,a as N,g as u}from"./vue-vendor-BupLktX_.js";import{_ as P}from"./admin-9F2yeZU0.js";import{e as w}from"./api-D9eN0dN9.js";import"./chart-vendor-Db3utXXw.js";import"./utils-vendor-Dq7h7Pqt.js";const j={class:"space-y-6"},R={class:"flex justify-between items-center"},H={class:"text-2xl font-bold text-gray-900"},G={class:"text-gray-600"},z={class:"flex space-x-3"},W={class:"bg-white shadow-sm rounded-lg border border-gray-200"},$={class:"p-6 space-y-6"},J={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},K={class:"md:col-span-2"},O={class:"md:col-span-2"},Q={class:"bg-white shadow-sm rounded-lg border border-gray-200"},X={class:"p-6 space-y-6"},Y={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Z={class:"md:col-span-2"},ee={class:"bg-white shadow-sm rounded-lg border border-gray-200"},te={class:"p-6"},oe={key:0,class:"text-center py-8"},se={key:1,class:"space-y-4"},le={class:"flex justify-between items-start mb-4"},ne={class:"text-md font-medium text-gray-900"},re=["onClick"],ie={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},de=["onUpdate:modelValue"],ae=["onUpdate:modelValue"],ue=["onUpdate:modelValue"],me=["onUpdate:modelValue"],pe={class:"mt-4"},ge=["onUpdate:modelValue"],fe={class:"bg-white shadow-sm rounded-lg border border-gray-200"},ye={class:"p-6 space-y-6"},be={class:"mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md"},ve={class:"space-y-1 text-center"},ce={class:"flex text-sm text-gray-600"},xe={class:"relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"},we={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ke={class:"bg-white shadow-sm rounded-lg border border-gray-200"},_e={class:"p-6 space-y-6"},Ve={class:"text-xs text-gray-500 mt-1"},Ue={class:"bg-white shadow-sm rounded-lg border border-gray-200"},Ee={class:"p-6 space-y-6"},Ce={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Te={class:"flex items-center"},qe={class:"flex items-center"},Se={class:"flex items-center"},De={class:"flex items-center"},Le={__name:"CreateEvent",setup(Me){const k=D(),y=N(),d=v(!1),b=v(!1),s=q({title:"",description:"",category:"",type:"",start_date:"",end_date:"",venue_name:"",capacity:"",address:"",city:"",state:"",website:"",contact_email:"",slug:"",meta_title:"",meta_description:"",is_featured:!1,allow_cancellation:!0,send_reminders:!0,require_approval:!1,ticket_types:[]}),g=()=>{s.ticket_types.push({name:"",price:"",quantity:"",description:"",sale_start:""})},_=r=>{s.ticket_types.splice(r,1)},V=r=>{const t=r.target.files[0];t&&console.log("Image uploaded:",t.name)},U=()=>{alert("Event saved as draft!")},E=()=>{alert("Event published successfully!"),y.push("/admin/events")},C=async()=>{try{b.value=!0;const r={title:form.value.title,description:form.value.description,short_description:form.value.shortDescription,category_id:form.value.category,venue_name:form.value.venueName,venue_address:form.value.venueAddress,venue_latitude:form.value.venueLatitude,venue_longitude:form.value.venueLongitude,start_date:form.value.startDate,end_date:form.value.endDate,booking_start_date:form.value.bookingStartDate,booking_end_date:form.value.bookingEndDate,featured_image:form.value.featuredImage,gallery_images:form.value.galleryImages,max_capacity:form.value.maxCapacity,is_featured:form.value.isFeatured,is_published:form.value.isPublished,status:form.value.status,seo_meta:{title:form.value.seoTitle,description:form.value.seoDescription,keywords:form.value.seoKeywords},custom_fields:form.value.customFields,ticket_types:form.value.ticketTypes};let t;if(d.value?t=await w.update(eventId.value,r):t=await w.create(r),t.success)alert(d.value?"Event updated successfully!":"Event created successfully!"),y.push("/admin/events");else throw new Error(t.message||"Failed to save event")}catch(r){console.error("Error saving event:",r),alert("Error: "+(r.message||"Failed to save event"))}finally{b.value=!1}};return S(()=>{k.params.id&&(d.value=!0),s.ticket_types.length===0&&g()}),(r,t)=>{const T=I("router-link");return u(),M(P,null,{default:c(()=>[e("div",j,[e("div",R,[e("div",null,[e("h1",H,a(d.value?"Edit Event":"Create New Event"),1),e("p",G,a(d.value?"Update event details and settings":"Set up a new event with all the details"),1)]),e("div",z,[B(T,{to:"/admin/events",class:"bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700 transition-colors"},{default:c(()=>t[20]||(t[20]=[F(" Cancel ")])),_:1,__:[20]}),e("button",{onClick:U,class:"bg-yellow-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-yellow-700 transition-colors"}," Save as Draft "),e("button",{onClick:E,class:"bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors"},a(d.value?"Update & Publish":"Create & Publish"),1)])]),e("form",{onSubmit:A(C,["prevent"]),class:"space-y-8"},[e("div",W,[t[29]||(t[29]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Basic Information")],-1)),e("div",$,[e("div",J,[e("div",K,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Event Title *",-1)),l(e("input",{"onUpdate:modelValue":t[0]||(t[0]=o=>s.title=o),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.title]])]),e("div",O,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Description *",-1)),l(e("textarea",{"onUpdate:modelValue":t[1]||(t[1]=o=>s.description=o),rows:"4",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.description]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Category *",-1)),l(e("select",{"onUpdate:modelValue":t[2]||(t[2]=o=>s.category=o),required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[23]||(t[23]=[e("option",{value:""},"Select Category",-1),e("option",{value:"conference"},"Conference",-1),e("option",{value:"festival"},"Festival",-1),e("option",{value:"exhibition"},"Exhibition",-1),e("option",{value:"workshop"},"Workshop",-1),e("option",{value:"sports"},"Sports",-1),e("option",{value:"entertainment"},"Entertainment",-1)]),512),[[x,s.category]])]),e("div",null,[t[26]||(t[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Event Type *",-1)),l(e("select",{"onUpdate:modelValue":t[3]||(t[3]=o=>s.type=o),required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[25]||(t[25]=[e("option",{value:""},"Select Type",-1),e("option",{value:"physical"},"Physical Event",-1),e("option",{value:"virtual"},"Virtual Event",-1),e("option",{value:"hybrid"},"Hybrid Event",-1)]),512),[[x,s.type]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Start Date & Time *",-1)),l(e("input",{"onUpdate:modelValue":t[4]||(t[4]=o=>s.start_date=o),type:"datetime-local",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.start_date]])]),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"End Date & Time *",-1)),l(e("input",{"onUpdate:modelValue":t[5]||(t[5]=o=>s.end_date=o),type:"datetime-local",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.end_date]])])])])]),e("div",Q,[t[35]||(t[35]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Venue Information")],-1)),e("div",X,[e("div",Y,[e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Venue Name *",-1)),l(e("input",{"onUpdate:modelValue":t[6]||(t[6]=o=>s.venue_name=o),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.venue_name]])]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Capacity",-1)),l(e("input",{"onUpdate:modelValue":t[7]||(t[7]=o=>s.capacity=o),type:"number",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.capacity]])]),e("div",Z,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Address *",-1)),l(e("textarea",{"onUpdate:modelValue":t[8]||(t[8]=o=>s.address=o),rows:"3",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.address]])]),e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"City *",-1)),l(e("input",{"onUpdate:modelValue":t[9]||(t[9]=o=>s.city=o),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.city]])]),e("div",null,[t[34]||(t[34]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"State *",-1)),l(e("input",{"onUpdate:modelValue":t[10]||(t[10]=o=>s.state=o),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.state]])])])])]),e("div",ee,[e("div",{class:"px-6 py-4 border-b border-gray-200 flex justify-between items-center"},[t[36]||(t[36]=e("h3",{class:"text-lg font-medium text-gray-900"},"Ticket Types",-1)),e("button",{type:"button",onClick:g,class:"bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"}," Add Ticket Type ")]),e("div",te,[s.ticket_types.length===0?(u(),m("div",oe,[t[37]||(t[37]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})],-1)),t[38]||(t[38]=e("p",{class:"mt-2 text-sm text-gray-500"},"No ticket types added yet",-1)),e("button",{type:"button",onClick:g,class:"mt-4 bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"}," Add First Ticket Type ")])):(u(),m("div",se,[(u(!0),m(h,null,L(s.ticket_types,(o,f)=>(u(),m("div",{key:f,class:"border border-gray-200 rounded-lg p-4"},[e("div",le,[e("h4",ne,"Ticket Type "+a(f+1),1),e("button",{type:"button",onClick:i=>_(f),class:"text-red-600 hover:text-red-900"},t[39]||(t[39]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,re)]),e("div",ie,[e("div",null,[t[40]||(t[40]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Name *",-1)),l(e("input",{"onUpdate:modelValue":i=>o.name=i,type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,8,de),[[n,o.name]])]),e("div",null,[t[41]||(t[41]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Price (₹) *",-1)),l(e("input",{"onUpdate:modelValue":i=>o.price=i,type:"number",min:"0",step:"0.01",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,8,ae),[[n,o.price]])]),e("div",null,[t[42]||(t[42]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Quantity *",-1)),l(e("input",{"onUpdate:modelValue":i=>o.quantity=i,type:"number",min:"1",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,8,ue),[[n,o.quantity]])]),e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Sale Start",-1)),l(e("input",{"onUpdate:modelValue":i=>o.sale_start=i,type:"datetime-local",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,8,me),[[n,o.sale_start]])])]),e("div",pe,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Description",-1)),l(e("textarea",{"onUpdate:modelValue":i=>o.description=i,rows:"2",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,8,ge),[[n,o.description]])])]))),128))]))])]),e("div",fe,[t[52]||(t[52]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Media & Images")],-1)),e("div",ye,[e("div",null,[t[49]||(t[49]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Event Banner",-1)),e("div",be,[e("div",ve,[t[47]||(t[47]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48"},[e("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),e("div",ce,[e("label",xe,[t[45]||(t[45]=e("span",null,"Upload a file",-1)),e("input",{type:"file",class:"sr-only",accept:"image/*",onChange:V},null,32)]),t[46]||(t[46]=e("p",{class:"pl-1"},"or drag and drop",-1))]),t[48]||(t[48]=e("p",{class:"text-xs text-gray-500"},"PNG, JPG, GIF up to 10MB",-1))])])]),e("div",we,[e("div",null,[t[50]||(t[50]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Event Website",-1)),l(e("input",{"onUpdate:modelValue":t[11]||(t[11]=o=>s.website=o),type:"url",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.website]])]),e("div",null,[t[51]||(t[51]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Contact Email",-1)),l(e("input",{"onUpdate:modelValue":t[12]||(t[12]=o=>s.contact_email=o),type:"email",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.contact_email]])])])])]),e("div",ke,[t[56]||(t[56]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"SEO & Meta Information")],-1)),e("div",_e,[e("div",null,[t[53]||(t[53]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"URL Slug",-1)),l(e("input",{"onUpdate:modelValue":t[13]||(t[13]=o=>s.slug=o),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.slug]]),e("p",Ve,"URL: /events/"+a(s.slug||"event-slug"),1)]),e("div",null,[t[54]||(t[54]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Meta Title",-1)),l(e("input",{"onUpdate:modelValue":t[14]||(t[14]=o=>s.meta_title=o),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.meta_title]])]),e("div",null,[t[55]||(t[55]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Meta Description",-1)),l(e("textarea",{"onUpdate:modelValue":t[15]||(t[15]=o=>s.meta_description=o),rows:"3",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[n,s.meta_description]])])])]),e("div",Ue,[t[61]||(t[61]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Event Settings")],-1)),e("div",Ee,[e("div",Ce,[e("div",Te,[l(e("input",{"onUpdate:modelValue":t[16]||(t[16]=o=>s.is_featured=o),type:"checkbox",class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[p,s.is_featured]]),t[57]||(t[57]=e("label",{class:"ml-2 text-sm text-gray-700"},"Featured Event",-1))]),e("div",qe,[l(e("input",{"onUpdate:modelValue":t[17]||(t[17]=o=>s.allow_cancellation=o),type:"checkbox",class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[p,s.allow_cancellation]]),t[58]||(t[58]=e("label",{class:"ml-2 text-sm text-gray-700"},"Allow Ticket Cancellation",-1))]),e("div",Se,[l(e("input",{"onUpdate:modelValue":t[18]||(t[18]=o=>s.send_reminders=o),type:"checkbox",class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[p,s.send_reminders]]),t[59]||(t[59]=e("label",{class:"ml-2 text-sm text-gray-700"},"Send Event Reminders",-1))]),e("div",De,[l(e("input",{"onUpdate:modelValue":t[19]||(t[19]=o=>s.require_approval=o),type:"checkbox",class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[p,s.require_approval]]),t[60]||(t[60]=e("label",{class:"ml-2 text-sm text-gray-700"},"Require Booking Approval",-1))])])])])],32)])]),_:1})}}};export{Le as default};
