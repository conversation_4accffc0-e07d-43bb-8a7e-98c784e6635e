<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\TicketType;
use App\Models\Event;
use Illuminate\Support\Facades\Validator;

class TicketTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = TicketType::with(['event']);

        // Filter by event if provided
        if ($request->has('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        // Apply filters
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $ticketTypes = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $ticketTypes
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|exists:events,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'quantity_available' => 'required|integer|min:1',
            'min_quantity_per_order' => 'required|integer|min:1',
            'max_quantity_per_order' => 'required|integer|min:1',
            'sale_start_date' => 'nullable|date',
            'sale_end_date' => 'nullable|date|after:sale_start_date',
            'is_active' => 'boolean',
            'is_hidden' => 'boolean',
            'benefits' => 'nullable|array',
            'sort_order' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $ticketType = TicketType::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Ticket type created successfully',
            'data' => $ticketType->load('event')
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(TicketType $ticketType)
    {
        $ticketType->load(['event', 'tickets']);

        return response()->json([
            'success' => true,
            'data' => $ticketType
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TicketType $ticketType)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'quantity_available' => 'required|integer|min:1',
            'min_quantity_per_order' => 'required|integer|min:1',
            'max_quantity_per_order' => 'required|integer|min:1',
            'sale_start_date' => 'nullable|date',
            'sale_end_date' => 'nullable|date|after:sale_start_date',
            'is_active' => 'boolean',
            'is_hidden' => 'boolean',
            'benefits' => 'nullable|array',
            'sort_order' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $ticketType->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Ticket type updated successfully',
            'data' => $ticketType->load('event')
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TicketType $ticketType)
    {
        // Check if ticket type has sold tickets
        if ($ticketType->quantity_sold > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete ticket type with sold tickets'
            ], 422);
        }

        $ticketType->delete();

        return response()->json([
            'success' => true,
            'message' => 'Ticket type deleted successfully'
        ]);
    }

    /**
     * Get ticket types for a specific event
     */
    public function byEvent(Event $event)
    {
        $ticketTypes = $event->ticketTypes()
                           ->active()
                           ->visible()
                           ->onSale()
                           ->ordered()
                           ->get();

        return response()->json([
            'success' => true,
            'data' => $ticketTypes
        ]);
    }
}
