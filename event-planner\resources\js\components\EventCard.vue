<template>
  <div class="bg-gray-800 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300 hover:shadow-xl hover:shadow-pink-500/20 group">
    <!-- Event Image -->
    <div class="relative h-48 overflow-hidden">
      <img v-if="event.featured_image" 
           :src="event.featured_image" 
           :alt="event.title"
           class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300">
      <div v-else class="w-full h-full bg-gradient-to-br from-pink-500 to-cyan-500 flex items-center justify-center">
        <span class="text-white text-3xl font-bold">{{ event.title.charAt(0) }}</span>
      </div>
      
      <!-- Overlay -->
      <div class="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300"></div>
      
      <!-- Category Badge -->
      <div class="absolute top-4 left-4">
        <span class="bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm"
              :style="{ backgroundColor: event.category?.color_code + '80' }">
          {{ event.category?.name }}
        </span>
      </div>
      
      <!-- Featured Badge -->
      <div v-if="event.is_featured" class="absolute top-4 right-4">
        <span class="bg-gradient-to-r from-pink-500 to-cyan-500 text-white px-3 py-1 rounded-full text-sm font-bold">
          ⭐ Featured
        </span>
      </div>

      <!-- Quick Actions Overlay -->
      <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <div class="flex space-x-3">
          <router-link :to="`/events/${event.slug}`" 
                       class="bg-white/20 backdrop-blur-sm text-white p-3 rounded-full hover:bg-white/30 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
          </router-link>
          <button @click="quickBook" 
                  class="bg-gradient-to-r from-pink-500 to-cyan-500 text-white p-3 rounded-full hover:shadow-lg hover:shadow-pink-500/50 transition-all">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Availability Indicator -->
      <div class="absolute bottom-4 right-4">
        <div v-if="availabilityStatus === 'available'" class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
          Available
        </div>
        <div v-else-if="availabilityStatus === 'limited'" class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
          Limited
        </div>
        <div v-else-if="availabilityStatus === 'sold_out'" class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
          Sold Out
        </div>
      </div>
    </div>

    <!-- Event Details -->
    <div class="p-6">
      <h3 class="text-xl font-bold text-white mb-2 line-clamp-2 group-hover:text-pink-400 transition-colors">
        {{ event.title }}
      </h3>
      <p class="text-gray-400 mb-4 line-clamp-2 text-sm">
        {{ event.short_description }}
      </p>
      
      <!-- Event Info -->
      <div class="space-y-2 mb-6">
        <div class="flex items-center text-gray-300">
          <svg class="w-4 h-4 mr-2 text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <span class="text-sm">{{ formatDate(event.start_date) }}</span>
        </div>
        <div class="flex items-center text-gray-300">
          <svg class="w-4 h-4 mr-2 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          <span class="text-sm truncate">{{ event.venue_name }}</span>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center text-gray-300">
            <svg class="w-4 h-4 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
            <span class="text-sm font-semibold">₹{{ getMinPrice() }}</span>
          </div>
          <div class="text-xs text-gray-500">
            {{ getTicketsSold() }} sold
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-3">
        <router-link :to="`/events/${event.slug}`" 
                     class="flex-1 bg-gradient-to-r from-pink-500 to-cyan-500 text-white text-center py-2 px-4 rounded-lg font-semibold hover:shadow-lg hover:shadow-pink-500/25 transition-all text-sm">
          View Details
        </router-link>
        <button @click="quickBook" 
                :disabled="availabilityStatus === 'sold_out'"
                class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-500 transition-colors text-sm disabled:opacity-50 disabled:cursor-not-allowed">
          {{ availabilityStatus === 'sold_out' ? 'Sold Out' : 'Book Now' }}
        </button>
      </div>

      <!-- Social Proof -->
      <div v-if="event.rating || event.reviews_count" class="flex items-center justify-between mt-4 pt-4 border-t border-gray-700">
        <div class="flex items-center">
          <div class="flex text-yellow-400">
            <svg v-for="i in 5" :key="i" 
                 :class="i <= (event.rating || 0) ? 'text-yellow-400' : 'text-gray-600'"
                 class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          </div>
          <span class="text-sm text-gray-400 ml-2">{{ event.rating || 0 }}/5</span>
        </div>
        <span class="text-xs text-gray-500">{{ event.reviews_count || 0 }} reviews</span>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'EventCard',
  props: {
    event: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = date.getTime() - now.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays === 0) return 'Today'
      if (diffDays === 1) return 'Tomorrow'
      if (diffDays < 7) return `In ${diffDays} days`

      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const getMinPrice = () => {
      if (!props.event.ticket_types || props.event.ticket_types.length === 0) return '0'
      return Math.min(...props.event.ticket_types.map(t => t.price)).toLocaleString()
    }

    const getTicketsSold = () => {
      if (!props.event.ticket_types) return 0
      return props.event.ticket_types.reduce((total, type) => total + (type.quantity_sold || 0), 0)
    }

    const availabilityStatus = computed(() => {
      if (!props.event.ticket_types || props.event.ticket_types.length === 0) return 'sold_out'
      
      const totalAvailable = props.event.ticket_types.reduce((total, type) => total + (type.quantity_available || 0), 0)
      const totalSold = getTicketsSold()
      const remaining = totalAvailable - totalSold
      
      if (remaining <= 0) return 'sold_out'
      if (remaining <= totalAvailable * 0.2) return 'limited'
      return 'available'
    })

    const quickBook = () => {
      if (availabilityStatus.value === 'sold_out') return
      router.push(`/booking/${props.event.id}`)
    }

    return {
      formatDate,
      getMinPrice,
      getTicketsSold,
      availabilityStatus,
      quickBook
    }
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
