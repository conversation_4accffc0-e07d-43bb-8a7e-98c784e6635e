var Tn=Object.defineProperty;var An=(i,t,e)=>t in i?Tn(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var M=(i,t,e)=>An(i,typeof t!="symbol"?t+"":t,e);import"./vue-vendor-BupLktX_.js";/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka <PERSON>
 * Released under the MIT License
 */function ee(i){return i+.5|0}const ct=(i,t,e)=>Math.max(Math.min(i,e),t);function jt(i){return ct(ee(i*2.55),0,255)}function ft(i){return ct(ee(i*255),0,255)}function lt(i){return ct(ee(i/2.55)/100,0,1)}function wi(i){return ct(ee(i*100),0,100)}const X={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Je=[..."0123456789ABCDEF"],Ln=i=>Je[i&15],In=i=>Je[(i&240)>>4]+Je[i&15],oe=i=>(i&240)>>4===(i&15),Fn=i=>oe(i.r)&&oe(i.g)&&oe(i.b)&&oe(i.a);function Rn(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&X[i[1]]*17,g:255&X[i[2]]*17,b:255&X[i[3]]*17,a:t===5?X[i[4]]*17:255}:(t===7||t===9)&&(e={r:X[i[1]]<<4|X[i[2]],g:X[i[3]]<<4|X[i[4]],b:X[i[5]]<<4|X[i[6]],a:t===9?X[i[7]]<<4|X[i[8]]:255})),e}const zn=(i,t)=>i<255?t(i):"";function En(i){var t=Fn(i)?Ln:In;return i?"#"+t(i.r)+t(i.g)+t(i.b)+zn(i.a,t):void 0}const Bn=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Hs(i,t,e){const s=t*Math.min(e,1-e),n=(o,r=(o+i/30)%12)=>e-s*Math.max(Math.min(r-3,9-r,1),-1);return[n(0),n(8),n(4)]}function Hn(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function Wn(i,t,e){const s=Hs(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function Nn(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function ci(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),r=Math.min(e,s,n),a=(o+r)/2;let l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=Nn(e,s,n,h,o),l=l*60+.5),[l|0,c||0,a]}function hi(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(ft)}function di(i,t,e){return hi(Hs,i,t,e)}function Vn(i,t,e){return hi(Wn,i,t,e)}function jn(i,t,e){return hi(Hn,i,t,e)}function Ws(i){return(i%360+360)%360}function $n(i){const t=Bn.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?jt(+t[5]):ft(+t[5]));const n=Ws(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=Vn(n,o,r):t[1]==="hsv"?s=jn(n,o,r):s=di(n,o,r),{r:s[0],g:s[1],b:s[2],a:e}}function Yn(i,t){var e=ci(i);e[0]=Ws(e[0]+t),e=di(e),i.r=e[0],i.g=e[1],i.b=e[2]}function Un(i){if(!i)return;const t=ci(i),e=t[0],s=wi(t[1]),n=wi(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${lt(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const ki={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Si={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function Xn(){const i={},t=Object.keys(Si),e=Object.keys(ki);let s,n,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],n=0;n<e.length;n++)o=e[n],a=a.replace(o,ki[o]);o=parseInt(Si[r],16),i[a]=[o>>16&255,o>>8&255,o&255]}return i}let re;function Kn(i){re||(re=Xn(),re.transparent=[0,0,0,0]);const t=re[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const qn=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Gn(i){const t=qn.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const r=+t[7];e=t[8]?jt(r):ct(r*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?jt(s):ct(s,0,255)),n=255&(t[4]?jt(n):ct(n,0,255)),o=255&(t[6]?jt(o):ct(o,0,255)),{r:s,g:n,b:o,a:e}}}function Zn(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${lt(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const Be=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,Ct=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function Qn(i,t,e){const s=Ct(lt(i.r)),n=Ct(lt(i.g)),o=Ct(lt(i.b));return{r:ft(Be(s+e*(Ct(lt(t.r))-s))),g:ft(Be(n+e*(Ct(lt(t.g))-n))),b:ft(Be(o+e*(Ct(lt(t.b))-o))),a:i.a+e*(t.a-i.a)}}function ae(i,t,e){if(i){let s=ci(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=di(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function Ns(i,t){return i&&Object.assign(t||{},i)}function Mi(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=ft(i[3]))):(t=Ns(i,{r:0,g:0,b:0,a:1}),t.a=ft(t.a)),t}function Jn(i){return i.charAt(0)==="r"?Gn(i):$n(i)}class Gt{constructor(t){if(t instanceof Gt)return t;const e=typeof t;let s;e==="object"?s=Mi(t):e==="string"&&(s=Rn(t)||Kn(t)||Jn(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=Ns(this._rgb);return t&&(t.a=lt(t.a)),t}set rgb(t){this._rgb=Mi(t)}rgbString(){return this._valid?Zn(this._rgb):void 0}hexString(){return this._valid?En(this._rgb):void 0}hslString(){return this._valid?Un(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const r=e===o?.5:e,a=2*r-1,l=s.a-n.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=r*s.a+(1-r)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=Qn(this._rgb,t._rgb,e)),this}clone(){return new Gt(this.rgb)}alpha(t){return this._rgb.a=ft(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=ee(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return ae(this._rgb,2,t),this}darken(t){return ae(this._rgb,2,-t),this}saturate(t){return ae(this._rgb,1,t),this}desaturate(t){return ae(this._rgb,1,-t),this}rotate(t){return Yn(this._rgb,t),this}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function ot(){}const to=(()=>{let i=0;return()=>i++})();function F(i){return i==null}function E(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function O(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function V(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function tt(i,t){return V(i)?i:t}function D(i,t){return typeof i>"u"?t:i}const eo=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function I(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function A(i,t,e,s){let n,o,r;if(E(i))for(o=i.length,n=0;n<o;n++)t.call(e,i[n],n);else if(O(i))for(r=Object.keys(i),o=r.length,n=0;n<o;n++)t.call(e,i[r[n]],r[n])}function we(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function ke(i){if(E(i))return i.map(ke);if(O(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=ke(i[e[n]]);return t}return i}function Vs(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function io(i,t,e,s){if(!Vs(i))return;const n=t[i],o=e[i];O(n)&&O(o)?Zt(n,o,s):t[i]=ke(o)}function Zt(i,t,e){const s=E(t)?t:[t],n=s.length;if(!O(i))return i;e=e||{};const o=e.merger||io;let r;for(let a=0;a<n;++a){if(r=s[a],!O(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],i,r,e)}return i}function Ut(i,t){return Zt(i,t,{merger:so})}function so(i,t,e){if(!Vs(i))return;const s=t[i],n=e[i];O(s)&&O(n)?Ut(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=ke(n))}const Pi={"":i=>i,x:i=>i.x,y:i=>i.y};function no(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function oo(i){const t=no(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function Se(i,t){return(Pi[t]||(Pi[t]=oo(t)))(i)}function fi(i){return i.charAt(0).toUpperCase()+i.slice(1)}const Me=i=>typeof i<"u",ut=i=>typeof i=="function",Di=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function ro(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const B=Math.PI,Q=2*B,ao=Q+B,Pe=Number.POSITIVE_INFINITY,lo=B/180,G=B/2,mt=B/4,Oi=B*2/3,js=Math.log10,Lt=Math.sign;function Xt(i,t,e){return Math.abs(i-t)<e}function Ci(i){const t=Math.round(i);i=Xt(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(js(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function co(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function ho(i){return typeof i=="symbol"||typeof i=="object"&&i!==null&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}function De(i){return!ho(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function fo(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function uo(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function wt(i){return i*(B/180)}function go(i){return i*(180/B)}function Ti(i){if(!V(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function po(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*B&&(o+=Q),{angle:o,distance:n}}function ti(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function mo(i,t){return(i-t+ao)%Q-B}function st(i){return(i%Q+Q)%Q}function $s(i,t,e,s){const n=st(i),o=st(t),r=st(e),a=st(o-n),l=st(r-n),c=st(n-o),h=st(n-r);return n===o||n===r||s&&o===r||a>l&&c<h}function Z(i,t,e){return Math.max(t,Math.min(e,i))}function bo(i){return Z(i,-32768,32767)}function ht(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function ui(i,t,e){e=e||(r=>i[r]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const ei=(i,t,e,s)=>ui(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),_o=(i,t,e)=>ui(i,e,s=>i[s][t]>=e);function xo(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const Ys=["push","pop","shift","splice","unshift"];function yo(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Ys.forEach(e=>{const s="_onData"+fi(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const r=n.apply(this,o);return i._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function Ai(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(Ys.forEach(o=>{delete i[o]}),delete i._chartjs)}function vo(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const Us=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function Xs(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,Us.call(window,()=>{s=!1,i.apply(t,e)}))}}function wo(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const gi=i=>i==="start"?"left":i==="end"?"right":"center",W=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,ko=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t,le=i=>i===0||i===1,Li=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*Q/e)),Ii=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*Q/e)+1,Kt={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*G)+1,easeOutSine:i=>Math.sin(i*G),easeInOutSine:i=>-.5*(Math.cos(B*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>le(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>le(i)?i:Li(i,.075,.3),easeOutElastic:i=>le(i)?i:Ii(i,.075,.3),easeInOutElastic(i){return le(i)?i:i<.5?.5*Li(i*2,.1125,.45):.5+.5*Ii(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-Kt.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?Kt.easeInBounce(i*2)*.5:Kt.easeOutBounce(i*2-1)*.5+.5};function pi(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Fi(i){return pi(i)?i:new Gt(i)}function He(i){return pi(i)?i:new Gt(i).saturate(.5).darken(.1).hexString()}const So=["x","y","borderWidth","radius","tension"],Mo=["color","borderColor","backgroundColor"];function Po(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:Mo},numbers:{type:"number",properties:So}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Do(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Ri=new Map;function Oo(i,t){t=t||{};const e=i+JSON.stringify(t);let s=Ri.get(e);return s||(s=new Intl.NumberFormat(i,t),Ri.set(e,s)),s}function Ks(i,t,e){return Oo(t,e).format(i)}const Co={values(i){return E(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=To(i,e)}const r=js(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),Ks(i,s,l)}};function To(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var qs={formatters:Co};function Ao(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:qs.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const Mt=Object.create(null),ii=Object.create(null);function qt(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function We(i,t,e){return typeof t=="string"?Zt(qt(i,t),e):Zt(qt(i,""),t)}class Lo{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>He(n.backgroundColor),this.hoverBorderColor=(s,n)=>He(n.borderColor),this.hoverColor=(s,n)=>He(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return We(this,t,e)}get(t){return qt(this,t)}describe(t,e){return We(ii,t,e)}override(t,e){return We(Mt,t,e)}route(t,e,s,n){const o=qt(this,t),r=qt(this,s),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=r[n];return O(l)?Object.assign({},c,l):D(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var z=new Lo({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Po,Do,Ao]);function Io(i){return!i||F(i.size)||F(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function zi(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function bt(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function Ei(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function si(i,t,e,s){Gs(i,t,e,s,null)}function Gs(i,t,e,s,n){let o,r,a,l,c,h,d,f;const u=t.pointStyle,m=t.rotation,g=t.radius;let p=(m||0)*lo;if(u&&typeof u=="object"&&(o=u.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(p),i.drawImage(u,-u.width/2,-u.height/2,u.width,u.height),i.restore();return}if(!(isNaN(g)||g<=0)){switch(i.beginPath(),u){default:n?i.ellipse(e,s,n/2,g,0,0,Q):i.arc(e,s,g,0,Q),i.closePath();break;case"triangle":h=n?n/2:g,i.moveTo(e+Math.sin(p)*h,s-Math.cos(p)*g),p+=Oi,i.lineTo(e+Math.sin(p)*h,s-Math.cos(p)*g),p+=Oi,i.lineTo(e+Math.sin(p)*h,s-Math.cos(p)*g),i.closePath();break;case"rectRounded":c=g*.516,l=g-c,r=Math.cos(p+mt)*l,d=Math.cos(p+mt)*(n?n/2-c:l),a=Math.sin(p+mt)*l,f=Math.sin(p+mt)*(n?n/2-c:l),i.arc(e-d,s-a,c,p-B,p-G),i.arc(e+f,s-r,c,p-G,p),i.arc(e+d,s+a,c,p,p+G),i.arc(e-f,s+r,c,p+G,p+B),i.closePath();break;case"rect":if(!m){l=Math.SQRT1_2*g,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}p+=mt;case"rectRot":d=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,f=Math.sin(p)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+f,s-r),i.lineTo(e+d,s+a),i.lineTo(e-f,s+r),i.closePath();break;case"crossRot":p+=mt;case"cross":d=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,f=Math.sin(p)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r);break;case"star":d=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,f=Math.sin(p)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r),p+=mt,d=Math.cos(p)*(n?n/2:g),r=Math.cos(p)*g,a=Math.sin(p)*g,f=Math.sin(p)*(n?n/2:g),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r);break;case"line":r=n?n/2:Math.cos(p)*g,a=Math.sin(p)*g,i.moveTo(e-r,s-a),i.lineTo(e+r,s+a);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(p)*(n?n/2:g),s+Math.sin(p)*g);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function Qt(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function Le(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function Ie(i){i.restore()}function Fo(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){const o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function Ro(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function zo(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),F(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function Eo(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(r,h),i.lineTo(a,h),i.stroke()}}function Bo(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Jt(i,t,e,s,n,o={}){const r=E(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,zo(i,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&Bo(i,o.backdrop),a&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),F(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),Eo(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function Oe(i,t){const{x:e,y:s,w:n,h:o,radius:r}=t;i.arc(e+r.topLeft,s+r.topLeft,r.topLeft,1.5*B,B,!0),i.lineTo(e,s+o-r.bottomLeft),i.arc(e+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,B,G,!0),i.lineTo(e+n-r.bottomRight,s+o),i.arc(e+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,G,0,!0),i.lineTo(e+n,s+r.topRight),i.arc(e+n-r.topRight,s+r.topRight,r.topRight,0,-G,!0),i.lineTo(e+r.topLeft,s)}const Ho=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Wo=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function No(i,t){const e=(""+i).match(Ho);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const Vo=i=>+i||0;function Zs(i,t){const e={},s=O(t),n=s?Object.keys(t):t,o=O(i)?s?r=>D(i[r],i[t[r]]):r=>i[r]:()=>i;for(const r of n)e[r]=Vo(o(r));return e}function Qs(i){return Zs(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Tt(i){return Zs(i,["topLeft","topRight","bottomLeft","bottomRight"])}function q(i){const t=Qs(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function N(i,t){i=i||{},t=t||z.font;let e=D(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=D(i.style,t.style);s&&!(""+s).match(Wo)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:D(i.family,t.family),lineHeight:No(D(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:D(i.weight,t.weight),string:""};return n.string=Io(n),n}function ce(i,t,e,s){let n,o,r;for(n=0,o=i.length;n<o;++n)if(r=i[n],r!==void 0&&r!==void 0)return r}function jo(i,t,e){const{min:s,max:n}=i,o=eo(t,(n-s)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function Pt(i,t){return Object.assign(Object.create(i),t)}function mi(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=sn("_fallback",i));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:a=>mi([a,...i],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete i[0][l],!0},get(a,l){return tn(a,l,()=>Zo(l,t,i,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(a,l){return Hi(a).includes(l)},ownKeys(a){return Hi(a)},set(a,l,c){const h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function It(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:Js(i,s),setContext:o=>It(i,o,e,s),override:o=>It(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete i[r],!0},get(o,r,a){return tn(o,r,()=>Yo(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,r){return Reflect.has(i,r)},ownKeys(){return Reflect.ownKeys(i)},set(o,r,a){return i[r]=a,delete o[r],!0}})}function Js(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:ut(e)?e:()=>e,isIndexable:ut(s)?s:()=>s}}const $o=(i,t)=>i?i+fi(t):t,bi=(i,t)=>O(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function tn(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];const s=e();return i[t]=s,s}function Yo(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=i;let a=s[t];return ut(a)&&r.isScriptable(t)&&(a=Uo(t,a,i,e)),E(a)&&a.length&&(a=Xo(t,a,i,r.isIndexable)),bi(t,a)&&(a=It(a,n,o&&o[t],r)),a}function Uo(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(i))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+i);a.add(i);let l=t(o,r||s);return a.delete(i),bi(i,l)&&(l=_i(n._scopes,n,i,l)),l}function Xo(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(O(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=_i(c,n,i,h);t.push(It(d,o,r&&r[i],a))}}return t}function en(i,t,e){return ut(i)?i(t,e):i}const Ko=(i,t)=>i===!0?t:typeof i=="string"?Se(t,i):void 0;function qo(i,t,e,s,n){for(const o of t){const r=Ko(e,o);if(r){i.add(r);const a=en(r._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==s)return a}else if(r===!1&&typeof s<"u"&&e!==s)return null}return!1}function _i(i,t,e,s){const n=t._rootScopes,o=en(t._fallback,e,s),r=[...i,...n],a=new Set;a.add(s);let l=Bi(a,r,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=Bi(a,r,o,l,s),l===null)?!1:mi(Array.from(a),[""],n,o,()=>Go(t,e,s))}function Bi(i,t,e,s,n){for(;e;)e=qo(i,t,e,s,n);return e}function Go(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return E(n)&&O(e)?e:n||{}}function Zo(i,t,e,s){let n;for(const o of t)if(n=sn($o(o,i),e),typeof n<"u")return bi(i,n)?_i(e,s,i,n):n}function sn(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function Hi(i){let t=i._keys;return t||(t=i._keys=Qo(i._scopes)),t}function Qo(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}const Jo=Number.EPSILON||1e-14,Ft=(i,t)=>t<i.length&&!i[t].skip&&i[t],nn=i=>i==="x"?"y":"x";function tr(i,t,e,s){const n=i.skip?t:i,o=t,r=e.skip?t:e,a=ti(o,n),l=ti(r,o);let c=a/(a+l),h=l/(a+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=s*c,f=s*h;return{previous:{x:o.x-d*(r.x-n.x),y:o.y-d*(r.y-n.y)},next:{x:o.x+f*(r.x-n.x),y:o.y+f*(r.y-n.y)}}}function er(i,t,e){const s=i.length;let n,o,r,a,l,c=Ft(i,0);for(let h=0;h<s-1;++h)if(l=c,c=Ft(i,h+1),!(!l||!c)){if(Xt(t[h],0,Jo)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],a=Math.pow(n,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),e[h]=n*r*t[h],e[h+1]=o*r*t[h])}}function ir(i,t,e="x"){const s=nn(e),n=i.length;let o,r,a,l=Ft(i,0);for(let c=0;c<n;++c){if(r=a,a=l,l=Ft(i,c+1),!a)continue;const h=a[e],d=a[s];r&&(o=(h-r[e])/3,a[`cp1${e}`]=h-o,a[`cp1${s}`]=d-o*t[c]),l&&(o=(l[e]-h)/3,a[`cp2${e}`]=h+o,a[`cp2${s}`]=d+o*t[c])}}function sr(i,t="x"){const e=nn(t),s=i.length,n=Array(s).fill(0),o=Array(s);let r,a,l,c=Ft(i,0);for(r=0;r<s;++r)if(a=l,l=c,c=Ft(i,r+1),!!l){if(c){const h=c[t]-l[t];n[r]=h!==0?(c[e]-l[e])/h:0}o[r]=a?c?Lt(n[r-1])!==Lt(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}er(i,n,o),ir(i,o,t)}function he(i,t,e){return Math.max(Math.min(i,e),t)}function nr(i,t){let e,s,n,o,r,a=Qt(i[0],t);for(e=0,s=i.length;e<s;++e)r=o,o=a,a=e<s-1&&Qt(i[e+1],t),o&&(n=i[e],r&&(n.cp1x=he(n.cp1x,t.left,t.right),n.cp1y=he(n.cp1y,t.top,t.bottom)),a&&(n.cp2x=he(n.cp2x,t.left,t.right),n.cp2y=he(n.cp2y,t.top,t.bottom)))}function or(i,t,e,s,n){let o,r,a,l;if(t.spanGaps&&(i=i.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")sr(i,n);else{let c=s?i[i.length-1]:i[0];for(o=0,r=i.length;o<r;++o)a=i[o],l=tr(c,a,i[Math.min(o+1,r-(s?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&nr(i,e)}function xi(){return typeof window<"u"&&typeof document<"u"}function yi(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Ce(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const Fe=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function rr(i,t){return Fe(i).getPropertyValue(t)}const ar=["top","right","bottom","left"];function St(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=ar[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const lr=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function cr(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let r=!1,a,l;if(lr(n,o,i.target))a=n,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function xt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=Fe(e),o=n.boxSizing==="border-box",r=St(n,"padding"),a=St(n,"border","width"),{x:l,y:c,box:h}=cr(i,e),d=r.left+(h&&a.left),f=r.top+(h&&a.top);let{width:u,height:m}=t;return o&&(u-=r.width+a.width,m-=r.height+a.height),{x:Math.round((l-d)/u*e.width/s),y:Math.round((c-f)/m*e.height/s)}}function hr(i,t,e){let s,n;if(t===void 0||e===void 0){const o=i&&yi(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const r=o.getBoundingClientRect(),a=Fe(o),l=St(a,"border","width"),c=St(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,s=Ce(a.maxWidth,o,"clientWidth"),n=Ce(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||Pe,maxHeight:n||Pe}}const de=i=>Math.round(i*10)/10;function dr(i,t,e,s){const n=Fe(i),o=St(n,"margin"),r=Ce(n.maxWidth,i,"clientWidth")||Pe,a=Ce(n.maxHeight,i,"clientHeight")||Pe,l=hr(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const f=St(n,"border","width"),u=St(n,"padding");c-=u.width+f.width,h-=u.height+f.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=de(Math.min(c,r,l.maxWidth)),h=de(Math.min(h,a,l.maxHeight)),c&&!h&&(h=de(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=de(Math.floor(h*s))),{width:c,height:h}}function Wi(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(i.currentDevicePixelRatio=s,r.height=n,r.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const fr=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};xi()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function Ni(i,t){const e=rr(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function yt(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function ur(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function gr(i,t,e,s){const n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},r=yt(i,n,e),a=yt(n,o,e),l=yt(o,t,e),c=yt(r,a,e),h=yt(a,l,e);return yt(c,h,e)}const pr=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},mr=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function At(i,t,e){return i?pr(t,e):mr()}function on(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function rn(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function an(i){return i==="angle"?{between:$s,compare:mo,normalize:st}:{between:ht,compare:(t,e)=>t-e,normalize:t=>t}}function Vi({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function br(i,t,e){const{property:s,start:n,end:o}=e,{between:r,normalize:a}=an(s),l=t.length;let{start:c,end:h,loop:d}=i,f,u;if(d){for(c+=l,h+=l,f=0,u=l;f<u&&r(a(t[c%l][s]),n,o);++f)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:i.style}}function ln(i,t,e){if(!e)return[i];const{property:s,start:n,end:o}=e,r=t.length,{compare:a,between:l,normalize:c}=an(s),{start:h,end:d,loop:f,style:u}=br(i,t,e),m=[];let g=!1,p=null,b,_,y;const v=()=>l(n,y,b)&&a(n,y)!==0,x=()=>a(o,b)===0||l(o,y,b),k=()=>g||v(),S=()=>!g||x();for(let w=h,P=h;w<=d;++w)_=t[w%r],!_.skip&&(b=c(_[s]),b!==y&&(g=l(b,n,o),p===null&&k()&&(p=a(b,n)===0?w:P),p!==null&&S()&&(m.push(Vi({start:p,end:w,loop:f,count:r,style:u})),p=null),P=w,y=b));return p!==null&&m.push(Vi({start:p,end:d,loop:f,count:r,style:u})),m}function cn(i,t){const e=[],s=i.segments;for(let n=0;n<s.length;n++){const o=ln(s[n],i.points,t);o.length&&e.push(...o)}return e}function _r(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function xr(i,t,e,s){const n=i.length,o=[];let r=t,a=i[t],l;for(l=t+1;l<=e;++l){const c=i[l%n];c.skip||c.stop?a.skip||(s=!1,o.push({start:t%n,end:(l-1)%n,loop:s}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%n,end:r%n,loop:s}),o}function yr(i,t){const e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];const o=!!i._loop,{start:r,end:a}=_r(e,n,o,s);if(s===!0)return ji(i,[{start:r,end:a,loop:o}],e,t);const l=a<r?a+n:a,c=!!i._fullLoop&&r===0&&a===n-1;return ji(i,xr(e,r,l,c),e,t)}function ji(i,t,e,s){return!s||!s.setContext||!e?t:vr(i,t,e,s)}function vr(i,t,e,s){const n=i._chart.getContext(),o=$i(i.options),{_datasetIndex:r,options:{spanGaps:a}}=i,l=e.length,c=[];let h=o,d=t[0].start,f=d;function u(m,g,p,b){const _=a?-1:1;if(m!==g){for(m+=l;e[m%l].skip;)m-=_;for(;e[g%l].skip;)g+=_;m%l!==g%l&&(c.push({start:m%l,end:g%l,loop:p,style:b}),h=b,d=g%l)}}for(const m of t){d=a?d:m.start;let g=e[d%l],p;for(f=d+1;f<=m.end;f++){const b=e[f%l];p=$i(s.setContext(Pt(n,{type:"segment",p0:g,p1:b,p0DataIndex:(f-1)%l,p1DataIndex:f%l,datasetIndex:r}))),wr(p,h)&&u(d,f-1,m.loop,h),g=b,h=p}d<f-1&&u(d,f-1,m.loop,h)}return c}function $i(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function wr(i,t){if(!t)return!1;const e=[],s=function(n,o){return pi(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(t,s)}function fe(i,t,e){return i.options.clip?i[e]:t[e]}function kr(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:fe(e,t,"left"),right:fe(e,t,"right"),top:fe(s,t,"top"),bottom:fe(s,t,"bottom")}:t}function hn(i,t){const e=t._clip;if(e.disabled)return!1;const s=kr(t,i.chartArea);return{left:e.left===!1?0:s.left-(e.left===!0?0:e.left),right:e.right===!1?i.width:s.right+(e.right===!0?0:e.right),top:e.top===!1?0:s.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?i.height:s.bottom+(e.bottom===!0?0:e.bottom)}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Sr{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(s-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=Us.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var rt=new Sr;const Yi="transparent",Mr={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=Fi(i||Yi),n=s.valid&&Fi(t||Yi);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class Pr{constructor(t,e,s,n){const o=e[s];n=ce([t.to,n,o,t.from]);const r=ce([t.from,o,n]);this._active=!0,this._fn=t.fn||Mr[t.type||typeof r],this._easing=Kt[t.easing]||Kt.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=ce([t.to,e,n,t.from]),this._from=ce([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<s),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class dn{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!O(t))return;const e=Object.keys(z.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!O(o))return;const r={};for(const a of e)r[a]=o[a];(E(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,e){const s=e.options,n=Or(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&Dr(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let d=o[c];const f=s.get(c);if(d)if(f&&d.active()){d.update(f,h,a);continue}else d.cancel();if(!f||!f.duration){t[c]=h;continue}o[c]=d=new Pr(f,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return rt.add(this._chart,s),!0}}function Dr(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function Or(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Ui(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function Cr(i,t,e){if(e===!1)return!1;const s=Ui(i,e),n=Ui(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function Tr(i){let t,e,s,n;return O(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function fn(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Xi(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let r,a,l,c;if(t===null)return;let h=!1;for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(h=!0,s.all)continue;break}c=i.values[l],V(c)&&(o||t===0||Lt(t)===Lt(c))&&(t+=c)}return!h&&!s.all?0:t}function Ar(i,t){const{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(i),a=new Array(r.length);let l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[n]:h,[o]:i[h]};return a}function Ne(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function Lr(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function Ir(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function Fr(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function Ki(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function qi(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,h=Lr(o,r,s),d=t.length;let f;for(let u=0;u<d;++u){const m=t[u],{[l]:g,[c]:p}=m,b=m._stacks||(m._stacks={});f=b[c]=Fr(n,h,g),f[a]=p,f._top=Ki(f,r,!0,s.type),f._bottom=Ki(f,r,!1,s.type);const _=f._visualValues||(f._visualValues={});_[a]=p}}function Ve(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function Rr(i,t){return Pt(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function zr(i,t,e){return Pt(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function Bt(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const je=i=>i==="reset"||i==="none",Gi=(i,t)=>t?i:Object.assign({},i),Er=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:fn(e,!0),values:null};class xe{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Ne(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Bt(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,f,u,m)=>d==="x"?f:d==="r"?m:u,o=e.xAxisID=D(s.xAxisID,Ve(t,"x")),r=e.yAxisID=D(s.yAxisID,Ve(t,"y")),a=e.rAxisID=D(s.rAxisID,Ve(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,r,a),h=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&Ai(this._data,this),t._stacked&&Bt(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(O(e)){const n=this._cachedMeta;this._data=Ar(e,n)}else if(s!==e){if(s){Ai(s,this);const n=this._cachedMeta;Bt(n),n._parsed=[]}e&&Object.isExtensible(e)&&yo(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=Ne(e.vScale,e),e.stack!==s.stack&&(n=!0,Bt(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&(qi(this,e._parsed),e._stacked=Ne(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,f;if(this._parsing===!1)s._parsed=n,s._sorted=!0,f=n;else{E(n[t])?f=this.parseArrayData(s,n,t,e):O(n[t])?f=this.parseObjectData(s,n,t,e):f=this.parsePrimitiveData(s,n,t,e);const u=()=>d[a]===null||c&&d[a]<c[a];for(h=0;h<e;++h)s._parsed[h+t]=d=f[h],l&&(u()&&(l=!1),c=d);s._sorted=l}r&&qi(this,f)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,d=new Array(n);let f,u,m;for(f=0,u=n;f<u;++f)m=f+s,d[f]={[a]:h||o.parse(c[m],m),[l]:r.parse(e[m],m)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:r}=t,a=new Array(n);let l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],a[l]={x:o.parse(d[0],h),y:r.parse(d[1],h)};return a}parseObjectData(t,e,s,n){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,d,f,u;for(h=0,d=n;h<d;++h)f=h+s,u=e[f],c[h]={x:o.parse(Se(u,a),f),y:r.parse(Se(u,l),f)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:fn(n,!0),values:e._stacks[t.axis]._visualValues};return Xi(a,r,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let r=o===null?NaN:o;const a=n&&s._stacks[e.axis];n&&a&&(n.values=a,r=Xi(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,r=n.length,a=this._getOtherScale(t),l=Er(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=Ir(a);let f,u;function m(){u=n[f];const g=u[a.axis];return!V(u[t.axis])||h>g||d<g}for(f=0;f<r&&!(!m()&&(this.updateRangeFromParsed(c,t,u,l),o));++f);if(o){for(f=r-1;f>=0;--f)if(!m()){this.updateRangeFromParsed(c,t,u,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],V(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=Tr(D(this.options.clip,Cr(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const d=n[h];d.hidden||(d.active&&c?r.push(d):d.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=zr(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Rr(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&Me(s);if(a)return Gi(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],f=c.getOptionScopes(this.getDataset(),h),u=Object.keys(z.elements[t]),m=()=>this.getContext(s,n,e),g=c.resolveNamedOptions(f,u,m,d);return g.$shared&&(g.$shared=l,o[r]=Object.freeze(Gi(g,l))),g}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),f=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(f,this.getContext(t,s,e))}const c=new dn(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||je(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:r}}updateElement(t,e,s,n){je(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!je(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const n=s.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,r=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&Bt(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}M(xe,"defaults",{}),M(xe,"datasetElementType",null),M(xe,"dataElementType",null);function _t(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class vi{constructor(t){M(this,"options");this.options=t||{}}static override(t){Object.assign(vi.prototype,t)}init(){}formats(){return _t()}parse(){return _t()}format(){return _t()}add(){return _t()}diff(){return _t()}startOf(){return _t()}endOf(){return _t()}}var Br={_date:vi};function Hr(i,t,e,s){const{controller:n,data:o,_sorted:r}=i,a=n._cachedMeta.iScale,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?_o:ei;if(s){if(n._sharedOptions){const h=o[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){const f=c(o,t,e-d),u=c(o,t,e+d);return{lo:f.lo,hi:u.hi}}}}else{const h=c(o,t,e);if(l){const{vScale:d}=n._cachedMeta,{_parsed:f}=i,u=f.slice(0,h.lo+1).reverse().findIndex(g=>!F(g[d.axis]));h.lo-=Math.max(0,u);const m=f.slice(h.hi).findIndex(g=>!F(g[d.axis]));h.hi+=Math.max(0,m)}return h}}return{lo:0,hi:o.length-1}}function Re(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:d,hi:f}=Hr(o[a],t,r,n);for(let u=d;u<=f;++u){const m=h[u];m.skip||s(m,c,u)}}}function Wr(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,r=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function $e(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||Re(i,e,t,function(a,l,c){!n&&!Qt(a,i.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function Nr(i,t,e,s){let n=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],s),{angle:d}=po(r,{x:t.x,y:t.y});$s(d,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return Re(i,e,t,o),n}function Vr(i,t,e,s,n,o){let r=[];const a=Wr(e);let l=Number.POSITIVE_INFINITY;function c(h,d,f){const u=h.inRange(t.x,t.y,n);if(s&&!u)return;const m=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(m))&&!u)return;const p=a(t,m);p<l?(r=[{element:h,datasetIndex:d,index:f}],l=p):p===l&&r.push({element:h,datasetIndex:d,index:f})}return Re(i,e,t,c),r}function Ye(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?Nr(i,t,e,n):Vr(i,t,e,s,n,o)}function Zi(i,t,e,s,n){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return Re(i,e,t,(l,c,h)=>{l[r]&&l[r](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),s&&!a?[]:o}var jr={modes:{index(i,t,e,s){const n=xt(t,i),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?$e(i,n,o,s,r):Ye(i,n,o,!1,s,r),l=[];return a.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=xt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?$e(i,n,o,s,r):Ye(i,n,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=i.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(i,t,e,s){const n=xt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return $e(i,n,o,s,r)},nearest(i,t,e,s){const n=xt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Ye(i,n,o,e.intersect,s,r)},x(i,t,e,s){const n=xt(t,i);return Zi(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=xt(t,i);return Zi(i,n,"y",e.intersect,s)}}};const un=["left","top","right","bottom"];function Ht(i,t){return i.filter(e=>e.pos===t)}function Qi(i,t){return i.filter(e=>un.indexOf(e.pos)===-1&&e.box.axis===t)}function Wt(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function $r(i){const t=[];let e,s,n,o,r,a;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function Yr(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!un.includes(n))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function Ur(i,t){const e=Yr(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,r,a;for(o=0,r=i.length;o<r;++o){a=i[o];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*s:l&&t.availableWidth,a.height=n):(a.width=s,a.height=h?h*n:l&&t.availableHeight)}return e}function Xr(i){const t=$r(i),e=Wt(t.filter(c=>c.box.fullSize),!0),s=Wt(Ht(t,"left"),!0),n=Wt(Ht(t,"right")),o=Wt(Ht(t,"top"),!0),r=Wt(Ht(t,"bottom")),a=Qi(t,"x"),l=Qi(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:Ht(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function Ji(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function gn(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function Kr(i,t,e,s){const{pos:n,box:o}=e,r=i.maxPadding;if(!O(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&gn(r,o.getPadding());const a=Math.max(0,t.outerWidth-Ji(r,i,"left","right")),l=Math.max(0,t.outerHeight-Ji(r,i,"top","bottom")),c=a!==i.w,h=l!==i.h;return i.w=a,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function qr(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function Gr(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return s(i?["left","right"]:["top","bottom"])}function $t(i,t,e,s){const n=[];let o,r,a,l,c,h;for(o=0,r=i.length,c=0;o<r;++o){a=i[o],l=a.box,l.update(a.width||t.w,a.height||t.h,Gr(a.horizontal,t));const{same:d,other:f}=Kr(t,e,a,s);c|=d&&n.length,h=h||f,l.fullSize||n.push(a)}return c&&$t(n,t,e,s)||h}function ue(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function ts(i,t,e,s){const n=e.padding;let{x:o,y:r}=t;for(const a of i){const l=a.box,c=s[a.stack]||{placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const d=t.w*h,f=c.size||l.height;Me(c.start)&&(r=c.start),l.fullSize?ue(l,n.left,r,e.outerWidth-n.right-n.left,f):ue(l,t.left+c.placed,r,d,f),c.start=r,c.placed+=d,r=l.bottom}else{const d=t.h*h,f=c.size||l.width;Me(c.start)&&(o=c.start),l.fullSize?ue(l,o,n.top,f,e.outerHeight-n.bottom-n.top):ue(l,o,t.top+c.placed,f,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=r}var K={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=q(i.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=Xr(i.boxes),l=a.vertical,c=a.horizontal;A(i.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});const h=l.reduce((g,p)=>p.box.options&&p.box.options.display===!1?g:g+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),f=Object.assign({},n);gn(f,q(s));const u=Object.assign({maxPadding:f,w:o,h:r,x:n.left,y:n.top},n),m=Ur(l.concat(c),d);$t(a.fullSize,u,d,m),$t(l,u,d,m),$t(c,u,d,m)&&$t(l,u,d,m),qr(u),ts(a.leftAndTop,u,d,m),u.x+=u.w,u.y+=u.h,ts(a.rightAndBottom,u,d,m),i.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},A(a.chartArea,g=>{const p=g.box;Object.assign(p,i.chartArea),p.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class pn{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class Zr extends pn{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const ye="$chartjs",Qr={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},es=i=>i===null||i==="";function Jr(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[ye]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",es(n)){const o=Ni(i,"width");o!==void 0&&(i.width=o)}if(es(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=Ni(i,"height");o!==void 0&&(i.height=o)}return i}const mn=fr?{passive:!0}:!1;function ta(i,t,e){i&&i.addEventListener(t,e,mn)}function ea(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,mn)}function ia(i,t){const e=Qr[i.type]||i.type,{x:s,y:n}=xt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function Te(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function sa(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Te(a.addedNodes,s),r=r&&!Te(a.removedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function na(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Te(a.removedNodes,s),r=r&&!Te(a.addedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const te=new Map;let is=0;function bn(){const i=window.devicePixelRatio;i!==is&&(is=i,te.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function oa(i,t){te.size||window.addEventListener("resize",bn),te.set(i,t)}function ra(i){te.delete(i),te.size||window.removeEventListener("resize",bn)}function aa(i,t,e){const s=i.canvas,n=s&&yi(s);if(!n)return;const o=Xs((a,l)=>{const c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),oa(i,o),r}function Ue(i,t,e){e&&e.disconnect(),t==="resize"&&ra(i)}function la(i,t,e){const s=i.canvas,n=Xs(o=>{i.ctx!==null&&e(ia(o,i))},i);return ta(s,t,n),n}class ca extends pn{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(Jr(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[ye])return!1;const s=e[ye].initial;["height","width"].forEach(o=>{const r=s[o];F(r)?e.removeAttribute(o):e.setAttribute(o,r)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[ye],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:sa,detach:na,resize:aa}[e]||la;n[e]=r(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:Ue,detach:Ue,resize:Ue}[e]||ea)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return dr(t,e,s,n)}isAttached(t){const e=t&&yi(t);return!!(e&&e.isConnected)}}function ha(i){return!xi()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Zr:ca}class nt{constructor(){M(this,"x");M(this,"y");M(this,"active",!1);M(this,"options");M(this,"$animations")}tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return De(this.x)&&De(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}M(nt,"defaults",{}),M(nt,"defaultRoutes");function da(i,t){const e=i.options.ticks,s=fa(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?ga(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return pa(t,c,o,r/n),c;const h=ua(o,t,n);if(r>0){let d,f;const u=r>1?Math.round((l-a)/(r-1)):null;for(ge(t,c,h,F(u)?0:a-u,a),d=0,f=r-1;d<f;d++)ge(t,c,h,o[d],o[d+1]);return ge(t,c,h,l,F(u)?t.length:l+u),c}return ge(t,c,h),c}function fa(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function ua(i,t,e){const s=ma(i),n=t.length/e;if(!s)return Math.max(n,1);const o=co(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>n)return l}return Math.max(n,1)}function ga(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function pa(i,t,e,s){let n=0,o=e[0],r;for(s=Math.ceil(s),r=0;r<i.length;r++)r===o&&(t.push(i[r]),n++,o=e[n*s])}function ge(i,t,e,s,n){const o=D(s,0),r=Math.min(D(n,i.length),i.length);let a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(i[c]),a++,h=Math.round(o+a*e))}function ma(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const ba=i=>i==="left"?"right":i==="right"?"left":i,ss=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,ns=(i,t)=>Math.min(t||i,i);function os(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function _a(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,r=i._endPixel,a=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,r-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-a||l>r+a)))return l}function xa(i,t){A(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function Nt(i){return i.drawTicks?i.tickLength:0}function rs(i,t){if(!i.display)return 0;const e=N(i.font,t),s=q(i.padding);return(E(i.text)?i.text.length:1)*e.lineHeight+s.height}function ya(i,t){return Pt(i,{scale:t,type:"scale"})}function va(i,t,e){return Pt(i,{tick:e,index:t,type:"tick"})}function wa(i,t,e){let s=gi(i);return(e&&t!=="right"||!e&&t==="right")&&(s=ba(s)),s}function ka(i,t,e,s){const{top:n,left:o,bottom:r,right:a,chart:l}=i,{chartArea:c,scales:h}=l;let d=0,f,u,m;const g=r-n,p=a-o;if(i.isHorizontal()){if(u=W(s,o,a),O(e)){const b=Object.keys(e)[0],_=e[b];m=h[b].getPixelForValue(_)+g-t}else e==="center"?m=(c.bottom+c.top)/2+g-t:m=ss(i,e,t);f=a-o}else{if(O(e)){const b=Object.keys(e)[0],_=e[b];u=h[b].getPixelForValue(_)-p+t}else e==="center"?u=(c.left+c.right)/2-p+t:u=ss(i,e,t);m=W(s,r,n),d=e==="left"?-G:G}return{titleX:u,titleY:m,maxWidth:f,rotation:d}}class Rt extends nt{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=tt(t,Number.POSITIVE_INFINITY),e=tt(e,Number.NEGATIVE_INFINITY),s=tt(s,Number.POSITIVE_INFINITY),n=tt(n,Number.NEGATIVE_INFINITY),{min:tt(t,s),max:tt(e,n),minDefined:V(t),maxDefined:V(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(s=Math.max(s,r.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:tt(e,tt(s,e)),max:tt(s,tt(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){I(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=jo(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?os(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=da(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){I(this.options.afterUpdate,[this])}beforeSetDimensions(){I(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){I(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),I(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){I(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=I(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){I(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){I(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=ns(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let r=n,a,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,f=h.highest.height,u=Z(this.chart.width-d,0,this.maxWidth);a=t.offset?this.maxWidth/s:u/(s-1),d+6>a&&(a=u/(s-(t.offset?.5:1)),l=this.maxHeight-Nt(t.grid)-e.padding-rs(t.title,this.chart.options.font),c=Math.sqrt(d*d+f*f),r=go(Math.min(Math.asin(Z((h.highest.height+6)/a,-1,1)),Math.asin(Z(l/c,-1,1))-Math.asin(Z(f/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){I(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){I(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=rs(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=Nt(o)+l):(t.height=this.maxHeight,t.width=Nt(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:f}=this._getLabelSizes(),u=s.padding*2,m=wt(this.labelRotation),g=Math.cos(m),p=Math.sin(m);if(a){const b=s.mirror?0:p*d.width+g*f.height;t.height=Math.min(this.maxHeight,t.height+b+u)}else{const b=s.mirror?0:g*d.width+p*f.height;t.width=Math.min(this.maxWidth,t.width+b+u)}this._calculatePadding(c,h,p,g)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,u=0;l?c?(f=n*t.width,u=s*e.height):(f=s*t.height,u=n*e.width):o==="start"?u=e.width:o==="end"?f=t.width:o!=="inner"&&(f=t.width/2,u=e.width/2),this.paddingLeft=Math.max((f-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((u-d+r)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){I(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)F(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=os(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/ns(e,s));let c=0,h=0,d,f,u,m,g,p,b,_,y,v,x;for(d=0;d<e;d+=l){if(m=t[d].label,g=this._resolveTickFontOptions(d),n.font=p=g.string,b=o[p]=o[p]||{data:{},gc:[]},_=g.lineHeight,y=v=0,!F(m)&&!E(m))y=zi(n,b.data,b.gc,y,m),v=_;else if(E(m))for(f=0,u=m.length;f<u;++f)x=m[f],!F(x)&&!E(x)&&(y=zi(n,b.data,b.gc,y,x),v+=_);r.push(y),a.push(v),c=Math.max(y,c),h=Math.max(v,h)}xa(o,e);const k=r.indexOf(c),S=a.indexOf(h),w=P=>({width:r[P]||0,height:a[P]||0});return{first:w(0),last:w(e-1),widest:w(k),highest:w(S),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return bo(this._alignToPixels?bt(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=va(this.getContext(),t,s))}return this.$context||(this.$context=ya(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=wt(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),f=Nt(o),u=[],m=a.setContext(this.getContext()),g=m.display?m.width:0,p=g/2,b=function(H){return bt(s,H,g)};let _,y,v,x,k,S,w,P,L,C,T,j;if(r==="top")_=b(this.bottom),S=this.bottom-f,P=_-p,C=b(t.top)+p,j=t.bottom;else if(r==="bottom")_=b(this.top),C=t.top,j=b(t.bottom)-p,S=_+p,P=this.top+f;else if(r==="left")_=b(this.right),k=this.right-f,w=_-p,L=b(t.left)+p,T=t.right;else if(r==="right")_=b(this.left),L=t.left,T=b(t.right)-p,k=_+p,w=this.left+f;else if(e==="x"){if(r==="center")_=b((t.top+t.bottom)/2+.5);else if(O(r)){const H=Object.keys(r)[0],U=r[H];_=b(this.chart.scales[H].getPixelForValue(U))}C=t.top,j=t.bottom,S=_+p,P=S+f}else if(e==="y"){if(r==="center")_=b((t.left+t.right)/2);else if(O(r)){const H=Object.keys(r)[0],U=r[H];_=b(this.chart.scales[H].getPixelForValue(U))}k=_-p,w=k-f,L=t.left,T=t.right}const J=D(n.ticks.maxTicksLimit,d),R=Math.max(1,Math.ceil(d/J));for(y=0;y<d;y+=R){const H=this.getContext(y),U=o.setContext(H),ie=a.setContext(H),se=U.lineWidth,Dt=U.color,ne=ie.dash||[],Ot=ie.dashOffset,zt=U.tickWidth,gt=U.tickColor,Et=U.tickBorderDash||[],pt=U.tickBorderDashOffset;v=_a(this,y,l),v!==void 0&&(x=bt(s,v,se),c?k=w=L=T=x:S=P=C=j=x,u.push({tx1:k,ty1:S,tx2:w,ty2:P,x1:L,y1:C,x2:T,y2:j,width:se,color:Dt,borderDash:ne,borderDashOffset:Ot,tickWidth:zt,tickColor:gt,tickBorderDash:Et,tickBorderDashOffset:pt}))}return this._ticksLength=d,this._borderValue=_,u}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,f=Nt(s.grid),u=f+h,m=d?-h:u,g=-wt(this.labelRotation),p=[];let b,_,y,v,x,k,S,w,P,L,C,T,j="middle";if(n==="top")k=this.bottom-m,S=this._getXAxisLabelAlignment();else if(n==="bottom")k=this.top+m,S=this._getXAxisLabelAlignment();else if(n==="left"){const R=this._getYAxisLabelAlignment(f);S=R.textAlign,x=R.x}else if(n==="right"){const R=this._getYAxisLabelAlignment(f);S=R.textAlign,x=R.x}else if(e==="x"){if(n==="center")k=(t.top+t.bottom)/2+u;else if(O(n)){const R=Object.keys(n)[0],H=n[R];k=this.chart.scales[R].getPixelForValue(H)+u}S=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")x=(t.left+t.right)/2-u;else if(O(n)){const R=Object.keys(n)[0],H=n[R];x=this.chart.scales[R].getPixelForValue(H)}S=this._getYAxisLabelAlignment(f).textAlign}e==="y"&&(l==="start"?j="top":l==="end"&&(j="bottom"));const J=this._getLabelSizes();for(b=0,_=a.length;b<_;++b){y=a[b],v=y.label;const R=o.setContext(this.getContext(b));w=this.getPixelForTick(b)+o.labelOffset,P=this._resolveTickFontOptions(b),L=P.lineHeight,C=E(v)?v.length:1;const H=C/2,U=R.color,ie=R.textStrokeColor,se=R.textStrokeWidth;let Dt=S;r?(x=w,S==="inner"&&(b===_-1?Dt=this.options.reverse?"left":"right":b===0?Dt=this.options.reverse?"right":"left":Dt="center"),n==="top"?c==="near"||g!==0?T=-C*L+L/2:c==="center"?T=-J.highest.height/2-H*L+L:T=-J.highest.height+L/2:c==="near"||g!==0?T=L/2:c==="center"?T=J.highest.height/2-H*L:T=J.highest.height-C*L,d&&(T*=-1),g!==0&&!R.showLabelBackdrop&&(x+=L/2*Math.sin(g))):(k=w,T=(1-C)*L/2);let ne;if(R.showLabelBackdrop){const Ot=q(R.backdropPadding),zt=J.heights[b],gt=J.widths[b];let Et=T-Ot.top,pt=0-Ot.left;switch(j){case"middle":Et-=zt/2;break;case"bottom":Et-=zt;break}switch(S){case"center":pt-=gt/2;break;case"right":pt-=gt;break;case"inner":b===_-1?pt-=gt:b>0&&(pt-=gt/2);break}ne={left:pt,top:Et,width:gt+Ot.width,height:zt+Ot.height,color:R.backdropColor}}p.push({label:v,font:P,textOffset:T,options:{rotation:g,color:U,strokeColor:ie,strokeWidth:se,textAlign:Dt,textBaseline:j,translation:[x,k],backdrop:ne}})}return p}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-wt(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){const l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,f;this.isHorizontal()?(c=bt(t,this.left,r)-r/2,h=bt(t,this.right,a)+a/2,d=f=l):(d=bt(t,this.top,r)-r/2,f=bt(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,f),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&Le(s,n);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;Jt(s,c,0,h,l,a)}n&&Ie(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=N(s.font),r=q(s.padding),a=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||O(e)?(l+=r.bottom,E(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:d,rotation:f}=ka(this,l,e,a);Jt(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:f,textAlign:wa(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=D(t.grid&&t.grid.z,-1),n=D(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Rt.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[s]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return N(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class pe{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;Pa(e)&&(s=this.register(e));const n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,Sa(t,r,s),this.override&&z.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in z[n]&&(delete z[n][s],this.override&&delete Mt[s])}}function Sa(i,t,e){const s=Zt(Object.create(null),[e?z.get(e):{},z.get(t),i.defaults]);z.set(t,s),i.defaultRoutes&&Ma(t,i.defaultRoutes),i.descriptors&&z.describe(t,i.descriptors)}function Ma(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");z.route(o,n,l,a)})}function Pa(i){return"id"in i&&"defaults"in i}class Da{constructor(){this.controllers=new pe(xe,"datasets",!0),this.elements=new pe(nt,"elements"),this.plugins=new pe(Object,"plugins"),this.scales=new pe(Rt,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):A(n,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,s){const n=fi(t);I(s["before"+n],[],s),e[t](s),I(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var it=new Da;class Oa{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,s,n){n=n||{};for(const o of t){const r=o.plugin,a=r[s],l=[e,n,o.options];if(I(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){F(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=D(s.options&&s.options.plugins,{}),o=Ca(s);return n===!1&&!e?[]:Aa(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function Ca(i){const t={},e=[],s=Object.keys(it.plugins.items);for(let o=0;o<s.length;o++)e.push(it.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function Ta(i,t){return!t&&i===!1?null:i===!0?{}:i}function Aa(i,{plugins:t,localIds:e},s,n){const o=[],r=i.getContext();for(const a of t){const l=a.id,c=Ta(s[l],n);c!==null&&o.push({plugin:a,options:La(i.config,{plugin:a,local:e[l]},c,r)})}return o}function La(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),r=i.getOptionScopes(s,o);return e&&t.defaults&&r.push(t.defaults),i.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function ni(i,t){const e=z.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function Ia(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function Fa(i,t){return i===t?"_index_":"_value_"}function as(i){if(i==="x"||i==="y"||i==="r")return i}function Ra(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function oi(i,...t){if(as(i))return i;for(const e of t){const s=e.axis||Ra(e.position)||i.length>1&&as(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function ls(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function za(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return ls(i,"x",e[0])||ls(i,"y",e[0])}return{}}function Ea(i,t){const e=Mt[i.type]||{scales:{}},s=t.scales||{},n=ni(i.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!O(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=oi(r,a,za(r,i),z.scales[a.type]),c=Fa(l,n),h=e.scales||{};o[r]=Ut(Object.create(null),[{axis:l},a,h[l],h[c]])}),i.data.datasets.forEach(r=>{const a=r.type||i.type,l=r.indexAxis||ni(a,t),h=(Mt[a]||{}).scales||{};Object.keys(h).forEach(d=>{const f=Ia(d,l),u=r[f+"AxisID"]||f;o[u]=o[u]||Object.create(null),Ut(o[u],[{axis:f},s[u],h[d]])})}),Object.keys(o).forEach(r=>{const a=o[r];Ut(a,[z.scales[a.type],z.scale])}),o}function _n(i){const t=i.options||(i.options={});t.plugins=D(t.plugins,{}),t.scales=Ea(i,t)}function xn(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function Ba(i){return i=i||{},i.data=xn(i.data),_n(i),i}const cs=new Map,yn=new Set;function me(i,t){let e=cs.get(i);return e||(e=t(),cs.set(i,e),yn.add(e)),e}const Vt=(i,t,e)=>{const s=Se(t,e);s!==void 0&&i.add(s)};class Ha{constructor(t){this._config=Ba(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=xn(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),_n(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return me(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return me(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return me(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return me(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,r=this._cachedScopes(t,s),a=r.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>Vt(l,t,d))),h.forEach(d=>Vt(l,n,d)),h.forEach(d=>Vt(l,Mt[o]||{},d)),h.forEach(d=>Vt(l,z,d)),h.forEach(d=>Vt(l,ii,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),yn.has(e)&&r.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,Mt[e]||{},z.datasets[e]||{},{type:e},z,ii]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=hs(this._resolverCache,t,n);let l=r;if(Na(r,e)){o.$shared=!1,s=ut(s)?s():s;const c=this.createResolver(t,s,a);l=It(r,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=hs(this._resolverCache,t,s);return O(e)?It(o,e,void 0,n):o}}function hs(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:mi(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}const Wa=i=>O(i)&&Object.getOwnPropertyNames(i).some(t=>ut(i[t]));function Na(i,t){const{isScriptable:e,isIndexable:s}=Js(i);for(const n of t){const o=e(n),r=s(n),a=(r||o)&&i[n];if(o&&(ut(a)||Wa(a))||r&&E(a))return!0}return!1}var Va="4.5.0";const ja=["top","bottom","left","right","chartArea"];function ds(i,t){return i==="top"||i==="bottom"||ja.indexOf(i)===-1&&t==="x"}function fs(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function us(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),I(e&&e.onComplete,[i],t)}function $a(i){const t=i.chart,e=t.options.animation;I(e&&e.onProgress,[i],t)}function vn(i){return xi()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const ve={},gs=i=>{const t=vn(i);return Object.values(ve).filter(e=>e.canvas===t).pop()};function Ya(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const r=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=r)}}}function Ua(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}class vt{static register(...t){it.add(...t),ps()}static unregister(...t){it.remove(...t),ps()}constructor(t,e){const s=this.config=new Ha(e),n=vn(t),o=gs(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||ha(n)),this.platform.updateConfig(s);const a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=to(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Oa,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=wo(d=>this.update(d),r.resizeDelay||0),this._dataChanges=[],ve[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}rt.listen(this,"complete",us),rt.listen(this,"progress",$a),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return F(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return it}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Wi(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Ei(this.canvas,this.ctx),this}stop(){return rt.stop(this),this}resize(t,e){rt.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Wi(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),I(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};A(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=oi(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),A(o,r=>{const a=r.options,l=a.id,c=oi(l,a),h=D(a.type,r.dtype);(a.position===void 0||ds(a.position,c)!==ds(r.dposition))&&(a.position=r.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{const f=it.getScale(h);d=new f({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(a,t)}),A(n,(r,a)=>{r||delete s[a]}),A(s,r=>{K.configure(this,r,r.options),K.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(fs("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||ni(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=it.getController(a),{datasetElementType:c,dataElementType:h}=z.datasets[a];Object.assign(l,{dataElementType:it.getElement(h),datasetElementType:c&&it.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){A(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),f=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(f),r=Math.max(+d.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),n||A(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(fs("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){A(this.scales,t=>{K.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!Di(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const r=s==="_removeElements"?-o:o;Ya(t,n,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!Di(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;K.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],A(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,ut(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(rt.has(this)?this.attached&&!rt.running(this)&&rt.start(this):(this.draw(),us({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const r=e[n];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s={meta:t,index:t.index,cancelable:!0},n=hn(this,t);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(n&&Le(e,n),t.controller.draw(),n&&Ie(e),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(t){return Qt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=jr.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=Pt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);Me(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),rt.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Ei(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete ve[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};A(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{n("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){A(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},A(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!we(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),r=o(e,t),a=s?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,s,r),l=ro(t),c=Ua(t,this._lastEvent,s,l);s&&(this._lastEvent=null,I(o.onHover,[t,a,this],this),l&&I(o.onClick,[t,a,this],this));const h=!we(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}}M(vt,"defaults",z),M(vt,"instances",ve),M(vt,"overrides",Mt),M(vt,"registry",it),M(vt,"version",Va),M(vt,"getChart",gs);function ps(){return A(vt.instances,i=>i._plugins.invalidate())}function wn(i,t,e=t){i.lineCap=D(e.borderCapStyle,t.borderCapStyle),i.setLineDash(D(e.borderDash,t.borderDash)),i.lineDashOffset=D(e.borderDashOffset,t.borderDashOffset),i.lineJoin=D(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=D(e.borderWidth,t.borderWidth),i.strokeStyle=D(e.borderColor,t.borderColor)}function Xa(i,t,e){i.lineTo(e.x,e.y)}function Ka(i){return i.stepped?Fo:i.tension||i.cubicInterpolationMode==="monotone"?Ro:Xa}function kn(i,t,e={}){const s=i.length,{start:n=0,end:o=s-1}=e,{start:r,end:a}=t,l=Math.max(n,r),c=Math.min(o,a),h=n<r&&o<r||n>a&&o>a;return{count:s,start:l,loop:t.loop,ilen:c<l&&!h?s+c-l:c-l}}function qa(i,t,e,s){const{points:n,options:o}=t,{count:r,start:a,loop:l,ilen:c}=kn(n,e,s),h=Ka(o);let{move:d=!0,reverse:f}=s||{},u,m,g;for(u=0;u<=c;++u)m=n[(a+(f?c-u:u))%r],!m.skip&&(d?(i.moveTo(m.x,m.y),d=!1):h(i,g,m,f,o.stepped),g=m);return l&&(m=n[(a+(f?c:0))%r],h(i,g,m,f,o.stepped)),!!l}function Ga(i,t,e,s){const n=t.points,{count:o,start:r,ilen:a}=kn(n,e,s),{move:l=!0,reverse:c}=s||{};let h=0,d=0,f,u,m,g,p,b;const _=v=>(r+(c?a-v:v))%o,y=()=>{g!==p&&(i.lineTo(h,p),i.lineTo(h,g),i.lineTo(h,b))};for(l&&(u=n[_(0)],i.moveTo(u.x,u.y)),f=0;f<=a;++f){if(u=n[_(f)],u.skip)continue;const v=u.x,x=u.y,k=v|0;k===m?(x<g?g=x:x>p&&(p=x),h=(d*h+v)/++d):(y(),i.lineTo(v,x),m=k,d=0,g=p=x),b=x}y()}function ri(i){const t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?Ga:qa}function Za(i){return i.stepped?ur:i.tension||i.cubicInterpolationMode==="monotone"?gr:yt}function Qa(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),wn(i,t.options),i.stroke(n)}function Ja(i,t,e,s){const{segments:n,options:o}=t,r=ri(t);for(const a of n)wn(i,o,a.style),i.beginPath(),r(i,t,a,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}const tl=typeof Path2D=="function";function el(i,t,e,s){tl&&!t.options.segment?Qa(i,t,e,s):Ja(i,t,e,s)}class kt extends nt{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const n=s.spanGaps?this._loop:this._fullLoop;or(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=yr(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options,n=t[e],o=this.points,r=cn(this,{property:e,start:n,end:n});if(!r.length)return;const a=[],l=Za(s);let c,h;for(c=0,h=r.length;c<h;++c){const{start:d,end:f}=r[c],u=o[d],m=o[f];if(u===m){a.push(u);continue}const g=Math.abs((n-u[e])/(m[e]-u[e])),p=l(u,m,g,s.stepped);p[e]=t[e],a.push(p)}return a.length===1?a[0]:a}pathSegment(t,e,s){return ri(this)(t,this,e,s)}path(t,e,s){const n=this.segments,o=ri(this);let r=this._loop;e=e||0,s=s||this.points.length-e;for(const a of n)r&=o(t,this,a,{start:e,end:e+s-1});return!!r}draw(t,e,s,n){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),el(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}M(kt,"id","line"),M(kt,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),M(kt,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),M(kt,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function ms(i,t,e,s){const n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}class Xe extends nt{constructor(e){super();M(this,"parsed");M(this,"skip");M(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.options,{x:r,y:a}=this.getProps(["x","y"],n);return Math.pow(e-r,2)+Math.pow(s-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,s){return ms(this,e,"x",s)}inYRange(e,s){return ms(this,e,"y",s)}getCenterPoint(e){const{x:s,y:n}=this.getProps(["x","y"],e);return{x:s,y:n}}size(e){e=e||this.options||{};let s=e.radius||0;s=Math.max(s,s&&e.hoverRadius||0);const n=s&&e.borderWidth||0;return(s+n)*2}draw(e,s){const n=this.options;this.skip||n.radius<.1||!Qt(this,s,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,si(e,n,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}M(Xe,"id","point"),M(Xe,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),M(Xe,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Sn(i,t){const{x:e,y:s,base:n,width:o,height:r}=i.getProps(["x","y","base","width","height"],t);let a,l,c,h,d;return i.horizontal?(d=r/2,a=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,a=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:a,top:c,right:l,bottom:h}}function dt(i,t,e,s){return i?0:Z(t,e,s)}function il(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=Qs(s);return{t:dt(n.top,o.top,0,e),r:dt(n.right,o.right,0,t),b:dt(n.bottom,o.bottom,0,e),l:dt(n.left,o.left,0,t)}}function sl(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=Tt(n),r=Math.min(t,e),a=i.borderSkipped,l=s||O(n);return{topLeft:dt(!l||a.top||a.left,o.topLeft,0,r),topRight:dt(!l||a.top||a.right,o.topRight,0,r),bottomLeft:dt(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:dt(!l||a.bottom||a.right,o.bottomRight,0,r)}}function nl(i){const t=Sn(i),e=t.right-t.left,s=t.bottom-t.top,n=il(i,e/2,s/2),o=sl(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function Ke(i,t,e,s){const n=t===null,o=e===null,a=i&&!(n&&o)&&Sn(i,s);return a&&(n||ht(t,a.left,a.right))&&(o||ht(e,a.top,a.bottom))}function ol(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function rl(i,t){i.rect(t.x,t.y,t.w,t.h)}function qe(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,r=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+r,radius:i.radius}}class Ge extends nt{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:r}=nl(this),a=ol(r.radius)?Oe:rl;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,qe(r,e,o)),t.clip(),a(t,qe(o,-e,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,qe(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return Ke(this,t,e,s)}inXRange(t,e){return Ke(this,t,null,e)}inYRange(t,e){return Ke(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}M(Ge,"id","bar"),M(Ge,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),M(Ge,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function al(i,t,e){const s=i.segments,n=i.points,o=t.points,r=[];for(const a of s){let{start:l,end:c}=a;c=ze(l,c,n);const h=ai(e,n[l],n[c],a.loop);if(!t.segments){r.push({source:a,target:h,start:n[l],end:n[c]});continue}const d=cn(t,h);for(const f of d){const u=ai(e,o[f.start],o[f.end],f.loop),m=ln(a,n,u);for(const g of m)r.push({source:g,target:f,start:{[e]:bs(h,u,"start",Math.max)},end:{[e]:bs(h,u,"end",Math.min)}})}}return r}function ai(i,t,e,s){if(s)return;let n=t[i],o=e[i];return i==="angle"&&(n=st(n),o=st(o)),{property:i,start:n,end:o}}function ll(i,t){const{x:e=null,y:s=null}=i||{},n=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=ze(r,a,n);const l=n[r],c=n[a];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function ze(i,t,e){for(;t>i;t--){const s=e[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function bs(i,t,e,s){return i&&t?s(i[e],t[e]):i?i[e]:t?t[e]:0}function Mn(i,t){let e=[],s=!1;return E(i)?(s=!0,e=i):e=ll(i,t),e.length?new kt({points:e,options:{tension:0},_loop:s,_fullLoop:s}):null}function _s(i){return i&&i.fill!==!1}function cl(i,t,e){let n=i[t].fill;const o=[t];let r;if(!e)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!V(n))return n;if(r=i[n],!r)return!1;if(r.visible)return n;o.push(n),n=r.fill}return!1}function hl(i,t,e){const s=gl(i);if(O(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return V(n)&&Math.floor(n)===n?dl(s[0],t,n,e):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function dl(i,t,e,s){return(i==="-"||i==="+")&&(e=t+e),e===t||e<0||e>=s?!1:e}function fl(i,t){let e=null;return i==="start"?e=t.bottom:i==="end"?e=t.top:O(i)?e=t.getPixelForValue(i.value):t.getBasePixel&&(e=t.getBasePixel()),e}function ul(i,t,e){let s;return i==="start"?s=e:i==="end"?s=t.options.reverse?t.min:t.max:O(i)?s=i.value:s=t.getBaseValue(),s}function gl(i){const t=i.options,e=t.fill;let s=D(e&&e.target,e);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function pl(i){const{scale:t,index:e,line:s}=i,n=[],o=s.segments,r=s.points,a=ml(t,e);a.push(Mn({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)bl(n,r[h],a)}return new kt({points:n,options:{}})}function ml(i,t){const e=[],s=i.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const o=s[n];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function bl(i,t,e){const s=[];for(let n=0;n<e.length;n++){const o=e[n],{first:r,last:a,point:l}=_l(o,t,"x");if(!(!l||r&&a)){if(r)s.unshift(l);else if(i.push(l),!a)break}}i.push(...s)}function _l(i,t,e){const s=i.interpolate(t,e);if(!s)return{};const n=s[e],o=i.segments,r=i.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],d=r[h.start][e],f=r[h.end][e];if(ht(n,d,f)){a=n===d,l=n===f;break}}return{first:a,last:l,point:s}}class Pn{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,s){const{x:n,y:o,radius:r}=this;return e=e||{start:0,end:Q},t.arc(n,o,r,e.end,e.start,!0),!s.bounds}interpolate(t){const{x:e,y:s,radius:n}=this,o=t.angle;return{x:e+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}}function xl(i){const{chart:t,fill:e,line:s}=i;if(V(e))return yl(t,e);if(e==="stack")return pl(i);if(e==="shape")return!0;const n=vl(i);return n instanceof Pn?n:Mn(n,s)}function yl(i,t){const e=i.getDatasetMeta(t);return e&&i.isDatasetVisible(t)?e.dataset:null}function vl(i){return(i.scale||{}).getPointPositionForValue?kl(i):wl(i)}function wl(i){const{scale:t={},fill:e}=i,s=fl(e,t);if(V(s)){const n=t.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function kl(i){const{scale:t,fill:e}=i,s=t.options,n=t.getLabels().length,o=s.reverse?t.max:t.min,r=ul(e,t,o),a=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new Pn({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<n;++l)a.push(t.getPointPositionForValue(l,r));return a}function Ze(i,t,e){const s=xl(t),{chart:n,index:o,line:r,scale:a,axis:l}=t,c=r.options,h=c.fill,d=c.backgroundColor,{above:f=d,below:u=d}=h||{},m=n.getDatasetMeta(o),g=hn(n,m);s&&r.points.length&&(Le(i,e),Sl(i,{line:r,target:s,above:f,below:u,area:e,scale:a,axis:l,clip:g}),Ie(i))}function Sl(i,t){const{line:e,target:s,above:n,below:o,area:r,scale:a,clip:l}=t,c=e._loop?"angle":t.axis;i.save();let h=o;o!==n&&(c==="x"?(xs(i,s,r.top),Qe(i,{line:e,target:s,color:n,scale:a,property:c,clip:l}),i.restore(),i.save(),xs(i,s,r.bottom)):c==="y"&&(ys(i,s,r.left),Qe(i,{line:e,target:s,color:o,scale:a,property:c,clip:l}),i.restore(),i.save(),ys(i,s,r.right),h=n)),Qe(i,{line:e,target:s,color:h,scale:a,property:c,clip:l}),i.restore()}function xs(i,t,e){const{segments:s,points:n}=t;let o=!0,r=!1;i.beginPath();for(const a of s){const{start:l,end:c}=a,h=n[l],d=n[ze(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(h.x,e),i.lineTo(h.x,h.y)),r=!!t.pathSegment(i,a,{move:r}),r?i.closePath():i.lineTo(d.x,e)}i.lineTo(t.first().x,e),i.closePath(),i.clip()}function ys(i,t,e){const{segments:s,points:n}=t;let o=!0,r=!1;i.beginPath();for(const a of s){const{start:l,end:c}=a,h=n[l],d=n[ze(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(e,h.y),i.lineTo(h.x,h.y)),r=!!t.pathSegment(i,a,{move:r}),r?i.closePath():i.lineTo(e,d.y)}i.lineTo(e,t.first().y),i.closePath(),i.clip()}function Qe(i,t){const{line:e,target:s,property:n,color:o,scale:r,clip:a}=t,l=al(e,s,n);for(const{source:c,target:h,start:d,end:f}of l){const{style:{backgroundColor:u=o}={}}=c,m=s!==!0;i.save(),i.fillStyle=u,Ml(i,r,a,m&&ai(n,d,f)),i.beginPath();const g=!!e.pathSegment(i,c);let p;if(m){g?i.closePath():vs(i,s,f,n);const b=!!s.pathSegment(i,h,{move:g,reverse:!0});p=g&&b,p||vs(i,s,d,n)}i.closePath(),i.fill(p?"evenodd":"nonzero"),i.restore()}}function Ml(i,t,e,s){const n=t.chart.chartArea,{property:o,start:r,end:a}=s||{};if(o==="x"||o==="y"){let l,c,h,d;o==="x"?(l=r,c=n.top,h=a,d=n.bottom):(l=n.left,c=r,h=n.right,d=a),i.beginPath(),e&&(l=Math.max(l,e.left),h=Math.min(h,e.right),c=Math.max(c,e.top),d=Math.min(d,e.bottom)),i.rect(l,c,h-l,d-c),i.clip()}}function vs(i,t,e,s){const n=t.interpolate(e,s);n&&i.lineTo(n.x,n.y)}var ql={id:"filler",afterDatasetsUpdate(i,t,e){const s=(i.data.datasets||[]).length,n=[];let o,r,a,l;for(r=0;r<s;++r)o=i.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof kt&&(l={visible:i.isDatasetVisible(r),index:r,fill:hl(a,r,s),chart:i,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,n.push(l);for(r=0;r<s;++r)l=n[r],!(!l||l.fill===!1)&&(l.fill=cl(n,r,e.propagate))},beforeDraw(i,t,e){const s=e.drawTime==="beforeDraw",n=i.getSortedVisibleDatasetMetas(),o=i.chartArea;for(let r=n.length-1;r>=0;--r){const a=n[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),s&&a.fill&&Ze(i.ctx,a,o))}},beforeDatasetsDraw(i,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const s=i.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const o=s[n].$filler;_s(o)&&Ze(i.ctx,o,i.chartArea)}},beforeDatasetDraw(i,t,e){const s=t.meta.$filler;!_s(s)||e.drawTime!=="beforeDatasetDraw"||Ze(i.ctx,s,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const ws=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},Pl=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class ks extends nt{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=I(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=N(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=ws(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a;let d=t;o.textAlign="left",o.textBaseline="middle";let f=-1,u=-h;return this.legendItems.forEach((m,g)=>{const p=s+e/2+o.measureText(m.text).width;(g===0||c[c.length-1]+p+2*a>r)&&(d+=h,c[c.length-(g>0?0:1)]=0,u+=h,f++),l[g]={left:0,top:u,row:f,width:p,height:n},c[c.length-1]+=p+a}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let d=a,f=0,u=0,m=0,g=0;return this.legendItems.forEach((p,b)=>{const{itemWidth:_,itemHeight:y}=Dl(s,e,o,p,n);b>0&&u+y+2*a>h&&(d+=f+a,c.push({width:f,height:u}),m+=f+a,g++,f=u=0),l[b]={left:m,top:u,col:g,width:_,height:y},f=Math.max(f,_),u+=y+a}),d+=f,c.push({width:f,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,r=At(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=W(s,this.left+n,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=W(s,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=W(s,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=W(s,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Le(t,this),this._draw(),Ie(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:r}=t,a=z.color,l=At(t.rtl,this.left,this.width),c=N(r.font),{padding:h}=r,d=c.size,f=d/2;let u;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:m,boxHeight:g,itemHeight:p}=ws(r,d),b=function(k,S,w){if(isNaN(m)||m<=0||isNaN(g)||g<0)return;n.save();const P=D(w.lineWidth,1);if(n.fillStyle=D(w.fillStyle,a),n.lineCap=D(w.lineCap,"butt"),n.lineDashOffset=D(w.lineDashOffset,0),n.lineJoin=D(w.lineJoin,"miter"),n.lineWidth=P,n.strokeStyle=D(w.strokeStyle,a),n.setLineDash(D(w.lineDash,[])),r.usePointStyle){const L={radius:g*Math.SQRT2/2,pointStyle:w.pointStyle,rotation:w.rotation,borderWidth:P},C=l.xPlus(k,m/2),T=S+f;Gs(n,L,C,T,r.pointStyleWidth&&m)}else{const L=S+Math.max((d-g)/2,0),C=l.leftForLtr(k,m),T=Tt(w.borderRadius);n.beginPath(),Object.values(T).some(j=>j!==0)?Oe(n,{x:C,y:L,w:m,h:g,radius:T}):n.rect(C,L,m,g),n.fill(),P!==0&&n.stroke()}n.restore()},_=function(k,S,w){Jt(n,w.text,k,S+p/2,c,{strikethrough:w.hidden,textAlign:l.textAlign(w.textAlign)})},y=this.isHorizontal(),v=this._computeTitleHeight();y?u={x:W(o,this.left+h,this.right-s[0]),y:this.top+h+v,line:0}:u={x:this.left+h,y:W(o,this.top+v+h,this.bottom-e[0].height),line:0},on(this.ctx,t.textDirection);const x=p+h;this.legendItems.forEach((k,S)=>{n.strokeStyle=k.fontColor,n.fillStyle=k.fontColor;const w=n.measureText(k.text).width,P=l.textAlign(k.textAlign||(k.textAlign=r.textAlign)),L=m+f+w;let C=u.x,T=u.y;l.setWidth(this.width),y?S>0&&C+L+h>this.right&&(T=u.y+=x,u.line++,C=u.x=W(o,this.left+h,this.right-s[u.line])):S>0&&T+x>this.bottom&&(C=u.x=C+e[u.line].width+h,u.line++,T=u.y=W(o,this.top+v+h,this.bottom-e[u.line].height));const j=l.x(C);if(b(j,T,k),C=ko(P,C+m+f,y?C+L:this.right,t.rtl),_(l.x(C),T,k),y)u.x+=L+h;else if(typeof k.text!="string"){const J=c.lineHeight;u.y+=Dn(k,J)+h}else u.y+=x}),rn(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=N(e.font),n=q(e.padding);if(!e.display)return;const o=At(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=s.size/2,c=n.top+l;let h,d=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),h=this.top+c,d=W(t.align,d,this.right-f);else{const m=this.columnSizes.reduce((g,p)=>Math.max(g,p.height),0);h=c+W(t.align,this.top,this.bottom-m-t.labels.padding-this._computeTitleHeight())}const u=W(a,d,d+f);r.textAlign=o.textAlign(gi(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=s.string,Jt(r,e.text,u,h,s)}_computeTitleHeight(){const t=this.options.title,e=N(t.font),s=q(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(ht(t,this.left,this.right)&&ht(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],ht(t,n.left,n.left+n.width)&&ht(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!Tl(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=Pl(n,s);n&&!o&&I(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&I(e.onHover,[t,s,this],this)}else s&&I(e.onClick,[t,s,this],this)}}function Dl(i,t,e,s,n){const o=Ol(s,i,t,e),r=Cl(n,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function Ol(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+s.measureText(n).width}function Cl(i,t,e){let s=i;return typeof t.text!="string"&&(s=Dn(t,e)),s}function Dn(i,t){const e=i.text?i.text.length:0;return t*e}function Tl(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var Gl={id:"legend",_element:ks,start(i,t,e){const s=i.legend=new ks({ctx:i.ctx,options:e,chart:i});K.configure(i,s,e),K.addBox(i,s)},stop(i){K.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;K.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=q(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class On extends nt{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=E(s.text)?s.text.length:1;this._padding=q(s.padding);const o=n*N(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:r}=this,a=r.align;let l=0,c,h,d;return this.isHorizontal()?(h=W(a,s,o),d=e+t,c=o-s):(r.position==="left"?(h=s+t,d=W(a,n,e),l=B*-.5):(h=o-t,d=W(a,e,n),l=B*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=N(e.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Jt(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:gi(e.align),textBaseline:"middle",translation:[r,a]})}}function Al(i,t){const e=new On({ctx:i.ctx,options:t,chart:i});K.configure(i,e,t),K.addBox(i,e),i.titleBlock=e}var Zl={id:"title",_element:On,start(i,t,e){Al(i,e)},stop(i){const t=i.titleBlock;K.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;K.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Yt={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){const a=i[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),n+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=i.length;o<r;++o){const l=i[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=ti(t,c);h<n&&(n=h,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function et(i,t){return t&&(E(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function at(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function Ll(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:i,label:r,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function Ss(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:r,boxHeight:a}=t,l=N(t.bodyFont),c=N(t.titleFont),h=N(t.footerFont),d=o.length,f=n.length,u=s.length,m=q(t.padding);let g=m.height,p=0,b=s.reduce((v,x)=>v+x.before.length+x.lines.length+x.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(g+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;g+=u*v+(b-u)*l.lineHeight+(b-1)*t.bodySpacing}f&&(g+=t.footerMarginTop+f*h.lineHeight+(f-1)*t.footerSpacing);let _=0;const y=function(v){p=Math.max(p,e.measureText(v).width+_)};return e.save(),e.font=c.string,A(i.title,y),e.font=l.string,A(i.beforeBody.concat(i.afterBody),y),_=t.displayColors?r+2+t.boxPadding:0,A(s,v=>{A(v.before,y),A(v.lines,y),A(v.after,y)}),_=0,e.font=h.string,A(i.footer,y),e.restore(),p+=m.width,{width:p,height:g}}function Il(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function Fl(i,t,e,s){const{x:n,width:o}=s,r=e.caretSize+e.caretPadding;if(i==="left"&&n+o+r>t.width||i==="right"&&n-o-r<0)return!0}function Rl(i,t,e,s){const{x:n,width:o}=e,{width:r,chartArea:{left:a,right:l}}=i;let c="center";return s==="center"?c=n<=(a+l)/2?"left":"right":n<=o/2?c="left":n>=r-o/2&&(c="right"),Fl(c,i,t,e)&&(c="center"),c}function Ms(i,t,e){const s=e.yAlign||t.yAlign||Il(i,e);return{xAlign:e.xAlign||t.xAlign||Rl(i,t,e,s),yAlign:s}}function zl(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function El(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function Ps(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=i,{xAlign:a,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:f,bottomRight:u}=Tt(r);let m=zl(t,a);const g=El(t,l,c);return l==="center"?a==="left"?m+=c:a==="right"&&(m-=c):a==="left"?m-=Math.max(h,f)+n:a==="right"&&(m+=Math.max(d,u)+n),{x:Z(m,0,s.width-t.width),y:Z(g,0,s.height-t.height)}}function be(i,t,e){const s=q(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function Ds(i){return et([],at(i))}function Bl(i,t,e){return Pt(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function Os(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const Cn={beforeTitle:ot,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:ot,beforeBody:ot,beforeLabel:ot,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return F(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:ot,afterBody:ot,beforeFooter:ot,footer:ot,afterFooter:ot};function $(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?Cn[t].call(e,s):n}class li extends nt{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new dn(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=Bl(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=$(s,"beforeTitle",this,t),o=$(s,"title",this,t),r=$(s,"afterTitle",this,t);let a=[];return a=et(a,at(n)),a=et(a,at(o)),a=et(a,at(r)),a}getBeforeBody(t,e){return Ds($(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return A(t,o=>{const r={before:[],lines:[],after:[]},a=Os(s,o);et(r.before,at($(a,"beforeLabel",this,o))),et(r.lines,$(a,"label",this,o)),et(r.after,at($(a,"afterLabel",this,o))),n.push(r)}),n}getAfterBody(t,e){return Ds($(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=$(s,"beforeFooter",this,t),o=$(s,"footer",this,t),r=$(s,"afterFooter",this,t);let a=[];return a=et(a,at(n)),a=et(a,at(o)),a=et(a,at(r)),a}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],r=[];let a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(Ll(this.chart,e[l]));return t.filter&&(a=a.filter((h,d,f)=>t.filter(h,d,f,s))),t.itemSort&&(a=a.sort((h,d)=>t.itemSort(h,d,s))),A(a,h=>{const d=Os(t.callbacks,h);n.push($(d,"labelColor",this,h)),o.push($(d,"labelPointStyle",this,h)),r.push($(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,r=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const a=Yt[s.position].call(this,n,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=Ss(this,s),c=Object.assign({},a,l),h=Ms(this.chart,s,c),d=Ps(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=Tt(a),{x:f,y:u}=t,{width:m,height:g}=e;let p,b,_,y,v,x;return o==="center"?(v=u+g/2,n==="left"?(p=f,b=p-r,y=v+r,x=v-r):(p=f+m,b=p+r,y=v-r,x=v+r),_=p):(n==="left"?b=f+Math.max(l,h)+r:n==="right"?b=f+m-Math.max(c,d)-r:b=this.caretX,o==="top"?(y=u,v=y-r,p=b-r,_=b+r):(y=u+g,v=y+r,p=b+r,_=b-r),x=y),{x1:p,x2:b,x3:_,y1:y,y2:v,y3:x}}drawTitle(t,e,s){const n=this.title,o=n.length;let r,a,l;if(o){const c=At(s.rtl,this.x,this.width);for(t.x=be(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",r=N(s.titleFont),a=s.titleSpacing,e.fillStyle=s.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,e,s,n,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=N(o.bodyFont),d=be(this,"left",o),f=n.x(d),u=l<h.lineHeight?(h.lineHeight-l)/2:0,m=e.y+u;if(o.usePointStyle){const g={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},p=n.leftForLtr(f,c)+c/2,b=m+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,si(t,g,p,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,si(t,g,p,b)}else{t.lineWidth=O(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const g=n.leftForLtr(f,c),p=n.leftForLtr(n.xPlus(f,1),c-2),b=Tt(r.borderRadius);Object.values(b).some(_=>_!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,Oe(t,{x:g,y:m,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),Oe(t,{x:p,y:m+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(g,m,c,l),t.strokeRect(g,m,c,l),t.fillStyle=r.backgroundColor,t.fillRect(p,m+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=N(s.bodyFont);let f=d.lineHeight,u=0;const m=At(s.rtl,this.x,this.width),g=function(w){e.fillText(w,m.x(t.x+u),t.y+f/2),t.y+=f+o},p=m.textAlign(r);let b,_,y,v,x,k,S;for(e.textAlign=r,e.textBaseline="middle",e.font=d.string,t.x=be(this,p,s),e.fillStyle=s.bodyColor,A(this.beforeBody,g),u=a&&p!=="right"?r==="center"?c/2+h:c+2+h:0,v=0,k=n.length;v<k;++v){for(b=n[v],_=this.labelTextColors[v],e.fillStyle=_,A(b.before,g),y=b.lines,a&&y.length&&(this._drawColorBox(e,t,v,m,s),f=Math.max(d.lineHeight,l)),x=0,S=y.length;x<S;++x)g(y[x]),f=d.lineHeight;A(b.after,g)}u=0,f=d.lineHeight,A(this.afterBody,g),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let r,a;if(o){const l=At(s.rtl,this.x,this.width);for(t.x=be(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",r=N(s.footerFont),e.fillStyle=s.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:f,bottomLeft:u,bottomRight:m}=Tt(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+d,l),r==="top"&&this.drawCaret(t,e,s,n),e.lineTo(a+c-f,l),e.quadraticCurveTo(a+c,l,a+c,l+f),r==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(a+c,l+h-m),e.quadraticCurveTo(a+c,l+h,a+c-m,l+h),r==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(a+u,l+h),e.quadraticCurveTo(a,l+h,a,l+h-u),r==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(a,l+d),e.quadraticCurveTo(a,l,a+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const r=Yt[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=Ss(this,t),l=Object.assign({},r,this._size),c=Ms(e,t,l),h=Ps(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=q(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),on(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),rn(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!we(s,n),r=this._positionChanged(n,e);(o||r)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,s),a=this._positionChanged(r,t),l=e||!we(r,o)||a;return l&&(this._active=r,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,r=Yt[o.position].call(this,t,e);return r!==!1&&(s!==r.x||n!==r.y)}}M(li,"positioners",Yt);var Ql={id:"tooltip",_element:li,positioners:Yt,afterInit(i,t,e){e&&(i.tooltip=new li({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Cn},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const Hl=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function Wl(i,t,e,s){const n=i.indexOf(t);if(n===-1)return Hl(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const Nl=(i,t)=>i===null?null:Z(Math.round(i),0,t);function Cs(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class Ts extends Rt{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(F(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:Wl(s,t,D(e,t),this._addedLabels),Nl(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return Cs.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}M(Ts,"id","category"),M(Ts,"defaults",{ticks:{callback:Cs}});function Vl(i,t){const e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:f}=i,u=o||1,m=h-1,{min:g,max:p}=t,b=!F(r),_=!F(a),y=!F(c),v=(p-g)/(d+1);let x=Ci((p-g)/m/u)*u,k,S,w,P;if(x<1e-14&&!b&&!_)return[{value:g},{value:p}];P=Math.ceil(p/x)-Math.floor(g/x),P>m&&(x=Ci(P*x/m/u)*u),F(l)||(k=Math.pow(10,l),x=Math.ceil(x*k)/k),n==="ticks"?(S=Math.floor(g/x)*x,w=Math.ceil(p/x)*x):(S=g,w=p),b&&_&&o&&fo((a-r)/o,x/1e3)?(P=Math.round(Math.min((a-r)/x,h)),x=(a-r)/P,S=r,w=a):y?(S=b?r:S,w=_?a:w,P=c-1,x=(w-S)/P):(P=(w-S)/x,Xt(P,Math.round(P),x/1e3)?P=Math.round(P):P=Math.ceil(P));const L=Math.max(Ti(x),Ti(S));k=Math.pow(10,F(l)?L:l),S=Math.round(S*k)/k,w=Math.round(w*k)/k;let C=0;for(b&&(f&&S!==r?(e.push({value:r}),S<r&&C++,Xt(Math.round((S+C*x)*k)/k,r,As(r,v,i))&&C++):S<r&&C++);C<P;++C){const T=Math.round((S+C*x)*k)/k;if(_&&T>a)break;e.push({value:T})}return _&&f&&w!==a?e.length&&Xt(e[e.length-1].value,a,As(a,v,i))?e[e.length-1].value=a:e.push({value:a}):(!_||w===a)&&e.push({value:w}),e}function As(i,t,{horizontal:e,minRotation:s}){const n=wt(s),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+i).length;return Math.min(t/o,r)}class jl extends Rt{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return F(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const r=l=>n=e?n:l,a=l=>o=s?o:l;if(t){const l=Lt(n),c=Lt(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=Vl(n,o);return t.bounds==="ticks"&&uo(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return Ks(t,this.chart.options.locale,this.options.ticks.format)}}class Ls extends jl{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=V(t)?t:0,this.max=V(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=wt(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}M(Ls,"id","linear"),M(Ls,"defaults",{ticks:{callback:qs.formatters.numeric}});const Ee={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Y=Object.keys(Ee);function Is(i,t){return i-t}function Fs(i,t){if(F(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),V(r)||(r=typeof s=="string"?e.parse(r,s):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(De(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function Rs(i,t,e,s){const n=Y.length;for(let o=Y.indexOf(i);o<n-1;++o){const r=Ee[Y[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=s)return Y[o]}return Y[n-1]}function $l(i,t,e,s,n){for(let o=Y.length-1;o>=Y.indexOf(e);o--){const r=Y[o];if(Ee[r].common&&i._adapter.diff(n,s,r)>=t-1)return r}return Y[e?Y.indexOf(e):0]}function Yl(i){for(let t=Y.indexOf(i)+1,e=Y.length;t<e;++t)if(Ee[Y[t]].common)return Y[t]}function zs(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=ui(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function Ul(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=e[a],l>=0&&(t[l].major=!0);return t}function Es(i,t,e){const s=[],n={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,s.push({value:a,major:!1});return o===0||!e?s:Ul(i,s,n,e)}class Ae extends Rt{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new Br._date(t.adapters.date);n.init(e),Ut(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:Fs(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=V(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=V(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,r=this.max,a=xo(n,o,r);return this._unit=e.unit||(s.autoSkip?Rs(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):$l(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:Yl(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),Es(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=Z(e,0,r),s=Z(s,0,r),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,r=o.unit||Rs(o.minUnit,e,s,this._getLabelCapacity(e)),a=D(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=De(l)||l===!0,h={};let d=e,f,u;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":r),t.diff(s,e,r)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+r);const m=n.ticks.source==="data"&&this.getDataTimestamps();for(f=d,u=0;f<s;f=+t.add(f,a,r),u++)zs(h,f,m);return(f===s||n.bounds==="ticks"||u===1)&&zs(h,f,m),Object.keys(h).sort(Is).map(g=>+g)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,s,n){const o=this.options,r=o.ticks.callback;if(r)return I(r,[t,e,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],d=c&&a[c],f=s[e],u=c&&d&&f&&f.major;return this._adapter.format(t,n||(u?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=wt(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,Es(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(Fs(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return vo(t.sort(Is))}}M(Ae,"id","time"),M(Ae,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function _e(i,t,e){let s=0,n=i.length-1,o,r,a,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=ei(i,"pos",t)),{pos:o,time:a}=i[s],{pos:r,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=ei(i,"time",t)),{time:o,pos:a}=i[s],{time:r,pos:l}=i[n]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class Bs extends Ae{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=_e(e,this.min),this._tableRange=_e(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(_e(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return _e(this._table,s*this._tableRange+this._minPos,!0)}}M(Bs,"id","timeseries"),M(Bs,"defaults",Ae.defaults);export{Ge as B,vt as C,Ls as L,Xe as P,Ts as a,kt as b,Ql as c,Gl as d,ql as i,Zl as p};
