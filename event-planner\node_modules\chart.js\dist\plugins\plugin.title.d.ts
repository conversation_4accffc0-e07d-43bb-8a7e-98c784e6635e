export class Title extends Element<import("../types/basic.js").AnyObject, import("../types/basic.js").AnyObject> {
    /**
       * @param {{ ctx: any; options: any; chart: any; }} config
       */
    constructor(config: {
        ctx: any;
        options: any;
        chart: any;
    });
    chart: any;
    options: any;
    ctx: any;
    _padding: import("../types.js").ChartArea;
    top: number;
    bottom: any;
    left: number;
    right: any;
    width: any;
    height: any;
    position: any;
    weight: any;
    fullSize: any;
    update(maxWidth: any, maxHeight: any): void;
    isHorizontal(): boolean;
    _drawArgs(offset: any): {
        titleX: any;
        titleY: any;
        maxWidth: number;
        rotation: number;
    };
    draw(): void;
}
declare namespace _default {
    export const id: string;
    export { Title as _element };
    export function start(chart: any, _args: any, options: any): void;
    export function stop(chart: any): void;
    export function beforeUpdate(chart: any, _args: any, options: any): void;
    export namespace defaults {
        export const align: string;
        export const display: boolean;
        export namespace font {
            const weight: string;
        }
        export const fullSize: boolean;
        export const padding: number;
        export const position: string;
        export const text: string;
        const weight_1: number;
        export { weight_1 as weight };
    }
    export namespace defaultRoutes {
        const color: string;
    }
    export namespace descriptors {
        const _scriptable: boolean;
        const _indexable: boolean;
    }
}
export default _default;
import Element from "../core/core.element.js";
