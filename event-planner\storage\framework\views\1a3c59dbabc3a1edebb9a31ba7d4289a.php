<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Event Manager')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=montserrat:400,500,600,700,800,900|open-sans:300,400,500,600,700" rel="stylesheet" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#1f2937">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Event Manager">

    <!-- Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="manifest" href="/manifest.json">

    <!-- SEO Meta Tags -->
    <meta name="description" content="Discover and manage amazing events. From conferences to festivals, find your next unforgettable experience with Event Manager.">
    <meta name="keywords" content="events, conferences, festivals, tickets, event management, booking">
    <meta name="author" content="Event Manager">

    <!-- Open Graph -->
    <meta property="og:title" content="Event Manager - Discover Amazing Events">
    <meta property="og:description" content="Discover and manage amazing events. From conferences to festivals, find your next unforgettable experience.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    <meta property="og:image" content="<?php echo e(asset('images/og-image.jpg')); ?>">

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Event Manager - Discover Amazing Events">
    <meta name="twitter:description" content="Discover and manage amazing events. From conferences to festivals, find your next unforgettable experience.">
    <meta name="twitter:image" content="<?php echo e(asset('images/og-image.jpg')); ?>">

    <!-- Styles -->
    <style>
        /* Loading screen styles */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1f2937 0%, #4c1d95 50%, #1f2937 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }
        
        .loading-screen.fade-out {
            opacity: 0;
            pointer-events: none;
        }
        
        .loader {
            width: 60px;
            height: 60px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top: 3px solid #ec4899;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .logo-text {
            background: linear-gradient(-45deg, #ec4899, #06b6d4, #8b5cf6, #f59e0b);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
            font-size: 1.5rem;
            margin-top: 1rem;
        }
        
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #111827;
            color: #ffffff;
            overflow-x: hidden;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', sans-serif;
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #1f2937;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #4b5563;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
        }
        
        /* Smooth transitions */
        * {
            transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
        }
    </style>

    <?php if(false && file_exists(public_path('build/manifest.json'))): ?>
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php else: ?>
        <!-- Production CDN fallback -->
        <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
        <script src="https://unpkg.com/vue-router@4/dist/vue-router.global.prod.js"></script>
        <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
        <script src="https://cdn.tailwindcss.com"></script>
        <script>
            // Suppress Tailwind CSS development warning
            tailwind.config = {
                content: ["./resources/**/*.{html,js,vue}"],
                theme: {
                    extend: {}
                }
            }
        </script>
        <style>
            /* Inline critical CSS */
            .gradient-text {
                background: linear-gradient(135deg, #ec4899, #06b6d4);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
            .animate-gradient {
                background: linear-gradient(-45deg, #ec4899, #06b6d4, #8b5cf6, #f59e0b);
                background-size: 400% 400%;
                animation: gradient 15s ease infinite;
            }
            @keyframes gradient {
                0% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
                100% { background-position: 0% 50%; }
            }
        </style>

        <!-- Working Vue.js App -->
        <script>
            console.log('Vue script starting...');

            // Wait for Vue.js to load
            function initVueApp() {
                console.log('Checking Vue availability...');
                console.log('Vue available:', typeof Vue !== 'undefined');

                if (typeof Vue === 'undefined') {
                    console.log('Vue not ready, retrying in 100ms...');
                    setTimeout(initVueApp, 100);
                    return;
                }

                console.log('Vue is ready! Initializing app...');

                try {
                    const { createApp } = Vue;

                    // Simple Vue app without router
                    const app = createApp({
                        template: '#vue-template',
                        data() {
                            return {
                                clickCount: 0,
                                message: ''
                            }
                        },
                        methods: {
                            testVue() {
                                this.clickCount++;
                                this.message = `Vue.js is working perfectly! Button clicked ${this.clickCount} times.`;
                                setTimeout(() => {
                                    this.message = '';
                                }, 3000);
                            },
                            hideLoadingScreen() {
                                setTimeout(() => {
                                    const loadingScreen = document.getElementById('loading-screen');
                                    if (loadingScreen) {
                                        loadingScreen.style.display = 'none';
                                    }
                                }, 500);
                            }
                        },
                        mounted() {
                            console.log('Vue app mounted successfully!');
                            this.hideLoadingScreen();
                        }
                    });

                    // Mount the app
                    app.mount('#app');
                    console.log('Vue app mounted to #app');

                } catch (error) {
                    console.error('Vue app initialization failed:', error);
                    // Fallback: hide loading screen and show error
                    setTimeout(() => {
                        const loadingScreen = document.getElementById('loading-screen');
                        if (loadingScreen) {
                            loadingScreen.style.display = 'none';
                        }
                        const appElement = document.getElementById('app');
                        if (appElement) {
                            appElement.innerHTML = \`
                                <div class="min-h-screen bg-gray-100 flex items-center justify-center">
                                    <div class="bg-white p-8 rounded-lg shadow-lg text-center">
                                        <h1 class="text-2xl font-bold text-red-600 mb-4">❌ Vue.js Error</h1>
                                        <p class="text-gray-600 mb-4">Vue.js failed to initialize: \${error.message}</p>
                                        <button onclick="location.reload()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Reload Page</button>
                                    </div>
                                </div>
                            \`;
                        }
                    }, 1000);
                }
            }

            // Start the initialization
            window.addEventListener('DOMContentLoaded', initVueApp);

            // Also try after a delay in case DOMContentLoaded already fired
            setTimeout(initVueApp, 100);
        </script>
    <?php endif; ?>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center">
            <div class="loader"></div>
            <div class="logo-text">Event Manager</div>
        </div>
    </div>

    <!-- Vue Template -->
    <script type="text/x-template" id="vue-template">
        <div class="min-h-screen bg-gray-100">
            <!-- Navigation -->
            <nav class="bg-white shadow-sm border-b">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="flex justify-between items-center h-16">
                        <div class="text-xl font-bold text-indigo-600">Event Manager</div>
                        <div class="space-x-4">
                            <a href="/events" class="text-gray-700 hover:text-indigo-600">Events</a>
                            <a href="/login" class="text-gray-700 hover:text-indigo-600">Login</a>
                            <a href="/admin" class="text-gray-700 hover:text-indigo-600">Admin</a>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Hero Section -->
            <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-20">
                <div class="max-w-7xl mx-auto px-4 text-center">
                    <h1 class="text-5xl font-bold mb-6">🎉 Event Manager</h1>
                    <p class="text-xl mb-8">Vue.js is now working! Discover and manage amazing events.</p>
                    <div class="space-x-4">
                        <button @click="testVue" class="bg-white text-indigo-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100">
                            Test Vue.js (<?php echo e(clickCount); ?> clicks)
                        </button>
                        <a href="/events" class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600">Browse Events</a>
                    </div>
                    <div v-if="message" class="mt-4">
                        <p class="text-lg bg-white text-indigo-600 inline-block px-4 py-2 rounded"><?php echo e(message); ?></p>
                    </div>
                </div>
            </div>

            <!-- Features Section -->
            <div class="max-w-7xl mx-auto px-4 py-16">
                <h2 class="text-3xl font-bold text-center mb-12">Quick Access</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-white p-6 rounded-lg shadow-lg text-center">
                        <div class="text-4xl mb-4">🎫</div>
                        <h3 class="text-xl font-bold mb-4">Browse Events</h3>
                        <p class="text-gray-600 mb-4">Discover amazing events happening near you</p>
                        <a href="/events" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">View Events</a>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-lg text-center">
                        <div class="text-4xl mb-4">👤</div>
                        <h3 class="text-xl font-bold mb-4">User Account</h3>
                        <p class="text-gray-600 mb-4">Login to manage your bookings and profile</p>
                        <a href="/login" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Login</a>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-lg text-center">
                        <div class="text-4xl mb-4">⚙️</div>
                        <h3 class="text-xl font-bold mb-4">Admin Panel</h3>
                        <p class="text-gray-600 mb-4">Manage events, users, and system settings</p>
                        <a href="/admin" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Admin</a>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <!-- Vue App -->
    <div id="app">
        <!-- Fallback content while Vue loads -->
        <div class="min-h-screen bg-gray-50 flex items-center justify-center">
            <div class="text-center">
                <div class="loader"></div>
                <div class="logo-text">Loading Event Manager...</div>
                <div class="mt-4">
                    <p class="text-gray-600">If this message persists, there may be a JavaScript loading issue.</p>
                    <button onclick="location.reload()" class="mt-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Reload Page</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Basic JavaScript Test -->
    <script>
        console.log('Basic script loaded');

        // Test basic JavaScript functionality
        window.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');

            // Test if we can manipulate DOM
            const loadingScreen = document.getElementById('loading-screen');
            const app = document.getElementById('app');

            console.log('Loading screen element:', loadingScreen);
            console.log('App element:', app);

            // Simple test - hide loading and show basic content after 2 seconds
            setTimeout(function() {
                console.log('Timeout executed - hiding loading screen');

                if (loadingScreen) {
                    loadingScreen.style.display = 'none';
                }

                if (app) {
                    app.innerHTML = `
                        <div class="min-h-screen bg-gray-100 flex items-center justify-center">
                            <div class="bg-white p-8 rounded-lg shadow-lg text-center max-w-md">
                                <h1 class="text-3xl font-bold text-green-600 mb-4">✅ Basic JavaScript Works!</h1>
                                <p class="text-gray-600 mb-4">The page loaded successfully without Vue.js</p>
                                <p class="text-sm text-gray-500 mb-4">This means the issue is with Vue.js loading, not basic JavaScript</p>
                                <button onclick="testClick()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                                    Test Click
                                </button>
                                <div class="mt-4">
                                    <a href="/test.html" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                                        Test Vue.js Separately
                                    </a>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }, 2000);
        });

        // Test function
        function testClick() {
            alert('JavaScript click event works!');
            console.log('Button clicked successfully');
        }

        // Log any errors
        window.addEventListener('error', function(e) {
            console.error('JavaScript error:', e.error);
            console.error('Error message:', e.message);
            console.error('Error source:', e.filename, 'Line:', e.lineno);
        });
    </script>

</body>
</html><?php /**PATH C:\Users\<USER>\OneDrive\ドキュメント\projrect\events\event-planner\resources\views/app.blade.php ENDPATH**/ ?>