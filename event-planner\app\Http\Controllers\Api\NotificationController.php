<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\EmailService;
use App\Services\SmsService;
use App\Models\Event;
use App\Models\Booking;
use App\Models\User;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    protected $emailService;
    protected $smsService;

    public function __construct(EmailService $emailService, SmsService $smsService)
    {
        $this->emailService = $emailService;
        $this->smsService = $smsService;
    }

    /**
     * Send event reminder to all attendees
     */
    public function sendEventReminder(Request $request, Event $event)
    {
        $validator = Validator::make($request->all(), [
            'include_email' => 'boolean',
            'include_sms' => 'boolean',
            'custom_message' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get all confirmed bookings for this event
        $bookings = Booking::where('event_id', $event->id)
                          ->where('status', 'confirmed')
                          ->with('user')
                          ->get();

        if ($bookings->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No confirmed bookings found for this event'
            ], 422);
        }

        $attendees = $bookings->map(function ($booking) {
            return [
                'name' => $booking->customer_info['name'] ?? $booking->user->name,
                'email' => $booking->customer_info['email'] ?? $booking->user->email,
                'phone' => $booking->customer_info['phone'] ?? $booking->user->phone,
            ];
        })->toArray();

        $results = [];

        // Send email reminders
        if ($request->boolean('include_email', true)) {
            $emailResult = $this->emailService->sendEventReminder($event, $attendees);
            $results['email'] = $emailResult;
        }

        // Send SMS reminders
        if ($request->boolean('include_sms', false)) {
            $smsResult = $this->smsService->sendEventReminderSms($event, $attendees);
            $results['sms'] = $smsResult;
        }

        return response()->json([
            'success' => true,
            'message' => 'Event reminders sent successfully',
            'data' => $results
        ]);
    }

    /**
     * Send custom campaign
     */
    public function sendCampaign(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'recipients' => 'required|array|min:1',
            'recipients.*.email' => 'required|email',
            'recipients.*.name' => 'nullable|string',
            'recipients.*.phone' => 'nullable|string',
            'send_email' => 'boolean',
            'send_sms' => 'boolean',
            'template' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $results = [];

        // Send email campaign
        if ($request->boolean('send_email', true)) {
            $template = $request->get('template', 'emails.campaign');
            $data = [
                'subject' => $request->subject,
                'message' => $request->message,
            ];

            $emailResult = $this->emailService->sendCampaign(
                $request->recipients,
                $request->subject,
                $template,
                $data
            );
            $results['email'] = $emailResult;
        }

        // Send SMS campaign
        if ($request->boolean('send_sms', false)) {
            $sent = 0;
            $failed = 0;

            foreach ($request->recipients as $recipient) {
                if (!empty($recipient['phone'])) {
                    $smsResult = $this->smsService->sendSms($recipient['phone'], $request->message);
                    if ($smsResult['success']) {
                        $sent++;
                    } else {
                        $failed++;
                    }
                } else {
                    $failed++;
                }
            }

            $results['sms'] = [
                'sent' => $sent,
                'failed' => $failed,
                'total' => count($request->recipients),
            ];
        }

        return response()->json([
            'success' => true,
            'message' => 'Campaign sent successfully',
            'data' => $results
        ]);
    }

    /**
     * Send event update notification
     */
    public function sendEventUpdate(Request $request, Event $event)
    {
        $validator = Validator::make($request->all(), [
            'update_message' => 'required|string|max:1000',
            'include_email' => 'boolean',
            'include_sms' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get all confirmed bookings for this event
        $bookings = Booking::where('event_id', $event->id)
                          ->whereIn('status', ['confirmed', 'pending'])
                          ->with('user')
                          ->get();

        if ($bookings->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No bookings found for this event'
            ], 422);
        }

        $attendees = $bookings->map(function ($booking) {
            return [
                'name' => $booking->customer_info['name'] ?? $booking->user->name,
                'email' => $booking->customer_info['email'] ?? $booking->user->email,
                'phone' => $booking->customer_info['phone'] ?? $booking->user->phone,
            ];
        })->toArray();

        $results = [];

        // Send email updates
        if ($request->boolean('include_email', true)) {
            $emailResult = $this->emailService->sendEventUpdate($event, $attendees, $request->update_message);
            $results['email'] = $emailResult;
        }

        // Send SMS updates
        if ($request->boolean('include_sms', false)) {
            $message = "📢 Event Update\n\n";
            $message .= "Event: {$event->title}\n";
            $message .= "Update: {$request->update_message}\n\n";
            $message .= "Event Manager";

            $sent = 0;
            $failed = 0;

            foreach ($attendees as $attendee) {
                if (!empty($attendee['phone'])) {
                    $smsResult = $this->smsService->sendSms($attendee['phone'], $message);
                    if ($smsResult['success']) {
                        $sent++;
                    } else {
                        $failed++;
                    }
                } else {
                    $failed++;
                }
            }

            $results['sms'] = [
                'sent' => $sent,
                'failed' => $failed,
                'total' => count($attendees),
            ];
        }

        return response()->json([
            'success' => true,
            'message' => 'Event update notifications sent successfully',
            'data' => $results
        ]);
    }

    /**
     * Test email configuration
     */
    public function testEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $success = $this->emailService->testEmailConfiguration($request->email);

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Test email sent successfully' : 'Failed to send test email'
        ]);
    }

    /**
     * Test SMS configuration
     */
    public function testSms(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $success = $this->smsService->testSmsConfiguration($request->phone);

        return response()->json([
            'success' => $success,
            'message' => $success ? 'Test SMS sent successfully' : 'Failed to send test SMS'
        ]);
    }

    /**
     * Get available email templates
     */
    public function getEmailTemplates()
    {
        $templates = $this->emailService->getAvailableTemplates();

        return response()->json([
            'success' => true,
            'data' => $templates
        ]);
    }

    /**
     * Get notification statistics
     */
    public function getStatistics(Request $request)
    {
        $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', now()->format('Y-m-d'));

        // This would typically come from a notifications log table
        // For now, return mock data
        $stats = [
            'email' => [
                'sent' => 1250,
                'delivered' => 1180,
                'opened' => 890,
                'clicked' => 340,
                'bounced' => 25,
                'failed' => 45,
            ],
            'sms' => [
                'sent' => 850,
                'delivered' => 820,
                'failed' => 30,
            ],
            'campaigns' => [
                'total' => 15,
                'active' => 3,
                'completed' => 12,
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
