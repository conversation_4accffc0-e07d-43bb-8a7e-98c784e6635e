<template>
  <AdminLayout>
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Notifications & Communications</h1>
          <p class="text-gray-600">Manage email templates, SMS campaigns, and push notifications</p>
        </div>
        <div class="flex space-x-3">
          <button @click="showCreateTemplate = true" 
                  class="bg-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-indigo-700 transition-colors">
            Create Template
          </button>
          <button @click="sendBulkNotification" 
                  class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors">
            Send Bulk Notification
          </button>
        </div>
      </div>

      <!-- Notification Tabs -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8 px-6">
            <button v-for="tab in notificationTabs" :key="tab.key"
                    @click="activeTab = tab.key"
                    :class="[
                      activeTab === tab.key 
                        ? 'border-indigo-500 text-indigo-600' 
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                      'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm'
                    ]">
              {{ tab.name }}
              <span v-if="tab.count" 
                    class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium">
                {{ tab.count }}
              </span>
            </button>
          </nav>
        </div>

        <!-- Email Templates -->
        <div v-if="activeTab === 'email'" class="p-6">
          <div class="space-y-6">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium text-gray-900">Email Templates</h3>
              <button @click="createEmailTemplate" 
                      class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                New Email Template
              </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div v-for="template in emailTemplates" :key="template.id" 
                   class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div class="flex justify-between items-start mb-3">
                  <h4 class="text-md font-medium text-gray-900">{{ template.name }}</h4>
                  <span :class="template.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                    {{ template.status }}
                  </span>
                </div>
                <p class="text-sm text-gray-600 mb-4">{{ template.description }}</p>
                <div class="flex justify-between items-center text-xs text-gray-500 mb-4">
                  <span>Used {{ template.usage_count }} times</span>
                  <span>{{ formatDate(template.updated_at) }}</span>
                </div>
                <div class="flex space-x-2">
                  <button @click="editTemplate(template)" 
                          class="flex-1 bg-indigo-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-indigo-700">
                    Edit
                  </button>
                  <button @click="previewTemplate(template)" 
                          class="flex-1 bg-gray-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-gray-700">
                    Preview
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- SMS Templates -->
        <div v-if="activeTab === 'sms'" class="p-6">
          <div class="space-y-6">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium text-gray-900">SMS Templates</h3>
              <button @click="createSMSTemplate" 
                      class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                New SMS Template
              </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div v-for="template in smsTemplates" :key="template.id" 
                   class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start mb-3">
                  <h4 class="text-md font-medium text-gray-900">{{ template.name }}</h4>
                  <span class="text-xs text-gray-500">{{ template.message.length }}/160 chars</span>
                </div>
                <div class="bg-gray-50 p-3 rounded-md mb-4">
                  <p class="text-sm text-gray-800">{{ template.message }}</p>
                </div>
                <div class="flex justify-between items-center text-xs text-gray-500 mb-4">
                  <span>Sent {{ template.sent_count }} times</span>
                  <span>{{ formatDate(template.updated_at) }}</span>
                </div>
                <div class="flex space-x-2">
                  <button @click="editSMSTemplate(template)" 
                          class="flex-1 bg-green-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-green-700">
                    Edit
                  </button>
                  <button @click="testSMS(template)" 
                          class="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-blue-700">
                    Test Send
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Push Notifications -->
        <div v-if="activeTab === 'push'" class="p-6">
          <div class="space-y-6">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium text-gray-900">Push Notifications</h3>
              <button @click="sendPushNotification" 
                      class="bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700">
                Send Push Notification
              </button>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div class="flex">
                <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
                <div>
                  <h4 class="text-sm font-medium text-yellow-800">Push Notification Setup Required</h4>
                  <p class="text-sm text-yellow-700 mt-1">Configure Firebase Cloud Messaging in Settings to enable push notifications.</p>
                </div>
              </div>
            </div>

            <div class="space-y-4">
              <div v-for="notification in pushNotifications" :key="notification.id" 
                   class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h4 class="text-md font-medium text-gray-900">{{ notification.title }}</h4>
                    <p class="text-sm text-gray-600 mt-1">{{ notification.message }}</p>
                    <div class="flex items-center mt-3 text-xs text-gray-500">
                      <span>Sent to {{ notification.recipients }} users</span>
                      <span class="mx-2">•</span>
                      <span>{{ formatDate(notification.sent_at) }}</span>
                      <span class="mx-2">•</span>
                      <span>{{ notification.click_rate }}% click rate</span>
                    </div>
                  </div>
                  <span :class="notification.status === 'sent' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                    {{ notification.status }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Campaign History -->
        <div v-if="activeTab === 'campaigns'" class="p-6">
          <div class="space-y-6">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium text-gray-900">Campaign History</h3>
              <button @click="createCampaign" 
                      class="bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-orange-700">
                Create Campaign
              </button>
            </div>

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recipients</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sent</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Open Rate</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="campaign in campaigns" :key="campaign.id" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div class="text-sm font-medium text-gray-900">{{ campaign.name }}</div>
                        <div class="text-sm text-gray-500">{{ campaign.subject }}</div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ campaign.type }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ campaign.recipients.toLocaleString() }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ formatDate(campaign.sent_at) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {{ campaign.open_rate }}%
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span :class="getCampaignStatusClass(campaign.status)" 
                            class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                        {{ campaign.status }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button @click="viewCampaignDetails(campaign)" 
                              class="text-indigo-600 hover:text-indigo-900 mr-3">View</button>
                      <button @click="duplicateCampaign(campaign)" 
                              class="text-green-600 hover:text-green-900">Duplicate</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Create Template Modal -->
      <div v-if="showCreateTemplate" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Create New Template</h3>
            <form @submit.prevent="createTemplate">
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Template Type</label>
                  <select v-model="newTemplate.type" required 
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">Select Type</option>
                    <option value="email">Email Template</option>
                    <option value="sms">SMS Template</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
                  <input v-model="newTemplate.name" type="text" required 
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea v-model="newTemplate.description" rows="3" 
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
                </div>
              </div>
              <div class="flex justify-end space-x-3 mt-6">
                <button type="button" @click="showCreateTemplate = false" 
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                  Cancel
                </button>
                <button type="submit" 
                        class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700">
                  Create Template
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import AdminLayout from '@/components/admin/AdminLayout.vue'

const activeTab = ref('email')
const showCreateTemplate = ref(false)

const notificationTabs = ref([
  { key: 'email', name: 'Email Templates', count: 8 },
  { key: 'sms', name: 'SMS Templates', count: 5 },
  { key: 'push', name: 'Push Notifications', count: 12 },
  { key: 'campaigns', name: 'Campaign History', count: 24 }
])

const newTemplate = reactive({
  type: '',
  name: '',
  description: ''
})

const emailTemplates = ref([
  {
    id: 1,
    name: 'Booking Confirmation',
    description: 'Sent when a user successfully books tickets',
    status: 'active',
    usage_count: 1247,
    updated_at: '2025-01-10T10:30:00Z'
  },
  {
    id: 2,
    name: 'Event Reminder',
    description: 'Reminder sent 24 hours before event',
    status: 'active',
    usage_count: 892,
    updated_at: '2025-01-08T15:45:00Z'
  },
  {
    id: 3,
    name: 'Ticket Cancellation',
    description: 'Confirmation of ticket cancellation',
    status: 'active',
    usage_count: 156,
    updated_at: '2025-01-05T09:20:00Z'
  }
])

const smsTemplates = ref([
  {
    id: 1,
    name: 'Booking Confirmation',
    message: 'Hi {name}, your booking for {event} is confirmed! Ref: {booking_ref}. Show this SMS at entry.',
    sent_count: 1247,
    updated_at: '2025-01-10T10:30:00Z'
  },
  {
    id: 2,
    name: 'Event Reminder',
    message: 'Reminder: {event} starts tomorrow at {time}. Venue: {venue}. See you there!',
    sent_count: 892,
    updated_at: '2025-01-08T15:45:00Z'
  }
])

const pushNotifications = ref([
  {
    id: 1,
    title: 'New Event Alert',
    message: 'Tech Conference 2025 tickets are now available!',
    recipients: 5420,
    click_rate: 12.5,
    status: 'sent',
    sent_at: '2025-01-12T09:00:00Z'
  },
  {
    id: 2,
    title: 'Event Reminder',
    message: 'Your event starts in 2 hours. Don\'t forget!',
    recipients: 1247,
    click_rate: 8.3,
    status: 'sent',
    sent_at: '2025-01-11T14:00:00Z'
  }
])

const campaigns = ref([
  {
    id: 1,
    name: 'January Newsletter',
    subject: 'Exciting Events This Month!',
    type: 'Email',
    recipients: 15420,
    open_rate: 24.5,
    status: 'completed',
    sent_at: '2025-01-01T10:00:00Z'
  },
  {
    id: 2,
    name: 'Event Reminder Campaign',
    subject: 'Don\'t Miss These Upcoming Events',
    type: 'Email',
    recipients: 8930,
    open_rate: 31.2,
    status: 'completed',
    sent_at: '2024-12-28T15:30:00Z'
  }
])

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getCampaignStatusClass = (status) => {
  const classes = {
    completed: 'bg-green-100 text-green-800',
    scheduled: 'bg-blue-100 text-blue-800',
    draft: 'bg-yellow-100 text-yellow-800',
    failed: 'bg-red-100 text-red-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const createTemplate = () => {
  // Create new template
  showCreateTemplate.value = false
  alert(`${newTemplate.type} template "${newTemplate.name}" created!`)
  
  // Reset form
  Object.assign(newTemplate, {
    type: '',
    name: '',
    description: ''
  })
}

const editTemplate = (template) => {
  alert(`Editing template: ${template.name}`)
}

const previewTemplate = (template) => {
  alert(`Previewing template: ${template.name}`)
}

const createEmailTemplate = () => {
  newTemplate.type = 'email'
  showCreateTemplate.value = true
}

const createSMSTemplate = () => {
  newTemplate.type = 'sms'
  showCreateTemplate.value = true
}

const editSMSTemplate = (template) => {
  alert(`Editing SMS template: ${template.name}`)
}

const testSMS = (template) => {
  alert(`Sending test SMS: ${template.name}`)
}

const sendPushNotification = () => {
  alert('Opening push notification composer...')
}

const createCampaign = () => {
  alert('Opening campaign creator...')
}

const sendBulkNotification = () => {
  alert('Opening bulk notification sender...')
}

const viewCampaignDetails = (campaign) => {
  alert(`Viewing campaign: ${campaign.name}`)
}

const duplicateCampaign = (campaign) => {
  alert(`Duplicating campaign: ${campaign.name}`)
}
</script>
