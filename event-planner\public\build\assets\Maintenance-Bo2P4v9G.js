import{r as n,B as H,l as G,w as P,h as e,t as l,x as i,z as u,A as f,f as r,k as g,F as p,g as d,n as Z}from"./vue-vendor-BupLktX_.js";import{_ as I}from"./admin-9F2yeZU0.js";import"./chart-vendor-Db3utXXw.js";const Y={class:"space-y-6"},K={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},N={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},Q={class:"flex items-center"},W={class:"ml-4"},J={class:"text-2xl font-bold text-gray-900"},X={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},T={class:"flex items-center"},ee={class:"ml-4"},te={class:"text-2xl font-bold text-gray-900"},se={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},oe={class:"flex items-center"},le={class:"ml-4"},ae={class:"text-2xl font-bold text-gray-900"},ne={class:"bg-white shadow-sm rounded-lg border border-gray-200"},de={class:"p-6"},ie={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},re={class:"space-y-4"},ue={class:"flex items-center"},ce={class:"flex items-center"},me={class:"space-y-3"},ge={class:"text-sm font-medium text-gray-900"},pe={class:"text-xs text-gray-500"},xe={class:"flex space-x-2"},be=["onClick"],ve=["onClick"],ye={class:"bg-white shadow-sm rounded-lg border border-gray-200"},fe={class:"p-6"},he={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},ke={class:"flex items-center mb-3"},we={class:"text-md font-medium text-gray-900"},Ce={class:"text-sm text-gray-500"},_e={class:"space-y-3"},Be=["onUpdate:modelValue"],De=["value"],Me=["onUpdate:modelValue"],ze=["onClick"],Se={class:"bg-white shadow-sm rounded-lg border border-gray-200"},Re={class:"p-6"},Ue={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},je={class:"space-y-3"},Ve={class:"flex justify-between items-center p-3 border border-gray-200 rounded-lg"},Ae={class:"text-xs text-gray-500"},$e={class:"flex justify-between items-center p-3 border border-gray-200 rounded-lg"},Le={class:"text-xs text-gray-500"},Fe={class:"flex justify-between items-center p-3 border border-gray-200 rounded-lg"},Oe={class:"text-xs text-gray-500"},Pe={__name:"Maintenance",setup(Ee){const h=n("2.3 GB"),k=n("2 hours ago"),w=n("15 days"),C=n("45 MB"),_=n("128 MB"),B=n("1.2 GB"),D=n("3 days ago"),x=n("daily"),b=n("30"),v=n(!0),y=n(!1),c=n([{id:1,filename:"backup_2025-01-12_02-00.sql.gz",size:45678901,created_at:"2025-01-12T02:00:00Z"},{id:2,filename:"backup_2025-01-11_02-00.sql.gz",size:44567890,created_at:"2025-01-11T02:00:00Z"},{id:3,filename:"backup_2025-01-10_02-00.sql.gz",size:43456789,created_at:"2025-01-10T02:00:00Z"}]),M=H([{key:"events",name:"Events Data",description:"All event information and details",color:"bg-blue-500",formats:["csv","json","xlsx"],format:"csv",dateRange:"30d"},{key:"bookings",name:"Bookings Data",description:"Booking records and transactions",color:"bg-green-500",formats:["csv","json","xlsx"],format:"csv",dateRange:"30d"},{key:"users",name:"User Data",description:"User profiles and information",color:"bg-purple-500",formats:["csv","json"],format:"csv",dateRange:"all"},{key:"analytics",name:"Analytics Data",description:"Performance metrics and statistics",color:"bg-orange-500",formats:["csv","json","xlsx"],format:"xlsx",dateRange:"90d"},{key:"financial",name:"Financial Reports",description:"Revenue and payment data",color:"bg-red-500",formats:["csv","xlsx","pdf"],format:"xlsx",dateRange:"1y"},{key:"logs",name:"System Logs",description:"Application and error logs",color:"bg-gray-500",formats:["txt","json"],format:"txt",dateRange:"30d"}]),z=o=>{if(o===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB"],a=Math.floor(Math.log(o)/Math.log(t));return parseFloat((o/Math.pow(t,a)).toFixed(2))+" "+s[a]},S=o=>new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),R=()=>{alert("Running comprehensive system check...")},U=()=>{alert("Creating database backup...")},j=()=>{alert("Backup settings saved successfully!")},V=o=>{alert(`Downloading backup: ${o.filename}`)},A=o=>{if(confirm(`Delete backup ${o.filename}?`)){const t=c.value.findIndex(s=>s.id===o.id);t>-1&&c.value.splice(t,1)}},$=o=>{alert(`Exporting ${o.name} as ${o.format.toUpperCase()}...`)},m=o=>{alert(`Clearing ${o} cache...`)},L=()=>{confirm("Clear all cache? This may temporarily slow down the system.")&&alert("All cache cleared successfully!")},F=()=>{alert("Optimizing database tables...")},O=()=>{confirm("Delete logs older than 90 days?")&&alert("Old logs cleaned successfully!")},E=()=>{alert("Updating database statistics...")},q=()=>{confirm("Run full system maintenance? This may take several minutes.")&&alert("Full maintenance started. You will be notified when complete.")};return(o,t)=>(d(),G(I,null,{default:P(()=>[e("div",Y,[e("div",{class:"flex justify-between items-center"},[t[7]||(t[7]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"System Maintenance"),e("p",{class:"text-gray-600"},"Database backups, data exports, and system maintenance tools")],-1)),e("div",{class:"flex space-x-3"},[e("button",{onClick:R,class:"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"}," System Check "),e("button",{onClick:U,class:"bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors"}," Create Backup ")])]),e("div",K,[t[14]||(t[14]=e("div",{class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},[e("div",{class:"flex items-center"},[e("div",{class:"p-2 bg-green-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"})])]),e("div",{class:"ml-4"},[e("p",{class:"text-sm font-medium text-gray-600"},"Database"),e("p",{class:"text-2xl font-bold text-green-600"},"Healthy")])])],-1)),e("div",N,[e("div",Q,[t[9]||(t[9]=e("div",{class:"p-2 bg-blue-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v4a2 2 0 01-2 2H5z"})])],-1)),e("div",W,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-600"},"Storage",-1)),e("p",J,l(h.value),1)])])]),e("div",X,[e("div",T,[t[11]||(t[11]=e("div",{class:"p-2 bg-purple-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),e("div",ee,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-600"},"Last Backup",-1)),e("p",te,l(k.value),1)])])]),e("div",se,[e("div",oe,[t[13]||(t[13]=e("div",{class:"p-2 bg-orange-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("div",le,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-600"},"Uptime",-1)),e("p",ae,l(w.value),1)])])])]),e("div",ne,[t[23]||(t[23]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Database Backups")],-1)),e("div",de,[e("div",ie,[e("div",null,[t[21]||(t[21]=e("h4",{class:"text-md font-medium text-gray-900 mb-4"},"Backup Settings",-1)),e("div",re,[e("div",null,[t[16]||(t[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Automatic Backup Schedule",-1)),i(e("select",{"onUpdate:modelValue":t[0]||(t[0]=s=>x.value=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[15]||(t[15]=[e("option",{value:"disabled"},"Disabled",-1),e("option",{value:"daily"},"Daily at 2:00 AM",-1),e("option",{value:"weekly"},"Weekly (Sunday 2:00 AM)",-1),e("option",{value:"monthly"},"Monthly (1st day 2:00 AM)",-1)]),512),[[u,x.value]])]),e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Retention Period",-1)),i(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>b.value=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[17]||(t[17]=[e("option",{value:"7"},"7 days",-1),e("option",{value:"30"},"30 days",-1),e("option",{value:"90"},"90 days",-1),e("option",{value:"365"},"1 year",-1)]),512),[[u,b.value]])]),e("div",ue,[i(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>v.value=s),type:"checkbox",class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[f,v.value]]),t[19]||(t[19]=e("label",{class:"ml-2 text-sm text-gray-700"},"Compress backups",-1))]),e("div",ce,[i(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>y.value=s),type:"checkbox",class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[f,y.value]]),t[20]||(t[20]=e("label",{class:"ml-2 text-sm text-gray-700"},"Encrypt backups",-1))]),e("button",{onClick:j,class:"w-full bg-indigo-600 text-white px-4 py-2 rounded-md font-medium hover:bg-indigo-700"}," Save Settings ")])]),e("div",null,[t[22]||(t[22]=e("h4",{class:"text-md font-medium text-gray-900 mb-4"},"Recent Backups",-1)),e("div",me,[(d(!0),r(p,null,g(c.value,s=>(d(),r("div",{key:s.id,class:"flex items-center justify-between p-3 border border-gray-200 rounded-lg"},[e("div",null,[e("p",ge,l(s.filename),1),e("p",pe,l(S(s.created_at))+" • "+l(z(s.size)),1)]),e("div",xe,[e("button",{onClick:a=>V(s),class:"text-indigo-600 hover:text-indigo-900 text-sm"},"Download",8,be),e("button",{onClick:a=>A(s),class:"text-red-600 hover:text-red-900 text-sm"},"Delete",8,ve)])]))),128))])])])])]),e("div",ye,[t[28]||(t[28]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Data Export")],-1)),e("div",fe,[e("div",he,[(d(!0),r(p,null,g(M,s=>(d(),r("div",{key:s.key,class:"border border-gray-200 rounded-lg p-4"},[e("div",ke,[e("div",{class:Z([s.color,"p-2 rounded-lg mr-3"])},t[24]||(t[24]=[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)]),2),e("div",null,[e("h4",we,l(s.name),1),e("p",Ce,l(s.description),1)])]),e("div",_e,[e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Format",-1)),i(e("select",{"onUpdate:modelValue":a=>s.format=a,class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},[(d(!0),r(p,null,g(s.formats,a=>(d(),r("option",{key:a,value:a},l(a.toUpperCase()),9,De))),128))],8,Be),[[u,s.format]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Date Range",-1)),i(e("select",{"onUpdate:modelValue":a=>s.dateRange=a,class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[26]||(t[26]=[e("option",{value:"all"},"All Time",-1),e("option",{value:"30d"},"Last 30 Days",-1),e("option",{value:"90d"},"Last 90 Days",-1),e("option",{value:"1y"},"Last Year",-1),e("option",{value:"custom"},"Custom Range",-1)]),8,Me),[[u,s.dateRange]])]),e("button",{onClick:a=>$(s),class:"w-full bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," Export "+l(s.name),9,ze)])]))),128))])])]),e("div",Se,[t[37]||(t[37]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"System Maintenance")],-1)),e("div",Re,[e("div",Ue,[e("div",null,[t[32]||(t[32]=e("h4",{class:"text-md font-medium text-gray-900 mb-4"},"Cache Management",-1)),e("div",je,[e("div",Ve,[e("div",null,[t[29]||(t[29]=e("p",{class:"text-sm font-medium text-gray-900"},"Application Cache",-1)),e("p",Ae,l(C.value)+" • Last cleared "+l(D.value),1)]),e("button",{onClick:t[4]||(t[4]=s=>m("app")),class:"bg-yellow-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-yellow-700"}," Clear ")]),e("div",$e,[e("div",null,[t[30]||(t[30]=e("p",{class:"text-sm font-medium text-gray-900"},"Database Query Cache",-1)),e("p",Le,l(_.value)+" • Auto-managed",1)]),e("button",{onClick:t[5]||(t[5]=s=>m("db")),class:"bg-yellow-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-yellow-700"}," Clear ")]),e("div",Fe,[e("div",null,[t[31]||(t[31]=e("p",{class:"text-sm font-medium text-gray-900"},"Image Cache",-1)),e("p",Oe,l(B.value)+" • Thumbnails & resized images",1)]),e("button",{onClick:t[6]||(t[6]=s=>m("images")),class:"bg-yellow-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-yellow-700"}," Clear ")]),e("button",{onClick:L,class:"w-full bg-red-600 text-white px-4 py-2 rounded-md font-medium hover:bg-red-700"}," Clear All Cache ")])]),e("div",null,[t[36]||(t[36]=e("h4",{class:"text-md font-medium text-gray-900 mb-4"},"Database Optimization",-1)),e("div",{class:"space-y-3"},[e("div",{class:"flex justify-between items-center p-3 border border-gray-200 rounded-lg"},[t[33]||(t[33]=e("div",null,[e("p",{class:"text-sm font-medium text-gray-900"},"Optimize Tables"),e("p",{class:"text-xs text-gray-500"},"Defragment and optimize database tables")],-1)),e("button",{onClick:F,class:"bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-blue-700"}," Optimize ")]),e("div",{class:"flex justify-between items-center p-3 border border-gray-200 rounded-lg"},[t[34]||(t[34]=e("div",null,[e("p",{class:"text-sm font-medium text-gray-900"},"Clean Old Logs"),e("p",{class:"text-xs text-gray-500"},"Remove logs older than 90 days")],-1)),e("button",{onClick:O,class:"bg-orange-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-orange-700"}," Clean ")]),e("div",{class:"flex justify-between items-center p-3 border border-gray-200 rounded-lg"},[t[35]||(t[35]=e("div",null,[e("p",{class:"text-sm font-medium text-gray-900"},"Update Statistics"),e("p",{class:"text-xs text-gray-500"},"Refresh database query statistics")],-1)),e("button",{onClick:E,class:"bg-purple-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-purple-700"}," Update ")]),e("button",{onClick:q,class:"w-full bg-green-600 text-white px-4 py-2 rounded-md font-medium hover:bg-green-700"}," Run Full Maintenance ")])])])])])])]),_:1}))}};export{Pe as default};
