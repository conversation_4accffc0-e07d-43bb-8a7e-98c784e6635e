<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center min-h-screen">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
    </div>

    <!-- Booking Form -->
    <div v-else class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="mb-8">
        <nav class="mb-4">
          <ol class="flex items-center space-x-2 text-sm text-gray-500">
            <li><router-link to="/" class="hover:text-indigo-600">Home</router-link></li>
            <li><span class="mx-2">/</span></li>
            <li><router-link to="/events" class="hover:text-indigo-600">Events</router-link></li>
            <li><span class="mx-2">/</span></li>
            <li><router-link :to="`/events/${event?.slug || event?.id}`" class="hover:text-indigo-600">{{ event?.title }}</router-link></li>
            <li><span class="mx-2">/</span></li>
            <li class="text-indigo-600">Booking</li>
          </ol>
        </nav>
        <h1 class="text-3xl font-bold text-gray-900">Complete Your Booking</h1>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Booking Form -->
        <div class="lg:col-span-2">
          <!-- Step Indicator -->
          <div class="mb-8">
            <div class="flex items-center">
              <div class="flex items-center text-sm">
                <div :class="currentStep >= 1 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-600'" 
                     class="w-8 h-8 rounded-full flex items-center justify-center font-medium">
                  1
                </div>
                <span class="ml-2 text-gray-600">Details</span>
              </div>
              <div class="flex-1 h-px bg-gray-200 mx-4"></div>
              <div class="flex items-center text-sm">
                <div :class="currentStep >= 2 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-600'" 
                     class="w-8 h-8 rounded-full flex items-center justify-center font-medium">
                  2
                </div>
                <span class="ml-2 text-gray-600">Payment</span>
              </div>
              <div class="flex-1 h-px bg-gray-200 mx-4"></div>
              <div class="flex items-center text-sm">
                <div :class="currentStep >= 3 ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-600'" 
                     class="w-8 h-8 rounded-full flex items-center justify-center font-medium">
                  3
                </div>
                <span class="ml-2 text-gray-600">Confirmation</span>
              </div>
            </div>
          </div>

          <!-- Step 1: Booking Details -->
          <div v-if="currentStep === 1" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Booking Details</h2>
            
            <form @submit.prevent="proceedToPayment">
              <!-- Ticket Selection -->
              <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-3">Select Ticket Type</label>
                <div class="space-y-3">
                  <div v-for="ticket in ticketTypes" :key="ticket.id" 
                       class="border border-gray-200 rounded-lg p-4 hover:border-indigo-300 transition-colors">
                    <label class="flex items-center cursor-pointer">
                      <input v-model="bookingForm.ticket_type_id" :value="ticket.id" type="radio" 
                             class="text-indigo-600 focus:ring-indigo-500">
                      <div class="ml-3 flex-1">
                        <div class="flex justify-between items-start">
                          <div>
                            <div class="font-medium text-gray-900">{{ ticket.name }}</div>
                            <div v-if="ticket.description" class="text-sm text-gray-600">{{ ticket.description }}</div>
                            <div class="text-sm text-gray-500 mt-1">
                              {{ ticket.quantity_remaining || 'Available' }} tickets remaining
                            </div>
                          </div>
                          <div class="text-lg font-semibold text-gray-900">
                            {{ formatPrice(ticket.price) }}
                          </div>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              <!-- Quantity -->
              <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                <select v-model="bookingForm.quantity" 
                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  <option v-for="i in 10" :key="i" :value="i">{{ i }} {{ i === 1 ? 'ticket' : 'tickets' }}</option>
                </select>
              </div>

              <!-- Attendee Information -->
              <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Attendee Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                    <input v-model="bookingForm.attendee_name" type="text" required 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                    <input v-model="bookingForm.attendee_email" type="email" required 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input v-model="bookingForm.attendee_phone" type="tel" 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Organization (Optional)</label>
                    <input v-model="bookingForm.organization" type="text" 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                </div>
              </div>

              <!-- Special Requirements -->
              <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Special Requirements (Optional)</label>
                <textarea v-model="bookingForm.special_requirements" rows="3" 
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                          placeholder="Any dietary restrictions, accessibility needs, or other requirements..."></textarea>
              </div>

              <!-- Terms and Conditions -->
              <div class="mb-6">
                <label class="flex items-start">
                  <input v-model="bookingForm.terms_accepted" type="checkbox" required 
                         class="mt-1 text-indigo-600 focus:ring-indigo-500">
                  <span class="ml-2 text-sm text-gray-600">
                    I agree to the <a href="#" class="text-indigo-600 hover:text-indigo-500">Terms and Conditions</a> 
                    and <a href="#" class="text-indigo-600 hover:text-indigo-500">Privacy Policy</a>
                  </span>
                </label>
              </div>

              <div class="flex justify-between">
                <router-link :to="`/events/${event?.slug || event?.id}`" 
                             class="bg-gray-200 text-gray-700 px-6 py-2 rounded-md font-medium hover:bg-gray-300 transition-colors">
                  Back to Event
                </router-link>
                <button type="submit" :disabled="!isFormValid" 
                        class="bg-indigo-600 text-white px-6 py-2 rounded-md font-medium hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
                  Continue to Payment
                </button>
              </div>
            </form>
          </div>

          <!-- Step 2: Payment -->
          <div v-if="currentStep === 2" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">Payment Information</h2>
            
            <!-- Payment Processing -->
            <div v-if="bookingStore.paymentStatus === 'processing'" class="text-center py-8">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
              <p class="text-gray-600">Processing your payment...</p>
            </div>

            <!-- Payment Form -->
            <form v-else @submit.prevent="processPayment">
              <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Card Number *</label>
                <input v-model="paymentForm.cardNumber" type="text" required 
                       placeholder="1234 5678 9012 3456"
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <p class="text-xs text-gray-500 mt-1">Use 4000 0000 0000 0002 to simulate payment failure</p>
              </div>

              <div class="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Expiry Date *</label>
                  <input v-model="paymentForm.expiryDate" type="text" required 
                         placeholder="MM/YY"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">CVV *</label>
                  <input v-model="paymentForm.cvv" type="text" required 
                         placeholder="123"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
              </div>

              <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Cardholder Name *</label>
                <input v-model="paymentForm.cardholderName" type="text" required 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              </div>

              <!-- Billing Address -->
              <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Billing Address</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Address *</label>
                    <input v-model="paymentForm.billingAddress" type="text" required 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">City *</label>
                    <input v-model="paymentForm.city" type="text" required 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Postal Code *</label>
                    <input v-model="paymentForm.postalCode" type="text" required 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                </div>
              </div>

              <!-- Error Message -->
              <div v-if="bookingStore.error" class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                  <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                  <div class="ml-3">
                    <p class="text-sm text-red-800">{{ bookingStore.error }}</p>
                  </div>
                </div>
              </div>

              <div class="flex justify-between">
                <button type="button" @click="currentStep = 1" 
                        class="bg-gray-200 text-gray-700 px-6 py-2 rounded-md font-medium hover:bg-gray-300 transition-colors">
                  Back
                </button>
                <button type="submit" :disabled="bookingStore.isLoading" 
                        class="bg-indigo-600 text-white px-6 py-2 rounded-md font-medium hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
                  {{ bookingStore.isLoading ? 'Processing...' : `Pay ${formatPrice(totalAmount)}` }}
                </button>
              </div>
            </form>
          </div>

          <!-- Step 3: Confirmation -->
          <div v-if="currentStep === 3" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="text-center py-8">
              <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-gray-900 mb-2">Booking Confirmed!</h2>
              <p class="text-gray-600 mb-6">Your booking has been successfully processed.</p>
              
              <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <div class="text-sm text-gray-600 mb-2">Booking Reference</div>
                <div class="text-lg font-mono font-semibold text-gray-900">{{ bookingStore.currentBooking?.booking_reference }}</div>
              </div>

              <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button @click="downloadTicket" 
                        class="bg-indigo-600 text-white px-6 py-2 rounded-md font-medium hover:bg-indigo-700 transition-colors">
                  Download Ticket
                </button>
                <router-link to="/my-tickets" 
                             class="bg-gray-200 text-gray-700 px-6 py-2 rounded-md font-medium hover:bg-gray-300 transition-colors">
                  View My Tickets
                </router-link>
              </div>
            </div>
          </div>
        </div>

        <!-- Booking Summary -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Booking Summary</h3>
            
            <!-- Event Info -->
            <div v-if="event" class="mb-6">
              <div class="flex">
                <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-4">
                  <span class="text-white text-xl font-bold">{{ event.title.charAt(0) }}</span>
                </div>
                <div class="flex-1">
                  <h4 class="font-medium text-gray-900">{{ event.title }}</h4>
                  <p class="text-sm text-gray-600">{{ formatDate(event.start_date) }}</p>
                  <p class="text-sm text-gray-600">{{ event.venue_name }}</p>
                </div>
              </div>
            </div>

            <!-- Selected Ticket -->
            <div v-if="selectedTicket" class="mb-6 p-3 bg-gray-50 rounded-lg">
              <div class="font-medium text-gray-900">{{ selectedTicket.name }}</div>
              <div class="text-sm text-gray-600">{{ formatPrice(selectedTicket.price) }} × {{ bookingForm.quantity }}</div>
            </div>

            <!-- Price Breakdown -->
            <div class="space-y-2 mb-6">
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Subtotal</span>
                <span class="text-gray-900">{{ formatPrice(subtotal) }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-600">Service Fee</span>
                <span class="text-gray-900">{{ formatPrice(serviceFee) }}</span>
              </div>
              <div class="border-t border-gray-200 pt-2">
                <div class="flex justify-between font-semibold">
                  <span class="text-gray-900">Total</span>
                  <span class="text-gray-900">{{ formatPrice(totalAmount) }}</span>
                </div>
              </div>
            </div>

            <!-- Security Notice -->
            <div class="text-xs text-gray-500 text-center">
              <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
              Secure payment powered by SSL encryption
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useBookingStore } from '@/stores/booking'
import { eventsAPI } from '@/services/api'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const bookingStore = useBookingStore()

const event = ref(null)
const isLoading = ref(true)
const currentStep = ref(1)

const bookingForm = reactive({
  ticket_type_id: null,
  quantity: 1,
  attendee_name: '',
  attendee_email: '',
  attendee_phone: '',
  organization: '',
  special_requirements: '',
  terms_accepted: false
})

const paymentForm = reactive({
  cardNumber: '',
  expiryDate: '',
  cvv: '',
  cardholderName: '',
  billingAddress: '',
  city: '',
  postalCode: ''
})

const ticketTypes = computed(() => {
  return event.value?.ticket_types || []
})

const selectedTicket = computed(() => {
  return ticketTypes.value.find(t => t.id === bookingForm.ticket_type_id)
})

const subtotal = computed(() => {
  if (!selectedTicket.value) return 0
  return selectedTicket.value.price * bookingForm.quantity
})

const serviceFee = computed(() => {
  return Math.round(subtotal.value * 0.05) // 5% service fee
})

const totalAmount = computed(() => {
  return subtotal.value + serviceFee.value
})

const isFormValid = computed(() => {
  return bookingForm.ticket_type_id && 
         bookingForm.attendee_name && 
         bookingForm.attendee_email && 
         bookingForm.terms_accepted
})

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatPrice = (price) => {
  if (price === 0) return 'Free'
  return `₹${price.toLocaleString()}`
}

const loadEvent = async () => {
  try {
    const eventId = route.params.eventId
    
    // For demo, use mock data
    if (eventId === '1' || eventId === 'tech-conference-2025') {
      event.value = {
        id: 1,
        title: 'Tech Conference 2025',
        slug: 'tech-conference-2025',
        start_date: '2025-02-15T09:00:00Z',
        venue_name: 'Convention Center',
        ticket_types: [
          {
            id: 1,
            name: 'Early Bird',
            description: 'Limited time offer with full access',
            price: 1999,
            quantity_remaining: 50
          },
          {
            id: 2,
            name: 'Regular',
            description: 'Standard conference access',
            price: 2500,
            quantity_remaining: 200
          },
          {
            id: 3,
            name: 'VIP',
            description: 'Premium access with networking dinner',
            price: 4999,
            quantity_remaining: 25
          }
        ]
      }
    }
  } catch (error) {
    console.error('Failed to load event:', error)
  } finally {
    isLoading.value = false
  }
}

const proceedToPayment = async () => {
  // Create booking
  const bookingData = {
    event_id: event.value.id,
    ticket_type_id: bookingForm.ticket_type_id,
    quantity: bookingForm.quantity,
    attendee_name: bookingForm.attendee_name,
    attendee_email: bookingForm.attendee_email,
    attendee_phone: bookingForm.attendee_phone,
    organization: bookingForm.organization,
    special_requirements: bookingForm.special_requirements,
    total_amount: totalAmount.value
  }

  const result = await bookingStore.createBooking(bookingData)
  
  if (result.success) {
    currentStep.value = 2
  }
}

const processPayment = async () => {
  const result = await bookingStore.simulatePayment(paymentForm)
  
  if (result.success) {
    currentStep.value = 3
  }
}

const downloadTicket = () => {
  // Simulate ticket download
  alert('Ticket download started! Check your downloads folder.')
}

// Pre-fill user info if logged in
watch(() => authStore.isAuthenticated, (isAuth) => {
  if (isAuth) {
    bookingForm.attendee_name = authStore.userName
    bookingForm.attendee_email = authStore.userEmail
  }
}, { immediate: true })

onMounted(() => {
  if (!authStore.isAuthenticated) {
    router.push(`/login?redirect=${route.fullPath}`)
    return
  }
  
  loadEvent()
})
</script>
