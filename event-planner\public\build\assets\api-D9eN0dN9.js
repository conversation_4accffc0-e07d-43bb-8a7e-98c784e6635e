import{a as n}from"./utils-vendor-Dq7h7Pqt.js";const t=n.create({baseURL:"/api",headers:{Accept:"application/json","Content-Type":"application/json"}});t.interceptors.request.use(e=>{const o=localStorage.getItem("auth_token");return o&&(e.headers.Authorization=`Bearer ${o}`),e},e=>Promise.reject(e));t.interceptors.response.use(e=>e,e=>{var o;return((o=e.response)==null?void 0:o.status)===401&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user"),window.location.href="/login"),Promise.reject(e)});const i={getAll:e=>t.get("/events",{params:e}),getFeatured:e=>t.get("/events/featured",{params:e}),getById:e=>t.get(`/events/${e}`),create:e=>t.post("/events",e),update:(e,o)=>t.put(`/events/${e}`,o),delete:e=>t.delete(`/events/${e}`),book:(e,o)=>t.post(`/events/${e}/book`,o),getBookings:e=>t.get(`/events/${e}/bookings`),uploadImage:(e,o)=>t.post(`/events/${e}/image`,o,{headers:{"Content-Type":"multipart/form-data"}})},r={create:e=>t.post("/bookings",e),getUserBookings:e=>t.get("/bookings",{params:e}),getById:e=>t.get(`/bookings/${e}`),cancel:(e,o="")=>t.post(`/bookings/${e}/cancel`,{reason:o}),processPayment:(e,o)=>t.post(`/bookings/${e}/payment`,o),downloadTicket:e=>t.get(`/bookings/${e}/ticket`,{responseType:"blob"}),adminList:e=>t.get("/admin/bookings",{params:e}),adminShow:e=>t.get(`/admin/bookings/${e}`),updateStatus:(e,o,s="")=>t.post(`/admin/bookings/${e}/status`,{status:o,notes:s}),checkIn:e=>t.post(`/admin/bookings/${e}/check-in`),bulkAction:(e,o)=>t.post("/admin/bookings/bulk-action",{action:e,booking_ids:o}),statistics:e=>t.get("/admin/bookings/statistics",{params:e})};export{r as b,i as e};
