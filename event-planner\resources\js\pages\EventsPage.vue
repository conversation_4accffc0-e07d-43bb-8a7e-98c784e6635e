<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
            Discover Amazing Events
          </h1>
          <p class="text-xl text-indigo-200 mb-8 max-w-2xl mx-auto">
            Find conferences, workshops, festivals, and more happening near you
          </p>

          <!-- Search Bar -->
          <div class="max-w-2xl mx-auto">
            <div class="relative">
              <input v-model="searchQuery"
                     @input="handleSearch"
                     type="text"
                     placeholder="Search events by name, category, or location..."
                     class="w-full px-6 py-4 pr-12 text-lg rounded-lg bg-white border border-gray-300 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
              <button class="absolute right-4 top-1/2 transform -translate-y-1/2">
                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-40">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col lg:flex-row gap-4 items-center justify-between">
          <!-- Filters -->
          <div class="flex flex-wrap gap-4 items-center">
            <!-- Category Filter -->
            <div class="relative">
              <select v-model="selectedCategory"
                      @change="loadEvents"
                      class="bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent appearance-none">
                <option value="">All Categories</option>
                <option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </option>
              </select>
              <svg class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>

            <!-- Date Filter -->
            <div class="relative">
              <select v-model="dateFilter"
                      @change="loadEvents"
                      class="bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent appearance-none">
                <option value="">All Dates</option>
                <option value="today">Today</option>
                <option value="tomorrow">Tomorrow</option>
                <option value="this_week">This Week</option>
                <option value="this_month">This Month</option>
                <option value="next_month">Next Month</option>
              </select>
              <svg class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>

            <!-- Price Filter -->
            <div class="relative">
              <select v-model="priceFilter"
                      @change="loadEvents"
                      class="bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent appearance-none">
                <option value="">All Prices</option>
                <option value="free">Free</option>
                <option value="under_1000">Under ₹1,000</option>
                <option value="1000_5000">₹1,000 - ₹5,000</option>
                <option value="over_5000">Over ₹5,000</option>
              </select>
              <svg class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>

          <!-- Sort and View Options -->
          <div class="flex items-center gap-4">
            <!-- Sort -->
            <div class="relative">
              <select v-model="sortBy"
                      @change="loadEvents"
                      class="bg-white border border-gray-300 rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent appearance-none">
                <option value="date">Sort by Date</option>
                <option value="price_low">Price: Low to High</option>
                <option value="price_high">Price: High to Low</option>
                <option value="popularity">Popularity</option>
                <option value="name">Name A-Z</option>
              </select>
              <svg class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>

            <!-- View Toggle -->
            <div class="flex border border-gray-300 rounded-lg">
              <button @click="viewMode = 'grid'"
                      :class="viewMode === 'grid' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-600'"
                      class="p-2 rounded-l-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                </svg>
              </button>
              <button @click="viewMode = 'list'"
                      :class="viewMode === 'list' ? 'bg-indigo-600 text-white' : 'bg-white text-gray-600'"
                      class="p-2 rounded-r-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Events Grid -->
    <section class="py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Results Info -->
        <div class="flex justify-between items-center mb-8">
          <div class="text-gray-400">
            <span v-if="!loading">{{ events.length }} events found</span>
            <span v-else>Loading events...</span>
          </div>
          <div class="flex items-center space-x-2">
            <button @click="viewMode = 'grid'"
                    :class="viewMode === 'grid' ? 'bg-pink-500 text-white' : 'bg-gray-700 text-gray-400'"
                    class="p-2 rounded-lg transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
              </svg>
            </button>
            <button @click="viewMode = 'list'"
                    :class="viewMode === 'list' ? 'bg-pink-500 text-white' : 'bg-gray-700 text-gray-400'"
                    class="p-2 rounded-lg transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="i in 6" :key="i" class="bg-gray-800 rounded-xl overflow-hidden animate-pulse">
            <div class="h-48 bg-gray-700"></div>
            <div class="p-6">
              <div class="h-4 bg-gray-700 rounded mb-2"></div>
              <div class="h-4 bg-gray-700 rounded w-3/4 mb-4"></div>
              <div class="h-8 bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>

        <!-- No Results -->
        <div v-else-if="events.length === 0" class="text-center py-20">
          <svg class="w-16 h-16 text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
          </svg>
          <h3 class="text-xl font-semibold text-gray-400 mb-2">No events found</h3>
          <p class="text-gray-500 mb-6">Try adjusting your search criteria or browse all events</p>
          <button @click="clearFilters" class="bg-gradient-to-r from-pink-500 to-cyan-500 text-white px-6 py-3 rounded-lg font-semibold">
            Clear Filters
          </button>
        </div>

        <!-- Events Grid View -->
        <div v-else-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <EventCard v-for="event in events" :key="event.id" :event="event" />
        </div>

        <!-- Events List View -->
        <div v-else class="space-y-6">
          <EventListItem v-for="event in events" :key="event.id" :event="event" />
        </div>

        <!-- Load More Button -->
        <div v-if="hasMore && !loading" class="text-center mt-12">
          <button @click="loadMore" 
                  :disabled="loadingMore"
                  class="bg-gray-700 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-600 transition-colors disabled:opacity-50">
            <span v-if="loadingMore">Loading...</span>
            <span v-else>Load More Events</span>
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import axios from 'axios'
import EventCard from '../components/EventCard.vue'
import EventListItem from '../components/EventListItem.vue'

export default {
  name: 'EventsPage',
  components: {
    EventCard,
    EventListItem
  },
  setup() {
    const events = ref([])
    const categories = ref([])
    const loading = ref(true)
    const loadingMore = ref(false)
    const searchQuery = ref('')
    const selectedCategory = ref('')
    const dateFilter = ref('')
    const sortBy = ref('start_date')
    const viewMode = ref('grid')
    const currentPage = ref(1)
    const hasMore = ref(true)

    let searchTimeout = null

    const debouncedSearch = () => {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        currentPage.value = 1
        loadEvents()
      }, 500)
    }

    const loadEvents = async (append = false) => {
      if (!append) {
        loading.value = true
        currentPage.value = 1
      } else {
        loadingMore.value = true
      }

      try {
        const params = {
          page: currentPage.value,
          search: searchQuery.value,
          category_id: selectedCategory.value,
          date_filter: dateFilter.value,
          sort_by: sortBy.value,
          per_page: 12
        }

        const response = await axios.get('/public/events', { params })
        
        if (append) {
          events.value = [...events.value, ...response.data.data]
        } else {
          events.value = response.data.data
        }

        hasMore.value = response.data.current_page < response.data.last_page
      } catch (error) {
        console.error('Failed to load events:', error)
        // Fallback to mock data
        if (!append) {
          events.value = [
            {
              id: 1,
              title: 'Liquid N Lights Interactive Art Experience',
              slug: 'liquid-n-lights-interactive-art-experience',
              short_description: 'Interactive art installation with glowing drink-filled bulbs',
              start_date: '2025-08-15T18:00:00.000000Z',
              venue_name: 'Downtown Art Gallery',
              is_featured: true,
              category: { name: 'Art Fairs', color_code: '#FF6B6B' },
              ticket_types: [
                { price: 2500 },
                { price: 3500 },
                { price: 5500 }
              ]
            }
          ]
        }
      } finally {
        loading.value = false
        loadingMore.value = false
      }
    }

    const loadCategories = async () => {
      try {
        const response = await axios.get('/public/categories')
        categories.value = response.data.data
      } catch (error) {
        console.error('Failed to load categories:', error)
        categories.value = [
          { id: 1, name: 'Art Fairs', color_code: '#FF6B6B' },
          { id: 2, name: 'Corporate Parties', color_code: '#4ECDC4' },
          { id: 3, name: 'Weddings', color_code: '#F8C471' }
        ]
      }
    }

    const loadMore = () => {
      currentPage.value++
      loadEvents(true)
    }

    const clearFilters = () => {
      searchQuery.value = ''
      selectedCategory.value = ''
      dateFilter.value = ''
      sortBy.value = 'start_date'
      currentPage.value = 1
      loadEvents()
    }

    onMounted(() => {
      loadEvents()
      loadCategories()
    })

    return {
      events,
      categories,
      loading,
      loadingMore,
      searchQuery,
      selectedCategory,
      dateFilter,
      sortBy,
      viewMode,
      hasMore,
      debouncedSearch,
      loadEvents,
      loadMore,
      clearFilters
    }
  }
}
</script>
