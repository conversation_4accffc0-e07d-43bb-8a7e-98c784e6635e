<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center min-h-screen">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
    </div>

    <!-- Event Not Found -->
    <div v-else-if="!event" class="flex flex-col items-center justify-center min-h-screen">
      <div class="text-center">
        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Event Not Found</h2>
        <p class="text-gray-600 mb-6">The event you're looking for doesn't exist or has been removed.</p>
        <router-link to="/events" class="bg-indigo-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-indigo-700 transition-colors">
          Browse Events
        </router-link>
      </div>
    </div>

    <!-- Event Details -->
    <div v-else>
      <!-- Hero Section -->
      <div class="relative h-96 bg-gradient-to-r from-indigo-600 to-purple-600 overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-40"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-end pb-8">
          <div class="text-white">
            <!-- Breadcrumb -->
            <nav class="mb-4">
              <ol class="flex items-center space-x-2 text-sm">
                <li><router-link to="/" class="hover:text-yellow-300">Home</router-link></li>
                <li><span class="mx-2">/</span></li>
                <li><router-link to="/events" class="hover:text-yellow-300">Events</router-link></li>
                <li><span class="mx-2">/</span></li>
                <li class="text-yellow-300">{{ event.title }}</li>
              </ol>
            </nav>

            <h1 class="text-4xl md:text-5xl font-bold mb-4">{{ event.title }}</h1>
            <div class="flex flex-wrap items-center gap-4 text-lg">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                {{ formatDate(event.start_date) }}
              </div>
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                {{ event.venue_name }}
              </div>
              <div v-if="event.category" class="bg-white/20 px-3 py-1 rounded-full text-sm">
                {{ event.category.name || event.category }}
              </div>
            </div>
          </div>
        </div>
      </div>

              <!-- Quick Info -->
              <div class="mt-6 lg:mt-0 lg:ml-8">
                <div class="bg-black/30 backdrop-blur-sm rounded-xl p-6 text-white">
                  <div class="space-y-3">
                    <div class="flex items-center">
                      <svg class="w-5 h-5 mr-3 text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                      </svg>
                      <span>{{ formatDate(event.start_date) }}</span>
                    </div>
                    <div class="flex items-center">
                      <svg class="w-5 h-5 mr-3 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                      <span>{{ event.venue_name }}</span>
                    </div>
                    <div class="flex items-center">
                      <svg class="w-5 h-5 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                      </svg>
                      <span>Starting from ₹{{ getMinPrice() }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Social Share -->
        <div class="absolute top-4 right-4">
          <div class="flex space-x-2">
            <button @click="shareEvent('twitter')" class="bg-black/30 backdrop-blur-sm text-white p-2 rounded-full hover:bg-black/50 transition-colors">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
              </svg>
            </button>
            <button @click="shareEvent('facebook')" class="bg-black/30 backdrop-blur-sm text-white p-2 rounded-full hover:bg-black/50 transition-colors">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </button>
            <button @click="shareEvent('whatsapp')" class="bg-black/30 backdrop-blur-sm text-white p-2 rounded-full hover:bg-black/50 transition-colors">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
              </svg>
            </button>
          </div>
        </div>
      </section>

      <!-- Sticky Navigation -->
      <nav class="sticky top-16 bg-gray-800/95 backdrop-blur-sm border-b border-gray-700 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex space-x-8 overflow-x-auto">
            <button v-for="tab in tabs" :key="tab.id"
                    @click="activeTab = tab.id"
                    :class="activeTab === tab.id ? 'border-pink-500 text-pink-400' : 'border-transparent text-gray-400 hover:text-white'"
                    class="py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors">
              {{ tab.name }}
            </button>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
          <!-- Content Area -->
          <div class="lg:col-span-2">
            <!-- Info Tab -->
            <div v-if="activeTab === 'info'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-white mb-4">About This Event</h2>
                <div class="prose prose-invert max-w-none">
                  <p class="text-gray-300 leading-relaxed">{{ event.description }}</p>
                </div>
              </div>

              <!-- Event Highlights -->
              <div v-if="event.custom_fields?.highlights">
                <h3 class="text-xl font-bold text-white mb-4">Event Highlights</h3>
                <ul class="space-y-2">
                  <li v-for="highlight in event.custom_fields.highlights" :key="highlight"
                      class="flex items-center text-gray-300">
                    <svg class="w-5 h-5 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    {{ highlight }}
                  </li>
                </ul>
              </div>

              <!-- Gallery -->
              <div v-if="event.gallery_images && event.gallery_images.length > 0">
                <h3 class="text-xl font-bold text-white mb-4">Gallery</h3>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div v-for="(image, index) in event.gallery_images" :key="index"
                       @click="openGallery(index)"
                       class="relative aspect-square rounded-lg overflow-hidden cursor-pointer group">
                    <img :src="image" :alt="`Gallery image ${index + 1}`"
                         class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300">
                    <div class="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Venue Tab -->
            <div v-if="activeTab === 'venue'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-white mb-4">Venue Information</h2>
                <div class="bg-gray-800 rounded-xl p-6">
                  <h3 class="text-xl font-semibold text-white mb-2">{{ event.venue_name }}</h3>
                  <p class="text-gray-300 mb-4">{{ event.venue_address }}</p>

                  <!-- Map placeholder -->
                  <div class="bg-gray-700 rounded-lg h-64 flex items-center justify-center">
                    <div class="text-center">
                      <svg class="w-12 h-12 text-gray-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                      <p class="text-gray-400">Interactive map will be displayed here</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- FAQ Tab -->
            <div v-if="activeTab === 'faq'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-white mb-4">Frequently Asked Questions</h2>
                <div class="space-y-4">
                  <div v-for="(faq, index) in faqs" :key="index" class="bg-gray-800 rounded-xl overflow-hidden">
                    <button @click="toggleFaq(index)"
                            class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-700 transition-colors">
                      <span class="font-semibold text-white">{{ faq.question }}</span>
                      <svg :class="faq.open ? 'rotate-180' : ''"
                           class="w-5 h-5 text-gray-400 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </button>
                    <div v-if="faq.open" class="px-6 pb-4">
                      <p class="text-gray-300">{{ faq.answer }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Reviews Tab -->
            <div v-if="activeTab === 'reviews'" class="space-y-8">
              <div>
                <h2 class="text-2xl font-bold text-white mb-4">Reviews & Ratings</h2>
                <div class="bg-gray-800 rounded-xl p-6 text-center">
                  <svg class="w-16 h-16 text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                  </svg>
                  <p class="text-gray-400">No reviews yet. Be the first to review this event!</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Sidebar -->
          <div class="lg:col-span-1">
            <!-- Ticket Selection -->
            <div class="bg-gray-800 rounded-xl p-6 sticky top-32">
              <h3 class="text-xl font-bold text-white mb-6">Select Tickets</h3>

              <div class="space-y-4 mb-6">
                <div v-for="ticketType in event.ticket_types" :key="ticketType.id"
                     class="border border-gray-700 rounded-lg p-4 hover:border-pink-500 transition-colors">
                  <div class="flex justify-between items-start mb-2">
                    <div>
                      <h4 class="font-semibold text-white">{{ ticketType.name }}</h4>
                      <p class="text-sm text-gray-400">{{ ticketType.description }}</p>
                    </div>
                    <div class="text-right">
                      <div class="text-lg font-bold text-white">₹{{ ticketType.price.toLocaleString() }}</div>
                      <div class="text-xs text-gray-500">{{ ticketType.quantity_remaining }} left</div>
                    </div>
                  </div>

                  <!-- Benefits -->
                  <div v-if="ticketType.benefits && ticketType.benefits.length > 0" class="mb-3">
                    <ul class="text-xs text-gray-400 space-y-1">
                      <li v-for="benefit in ticketType.benefits" :key="benefit" class="flex items-center">
                        <svg class="w-3 h-3 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        {{ benefit }}
                      </li>
                    </ul>
                  </div>

                  <!-- Quantity Selector -->
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <button @click="decrementTicket(ticketType.id)"
                              :disabled="!selectedTickets[ticketType.id] || selectedTickets[ticketType.id] <= 0"
                              class="w-8 h-8 rounded-full bg-gray-700 text-white flex items-center justify-center hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                        </svg>
                      </button>
                      <span class="w-8 text-center text-white font-semibold">{{ selectedTickets[ticketType.id] || 0 }}</span>
                      <button @click="incrementTicket(ticketType.id)"
                              :disabled="(selectedTickets[ticketType.id] || 0) >= ticketType.max_quantity_per_order || ticketType.quantity_remaining <= 0"
                              class="w-8 h-8 rounded-full bg-gray-700 text-white flex items-center justify-center hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Total -->
              <div class="border-t border-gray-700 pt-4 mb-6">
                <div class="flex justify-between items-center text-lg font-bold text-white">
                  <span>Total ({{ totalTickets }} tickets)</span>
                  <span>₹{{ totalAmount.toLocaleString() }}</span>
                </div>
              </div>

              <!-- Book Button -->
              <router-link v-if="totalTickets > 0"
                           :to="`/booking/${event.id}`"
                           class="w-full bg-gradient-to-r from-pink-500 to-cyan-500 text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg hover:shadow-pink-500/25 transition-all block text-center">
                Proceed to Book
              </router-link>
              <button v-else
                      disabled
                      class="w-full bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold opacity-50 cursor-not-allowed">
                Select Tickets
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <svg class="w-16 h-16 text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <h2 class="text-2xl font-bold text-white mb-2">Event Not Found</h2>
        <p class="text-gray-400 mb-6">The event you're looking for doesn't exist or has been removed.</p>
        <router-link to="/events" class="bg-gradient-to-r from-pink-500 to-cyan-500 text-white px-6 py-3 rounded-lg font-semibold">
          Browse Events
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'EventDetailPage',
  setup() {
    const route = useRoute()
    const router = useRouter()

    const event = ref(null)
    const loading = ref(true)
    const activeTab = ref('info')
    const selectedTickets = reactive({})

    const tabs = [
      { id: 'info', name: 'Info' },
      { id: 'venue', name: 'Venue' },
      { id: 'faq', name: 'FAQ' },
      { id: 'reviews', name: 'Reviews' }
    ]

    const faqs = ref([
      {
        question: 'What is Liquid N Lights?',
        answer: 'Liquid N Lights is a custom designed art installation that combines the nostalgia of Lite Brite with current Instagram culture. Each glowing bulb is filled with alcoholic or non-alcoholic drinks for the interaction of the guest.',
        open: false
      },
      {
        question: 'How does the customization work?',
        answer: 'You send our team your image and we create a customized rendering that transforms your vision into an interactive experience perfect for your event.',
        open: false
      },
      {
        question: 'What types of events is this suitable for?',
        answer: 'Our installation is perfect for art fairs, corporate parties, galas, launch parties, birthdays, weddings, and private parties.',
        open: false
      },
      {
        question: 'Can I get a refund if I cannot attend?',
        answer: 'Refunds are available up to 48 hours before the event start time. Please contact our support team for assistance.',
        open: false
      }
    ])

    const totalTickets = computed(() => {
      return Object.values(selectedTickets).reduce((sum, qty) => sum + (qty || 0), 0)
    })

    const totalAmount = computed(() => {
      if (!event.value?.ticket_types) return 0

      return event.value.ticket_types.reduce((sum, ticketType) => {
        const quantity = selectedTickets[ticketType.id] || 0
        return sum + (quantity * ticketType.price)
      }, 0)
    })

    const loadEvent = async () => {
      try {
        const response = await axios.get(`/public/events/${route.params.slug}`)
        event.value = response.data.data

        // Initialize selected tickets
        event.value.ticket_types?.forEach(ticketType => {
          selectedTickets[ticketType.id] = 0
        })
      } catch (error) {
        console.error('Failed to load event:', error)
        // Fallback to mock data
        event.value = {
          id: 1,
          title: 'Liquid N Lights Interactive Art Experience',
          slug: 'liquid-n-lights-interactive-art-experience',
          description: 'Experience our signature interactive art installation that combines the nostalgia of Lite Brite with modern Instagram culture. Each glowing bulb is filled with drinks for guest interaction, creating a unique and memorable experience perfect for art fairs, corporate parties, galas, launch parties, birthdays, weddings, and private parties.',
          short_description: 'Interactive art installation with glowing drink-filled bulbs',
          start_date: '2025-08-15T18:00:00.000000Z',
          end_date: '2025-08-15T23:00:00.000000Z',
          venue_name: 'Downtown Art Gallery',
          venue_address: '123 Art Street, Downtown, City 12345',
          is_featured: true,
          category: { name: 'Art Fairs', color_code: '#FF6B6B' },
          ticket_types: [
            {
              id: 1,
              name: 'Early Bird',
              description: 'Limited early bird tickets with special pricing',
              price: 2500,
              quantity_available: 50,
              quantity_sold: 15,
              quantity_remaining: 35,
              max_quantity_per_order: 4,
              benefits: ['Early access', 'Welcome drink', 'Exclusive photo session']
            },
            {
              id: 2,
              name: 'General Admission',
              description: 'Standard admission to the interactive art experience',
              price: 3500,
              quantity_available: 120,
              quantity_sold: 45,
              quantity_remaining: 75,
              max_quantity_per_order: 6,
              benefits: ['Full access to installation', 'Complimentary drink']
            },
            {
              id: 3,
              name: 'VIP Experience',
              description: 'Premium experience with exclusive perks',
              price: 5500,
              quantity_available: 30,
              quantity_sold: 8,
              quantity_remaining: 22,
              max_quantity_per_order: 2,
              benefits: ['Priority access', 'Premium drinks', 'Artist meet & greet', 'Exclusive merchandise']
            }
          ]
        }

        // Initialize selected tickets for mock data
        event.value.ticket_types.forEach(ticketType => {
          selectedTickets[ticketType.id] = 0
        })
      } finally {
        loading.value = false
      }
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const getMinPrice = () => {
      if (!event.value?.ticket_types || event.value.ticket_types.length === 0) return '0'
      return Math.min(...event.value.ticket_types.map(t => t.price)).toLocaleString()
    }

    const incrementTicket = (ticketTypeId) => {
      const ticketType = event.value.ticket_types.find(t => t.id === ticketTypeId)
      if (!ticketType) return

      const currentQty = selectedTickets[ticketTypeId] || 0
      if (currentQty < ticketType.max_quantity_per_order && currentQty < ticketType.quantity_remaining) {
        selectedTickets[ticketTypeId] = currentQty + 1
      }
    }

    const decrementTicket = (ticketTypeId) => {
      const currentQty = selectedTickets[ticketTypeId] || 0
      if (currentQty > 0) {
        selectedTickets[ticketTypeId] = currentQty - 1
      }
    }

    const toggleFaq = (index) => {
      faqs.value[index].open = !faqs.value[index].open
    }

    const shareEvent = (platform) => {
      const url = window.location.href
      const title = event.value?.title || 'Check out this event'

      let shareUrl = ''
      switch (platform) {
        case 'twitter':
          shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`
          break
        case 'facebook':
          shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`
          break
        case 'whatsapp':
          shareUrl = `https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`
          break
      }

      if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400')
      }
    }

    const openGallery = (index) => {
      // TODO: Implement gallery lightbox
      console.log('Open gallery at index:', index)
    }

    const proceedToBooking = () => {
      if (totalTickets.value === 0) return

      // Store selected tickets in session storage
      const bookingData = {
        eventId: event.value.id,
        tickets: Object.entries(selectedTickets)
          .filter(([_, qty]) => qty > 0)
          .map(([ticketTypeId, quantity]) => ({
            ticketTypeId: parseInt(ticketTypeId),
            quantity
          }))
      }

      sessionStorage.setItem('bookingData', JSON.stringify(bookingData))
      router.push(`/booking/${event.value.id}`)
    }

    onMounted(() => {
      loadEvent()
    })

    return {
      event,
      loading,
      activeTab,
      selectedTickets,
      tabs,
      faqs,
      totalTickets,
      totalAmount,
      formatDate,
      getMinPrice,
      incrementTicket,
      decrementTicket,
      toggleFaq,
      shareEvent,
      openGallery,
      proceedToBooking
    }
  }
}
</script>

<style scoped>
.prose {
  max-width: none;
}

.prose p {
  margin-bottom: 1rem;
}
</style>