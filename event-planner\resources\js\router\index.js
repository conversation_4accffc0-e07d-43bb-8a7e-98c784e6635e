import { createRouter, createWebHistory } from 'vue-router'

// Import pages that exist
import HomePage from '@/pages/HomePage.vue'
import EventsPage from '@/pages/EventsPage.vue'
import EventDetailPage from '@/pages/EventDetailPage.vue'
import BookingPage from '@/pages/BookingPage.vue'
import DashboardPage from '@/pages/DashboardPage.vue'
import MyTicketsPage from '@/pages/MyTicketsPage.vue'
import LoginPage from '@/pages/auth/LoginPage.vue'
import RegisterPage from '@/pages/auth/RegisterPage.vue'

// Admin pages
import AdminDashboard from '@/pages/admin/Dashboard.vue'
import AdminEvents from '@/pages/admin/Events.vue'
import AdminBookings from '@/pages/admin/Bookings.vue'
import AdminUsers from '@/pages/admin/Users.vue'
import AdminRoles from '@/pages/admin/Roles.vue'
import AdminSettings from '@/pages/admin/Settings.vue'
import AdminScanner from '@/pages/admin/Scanner.vue'
import AdminReports from '@/pages/admin/Reports.vue'
import AdminCreateEvent from '@/pages/admin/CreateEvent.vue'
import AdminNotifications from '@/pages/admin/Notifications.vue'
import AdminSystemLogs from '@/pages/admin/SystemLogs.vue'
import AdminMediaLibrary from '@/pages/admin/MediaLibrary.vue'
import AdminMaintenance from '@/pages/admin/Maintenance.vue'

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomePage,
    meta: { title: 'Home' }
  },
  {
    path: '/events',
    name: 'events',
    component: EventsPage,
    meta: { title: 'Events' }
  },
  {
    path: '/events/:slug',
    name: 'event-detail',
    component: EventDetailPage,
    meta: { title: 'Event Details' }
  },
  {
    path: '/booking/:eventId',
    name: 'booking',
    component: BookingPage,
    meta: { requiresAuth: true, title: 'Book Event' }
  },
  {
    path: '/my-tickets',
    name: 'my-tickets',
    component: MyTicketsPage,
    meta: { requiresAuth: true, title: 'My Tickets' }
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: DashboardPage,
    meta: { requiresAuth: true, title: 'Dashboard' }
  },
  {
    path: '/login',
    name: 'login',
    component: LoginPage,
    meta: { title: 'Login', guest: true }
  },
  {
    path: '/register',
    name: 'register',
    component: RegisterPage,
    meta: { title: 'Register', guest: true }
  },
  {
    path: '/admin',
    name: 'admin-dashboard',
    component: AdminDashboard,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Admin Dashboard' }
  },
  {
    path: '/admin/events',
    name: 'admin-events',
    component: AdminEvents,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Events Management' }
  },
  {
    path: '/admin/bookings',
    name: 'admin-bookings',
    component: AdminBookings,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Bookings Management' }
  },
  {
    path: '/admin/users',
    name: 'admin-users',
    component: AdminUsers,
    meta: { requiresAuth: true, requiresRole: ['admin'], title: 'User Management' }
  },
  {
    path: '/admin/roles',
    name: 'admin-roles',
    component: AdminRoles,
    meta: { requiresAuth: true, requiresRole: ['admin'], title: 'Role Management' }
  },
  {
    path: '/admin/settings',
    name: 'admin-settings',
    component: AdminSettings,
    meta: { requiresAuth: true, requiresRole: ['admin'], title: 'System Settings' }
  },
  {
    path: '/admin/scanner',
    name: 'admin-scanner',
    component: AdminScanner,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager', 'scanner'], title: 'Ticket Scanner' }
  },
  {
    path: '/admin/reports',
    name: 'admin-reports',
    component: AdminReports,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Reports & Analytics' }
  },
  {
    path: '/admin/events/create',
    name: 'admin-create-event',
    component: AdminCreateEvent,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Create Event' }
  },
  {
    path: '/admin/events/:id/edit',
    name: 'admin-edit-event',
    component: AdminCreateEvent,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Edit Event' }
  },
  {
    path: '/admin/notifications',
    name: 'admin-notifications',
    component: AdminNotifications,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Notifications' }
  },
  {
    path: '/admin/logs',
    name: 'admin-logs',
    component: AdminSystemLogs,
    meta: { requiresAuth: true, requiresRole: ['admin'], title: 'System Logs' }
  },
  {
    path: '/admin/media',
    name: 'admin-media',
    component: AdminMediaLibrary,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Media Library' }
  },
  {
    path: '/admin/maintenance',
    name: 'admin-maintenance',
    component: AdminMaintenance,
    meta: { requiresAuth: true, requiresRole: ['admin'], title: 'System Maintenance' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  // Set page title
  document.title = to.meta.title ? `${to.meta.title} - Event Manager` : 'Event Manager'

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    // For demo, we'll simulate authentication
    const isAuthenticated = localStorage.getItem('auth_token')
    if (!isAuthenticated) {
      next({ name: 'login', query: { redirect: to.fullPath } })
      return
    }
  }

  // Check role requirements for admin routes
  if (to.meta.requiresRole) {
    // For demo, simulate admin role check
    const userRole = localStorage.getItem('user_role') || 'attendee'
    const hasRequiredRole = to.meta.requiresRole.includes(userRole)

    if (!hasRequiredRole) {
      next({ name: 'home' })
      return
    }
  }

  next()
})

export default router
