import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Import pages
import HomePage from '@/pages/HomePage.vue'
import EventsPage from '@/pages/EventsPage.vue'
import EventDetailPage from '@/pages/EventDetailPage.vue'
import BookingPage from '@/pages/BookingPage.vue'
import MyTicketsPage from '@/pages/MyTicketsPage.vue'
import LoginPage from '@/pages/auth/LoginPage.vue'
import RegisterPage from '@/pages/auth/RegisterPage.vue'
import DashboardPage from '@/pages/DashboardPage.vue'

// Admin pages
import AdminDashboard from '@/pages/admin/Dashboard.vue'
import AdminEvents from '@/pages/admin/Events.vue'
import AdminUsers from '@/pages/admin/Users.vue'
import AdminSettings from '@/pages/admin/Settings.vue'

// Organizer pages
import OrganizerDashboard from '@/pages/organizer/Dashboard.vue'
import OrganizerEvents from '@/pages/organizer/Events.vue'
import CreateEvent from '@/pages/organizer/CreateEvent.vue'
import EditEvent from '@/pages/organizer/EditEvent.vue'

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomePage,
    meta: { title: 'Home' }
  },
  {
    path: '/events',
    name: 'events',
    component: EventsPage,
    meta: { title: 'Events' }
  },
  {
    path: '/events/:slug',
    name: 'event-detail',
    component: EventDetailPage,
    meta: { title: 'Event Details' }
  },
  {
    path: '/booking/:eventId',
    name: 'booking',
    component: BookingPage,
    meta: { requiresAuth: true, title: 'Book Event' }
  },
  {
    path: '/my-tickets',
    name: 'my-tickets',
    component: MyTicketsPage,
    meta: { requiresAuth: true, title: 'My Tickets' }
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: DashboardPage,
    meta: { requiresAuth: true, title: 'Dashboard' }
  },
  {
    path: '/login',
    name: 'login',
    component: LoginPage,
    meta: { title: 'Login', guest: true }
  },
  {
    path: '/register',
    name: 'register',
    component: RegisterPage,
    meta: { title: 'Register', guest: true }
  },
  {
    path: '/admin',
    name: 'admin',
    component: AdminDashboard,
    meta: { requiresAuth: true, requiresRole: ['admin'], title: 'Admin Dashboard' },
    children: [
      {
        path: 'events',
        name: 'admin-events',
        component: AdminEvents,
        meta: { title: 'Manage Events' }
      },
      {
        path: 'users',
        name: 'admin-users',
        component: AdminUsers,
        meta: { title: 'Manage Users' }
      },
      {
        path: 'settings',
        name: 'admin-settings',
        component: AdminSettings,
        meta: { title: 'Settings' }
      }
    ]
  },
  {
    path: '/organizer',
    name: 'organizer',
    component: OrganizerDashboard,
    meta: { requiresAuth: true, requiresRole: ['organizer', 'admin'], title: 'Organizer Dashboard' },
    children: [
      {
        path: 'events',
        name: 'organizer-events',
        component: OrganizerEvents,
        meta: { title: 'My Events' }
      },
      {
        path: 'events/create',
        name: 'create-event',
        component: CreateEvent,
        meta: { title: 'Create Event' }
      },
      {
        path: 'events/:id/edit',
        name: 'edit-event',
        component: EditEvent,
        meta: { title: 'Edit Event' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Set page title
  document.title = to.meta.title ? `${to.meta.title} - Event Manager` : 'Event Manager'
  
  // Check if route requires authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'login', query: { redirect: to.fullPath } })
    return
  }
  
  // Check if route is for guests only
  if (to.meta.guest && authStore.isAuthenticated) {
    next({ name: 'dashboard' })
    return
  }
  
  // Check role requirements
  if (to.meta.requiresRole && !authStore.hasRole(to.meta.requiresRole)) {
    next({ name: 'dashboard' })
    return
  }
  
  next()
})

export default router
