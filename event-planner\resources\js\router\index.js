import { createRouter, createWebHistory } from 'vue-router'

// Use dynamic imports for code splitting
const HomePage = () => import('@/pages/HomePage.vue')
const EventsPage = () => import('@/pages/EventsPage.vue')
const EventDetailPage = () => import('@/pages/EventDetailPage.vue')
const BookingPage = () => import('@/pages/BookingPage.vue')
const DashboardPage = () => import('@/pages/DashboardPage.vue')
const MyTicketsPage = () => import('@/pages/MyTicketsPage.vue')
const LoginPage = () => import('@/pages/auth/LoginPage.vue')
const RegisterPage = () => import('@/pages/auth/RegisterPage.vue')

// Admin pages with dynamic imports for better code splitting
const AdminDashboard = () => import('@/pages/admin/Dashboard.vue')
const AdminEvents = () => import('@/pages/admin/Events.vue')
const AdminBookings = () => import('@/pages/admin/Bookings.vue')
const AdminUsers = () => import('@/pages/admin/Users.vue')
const AdminRoles = () => import('@/pages/admin/Roles.vue')
const AdminSettings = () => import('@/pages/admin/Settings.vue')
const AdminScanner = () => import('@/pages/admin/Scanner.vue')
const AdminReports = () => import('@/pages/admin/Reports.vue')
const AdminCreateEvent = () => import('@/pages/admin/CreateEvent.vue')
const AdminNotifications = () => import('@/pages/admin/Notifications.vue')
const AdminSystemLogs = () => import('@/pages/admin/SystemLogs.vue')
const AdminMediaLibrary = () => import('@/pages/admin/MediaLibrary.vue')
const AdminMaintenance = () => import('@/pages/admin/Maintenance.vue')

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomePage,
    meta: { title: 'Home' }
  },
  {
    path: '/events',
    name: 'events',
    component: EventsPage,
    meta: { title: 'Events' }
  },
  {
    path: '/events/:slug',
    name: 'event-detail',
    component: EventDetailPage,
    meta: { title: 'Event Details' }
  },
  {
    path: '/booking/:eventId',
    name: 'booking',
    component: BookingPage,
    meta: { requiresAuth: true, title: 'Book Event' }
  },
  {
    path: '/my-tickets',
    name: 'my-tickets',
    component: MyTicketsPage,
    meta: { requiresAuth: true, title: 'My Tickets' }
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: DashboardPage,
    meta: { requiresAuth: true, title: 'Dashboard' }
  },
  {
    path: '/login',
    name: 'login',
    component: LoginPage,
    meta: { title: 'Login', guest: true }
  },
  {
    path: '/register',
    name: 'register',
    component: RegisterPage,
    meta: { title: 'Register', guest: true }
  },
  {
    path: '/admin',
    name: 'admin-dashboard',
    component: AdminDashboard,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Admin Dashboard' }
  },
  {
    path: '/admin/events',
    name: 'admin-events',
    component: AdminEvents,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Events Management' }
  },
  {
    path: '/admin/bookings',
    name: 'admin-bookings',
    component: AdminBookings,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Bookings Management' }
  },
  {
    path: '/admin/users',
    name: 'admin-users',
    component: AdminUsers,
    meta: { requiresAuth: true, requiresRole: ['admin'], title: 'User Management' }
  },
  {
    path: '/admin/roles',
    name: 'admin-roles',
    component: AdminRoles,
    meta: { requiresAuth: true, requiresRole: ['admin'], title: 'Role Management' }
  },
  {
    path: '/admin/settings',
    name: 'admin-settings',
    component: AdminSettings,
    meta: { requiresAuth: true, requiresRole: ['admin'], title: 'System Settings' }
  },
  {
    path: '/admin/scanner',
    name: 'admin-scanner',
    component: AdminScanner,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager', 'scanner'], title: 'Ticket Scanner' }
  },
  {
    path: '/admin/reports',
    name: 'admin-reports',
    component: AdminReports,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Reports & Analytics' }
  },
  {
    path: '/admin/events/create',
    name: 'admin-create-event',
    component: AdminCreateEvent,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Create Event' }
  },
  {
    path: '/admin/events/:id/edit',
    name: 'admin-edit-event',
    component: AdminCreateEvent,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Edit Event' }
  },
  {
    path: '/admin/notifications',
    name: 'admin-notifications',
    component: AdminNotifications,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Notifications' }
  },
  {
    path: '/admin/logs',
    name: 'admin-logs',
    component: AdminSystemLogs,
    meta: { requiresAuth: true, requiresRole: ['admin'], title: 'System Logs' }
  },
  {
    path: '/admin/media',
    name: 'admin-media',
    component: AdminMediaLibrary,
    meta: { requiresAuth: true, requiresRole: ['admin', 'manager'], title: 'Media Library' }
  },
  {
    path: '/admin/maintenance',
    name: 'admin-maintenance',
    component: AdminMaintenance,
    meta: { requiresAuth: true, requiresRole: ['admin'], title: 'System Maintenance' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  // Set page title
  document.title = to.meta.title ? `${to.meta.title} - Event Manager` : 'Event Manager'

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    // For demo, we'll simulate authentication
    const isAuthenticated = localStorage.getItem('auth_token')
    if (!isAuthenticated) {
      next({ name: 'login', query: { redirect: to.fullPath } })
      return
    }
  }

  // Check role requirements for admin routes
  if (to.meta.requiresRole) {
    // For demo, simulate admin role check
    const userRole = localStorage.getItem('user_role') || 'attendee'
    const hasRequiredRole = to.meta.requiresRole.includes(userRole)

    if (!hasRequiredRole) {
      next({ name: 'home' })
      return
    }
  }

  next()
})

export default router
