import { createRouter, createWebHistory } from 'vue-router'

// Import pages that exist
import HomePage from '@/pages/HomePage.vue'
import EventsPage from '@/pages/EventsPage.vue'
import EventDetailPage from '@/pages/EventDetailPage.vue'
import BookingPage from '@/pages/BookingPage.vue'
import DashboardPage from '@/pages/DashboardPage.vue'
import MyTicketsPage from '@/pages/MyTicketsPage.vue'
import LoginPage from '@/pages/auth/LoginPage.vue'
import RegisterPage from '@/pages/auth/RegisterPage.vue'

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomePage,
    meta: { title: 'Home' }
  },
  {
    path: '/events',
    name: 'events',
    component: EventsPage,
    meta: { title: 'Events' }
  },
  {
    path: '/events/:slug',
    name: 'event-detail',
    component: EventDetailPage,
    meta: { title: 'Event Details' }
  },
  {
    path: '/booking/:eventId',
    name: 'booking',
    component: BookingPage,
    meta: { requiresAuth: true, title: 'Book Event' }
  },
  {
    path: '/my-tickets',
    name: 'my-tickets',
    component: MyTicketsPage,
    meta: { requiresAuth: true, title: 'My Tickets' }
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: DashboardPage,
    meta: { requiresAuth: true, title: 'Dashboard' }
  },
  {
    path: '/login',
    name: 'login',
    component: LoginPage,
    meta: { title: 'Login', guest: true }
  },
  {
    path: '/register',
    name: 'register',
    component: RegisterPage,
    meta: { title: 'Register', guest: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  // Set page title
  document.title = to.meta.title ? `${to.meta.title} - Event Manager` : 'Event Manager'

  // Simple navigation for now - we'll add auth checks later
  next()
})

export default router
