<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CmsPage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CmsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = CmsPage::with(['creator', 'updater']);

        // Apply filters
        if ($request->has('page_type')) {
            $query->where('page_type', $request->page_type);
        }

        if ($request->has('is_published')) {
            $query->where('is_published', $request->boolean('is_published'));
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%");
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $pages = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $pages
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'page_type' => 'required|in:page,home,about,contact,gallery,faq,custom',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string',
            'og_data' => 'nullable|array',
            'is_published' => 'boolean',
            'show_in_menu' => 'boolean',
            'menu_order' => 'nullable|integer',
            'template' => 'nullable|string|max:255',
            'content_blocks' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $request->all();
        $data['created_by'] = auth()->id();

        // Generate slug if not provided
        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['title']);
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $data['featured_image'] = $request->file('featured_image')->store('cms/featured', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            $galleryPaths = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryPaths[] = $image->store('cms/gallery', 'public');
            }
            $data['gallery_images'] = $galleryPaths;
        }

        $page = CmsPage::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Page created successfully',
            'data' => $page->load(['creator'])
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(CmsPage $cms)
    {
        $cms->load(['creator', 'updater']);

        return response()->json([
            'success' => true,
            'data' => $cms
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CmsPage $cms)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'page_type' => 'required|in:page,home,about,contact,gallery,faq,custom',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string',
            'og_data' => 'nullable|array',
            'is_published' => 'boolean',
            'show_in_menu' => 'boolean',
            'menu_order' => 'nullable|integer',
            'template' => 'nullable|string|max:255',
            'content_blocks' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $request->all();
        $data['updated_by'] = auth()->id();

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($cms->featured_image) {
                Storage::disk('public')->delete($cms->featured_image);
            }
            $data['featured_image'] = $request->file('featured_image')->store('cms/featured', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            // Delete old gallery images
            if ($cms->gallery_images) {
                foreach ($cms->gallery_images as $image) {
                    Storage::disk('public')->delete($image);
                }
            }

            $galleryPaths = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryPaths[] = $image->store('cms/gallery', 'public');
            }
            $data['gallery_images'] = $galleryPaths;
        }

        $cms->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Page updated successfully',
            'data' => $cms->load(['creator', 'updater'])
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CmsPage $cms)
    {
        // Delete associated images
        if ($cms->featured_image) {
            Storage::disk('public')->delete($cms->featured_image);
        }

        if ($cms->gallery_images) {
            foreach ($cms->gallery_images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $cms->delete();

        return response()->json([
            'success' => true,
            'message' => 'Page deleted successfully'
        ]);
    }

    /**
     * Duplicate a page
     */
    public function duplicate(CmsPage $cms)
    {
        $newPage = $cms->replicate();
        $newPage->title = $cms->title . ' (Copy)';
        $newPage->slug = Str::slug($newPage->title);
        $newPage->is_published = false;
        $newPage->created_by = auth()->id();
        $newPage->updated_by = null;
        $newPage->save();

        return response()->json([
            'success' => true,
            'message' => 'Page duplicated successfully',
            'data' => $newPage->load(['creator'])
        ]);
    }

    /**
     * Get menu pages
     */
    public function menuPages()
    {
        $pages = CmsPage::where('show_in_menu', true)
                       ->where('is_published', true)
                       ->orderBy('menu_order', 'asc')
                       ->select(['id', 'title', 'slug', 'page_type', 'menu_order'])
                       ->get();

        return response()->json([
            'success' => true,
            'data' => $pages
        ]);
    }

    /**
     * Import content from Liquid N Lights
     */
    public function importLiquidNLights()
    {
        // Create About page
        $aboutPage = CmsPage::updateOrCreate(
            ['page_type' => 'about'],
            [
                'title' => 'About Liquid N Lights',
                'slug' => 'about',
                'content' => '<h2>Interactive Art Installation</h2>
                <p>Liquid N Lights is a custom designed art installation that combines the nostalgia of Lite Brite with current Instagram culture. Each glowing bulb is filled with alcoholic or non-alcoholic drinks for the interaction of the guest.</p>

                <h3>How It Works</h3>
                <p>You send our team your image and we create a customized rendering that transforms your vision into an interactive experience. Perfect for art fairs, corporate parties, galas, launch parties, birthdays, weddings, and private parties.</p>',
                'excerpt' => 'Custom designed art installation combining nostalgia with modern Instagram culture.',
                'page_type' => 'about',
                'is_published' => true,
                'show_in_menu' => true,
                'menu_order' => 1,
                'meta_title' => 'About Liquid N Lights - Interactive Art Installation',
                'meta_description' => 'Learn about our custom designed art installation that combines Lite Brite nostalgia with Instagram culture.',
                'created_by' => auth()->id(),
            ]
        );

        // Create Gallery page
        $galleryPage = CmsPage::updateOrCreate(
            ['page_type' => 'gallery'],
            [
                'title' => 'Gallery',
                'slug' => 'gallery',
                'content' => '<h2>Event Gallery</h2>
                <p>Explore our stunning collection of Liquid N Lights installations at various events:</p>
                <ul>
                    <li>Art Fairs</li>
                    <li>Corporate Parties</li>
                    <li>Galas</li>
                    <li>Launch Parties</li>
                    <li>Birthdays</li>
                    <li>Weddings</li>
                    <li>Private Parties</li>
                </ul>',
                'excerpt' => 'View our stunning installations at various events and celebrations.',
                'page_type' => 'gallery',
                'is_published' => true,
                'show_in_menu' => true,
                'menu_order' => 2,
                'meta_title' => 'Gallery - Liquid N Lights Event Photos',
                'meta_description' => 'Browse our gallery of Liquid N Lights installations at weddings, corporate events, and private parties.',
                'created_by' => auth()->id(),
            ]
        );

        // Create FAQ page
        $faqPage = CmsPage::updateOrCreate(
            ['page_type' => 'faq'],
            [
                'title' => 'Frequently Asked Questions',
                'slug' => 'faq',
                'content' => '<h2>What is Liquid N Lights?</h2>
                <p>Liquid N Lights is a custom designed art installation that combines the nostalgia of Lite Brite with current Instagram culture. Each glowing bulb is filled with alcoholic or non-alcoholic drinks for the interaction of the guest.</p>

                <h2>How does the customization work?</h2>
                <p>You send our team your image and we create a customized rendering that transforms your vision into an interactive experience perfect for your event.</p>

                <h2>What types of events is this suitable for?</h2>
                <p>Our installation is perfect for art fairs, corporate parties, galas, launch parties, birthdays, weddings, and private parties.</p>',
                'excerpt' => 'Common questions about our interactive art installation.',
                'page_type' => 'faq',
                'is_published' => true,
                'show_in_menu' => true,
                'menu_order' => 3,
                'meta_title' => 'FAQ - Liquid N Lights Questions',
                'meta_description' => 'Find answers to frequently asked questions about our interactive art installation.',
                'created_by' => auth()->id(),
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Liquid N Lights content imported successfully',
            'data' => [
                'about' => $aboutPage,
                'gallery' => $galleryPage,
                'faq' => $faqPage,
            ]
        ]);
    }
}
