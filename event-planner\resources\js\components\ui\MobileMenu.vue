<template>
  <div>
    <!-- Mobile menu button -->
    <button
      @click="isOpen = !isOpen"
      class="md:hidden relative inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-indigo-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 touch-target"
      :aria-expanded="isOpen"
      aria-label="Toggle mobile menu"
    >
      <Bars3Icon v-if="!isOpen" class="block h-6 w-6" />
      <XMarkIcon v-else class="block h-6 w-6" />
    </button>

    <!-- Mobile menu overlay -->
    <Transition
      enter-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-300"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isOpen"
        class="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden"
        @click="isOpen = false"
      />
    </Transition>

    <!-- Mobile menu panel -->
    <Transition
      enter-active-class="transition-transform duration-300 ease-out"
      enter-from-class="transform translate-x-full"
      enter-to-class="transform translate-x-0"
      leave-active-class="transition-transform duration-300 ease-in"
      leave-from-class="transform translate-x-0"
      leave-to-class="transform translate-x-full"
    >
      <div
        v-if="isOpen"
        class="fixed top-0 right-0 z-50 h-full w-80 max-w-sm bg-white shadow-xl md:hidden safe-top safe-bottom"
      >
        <div class="flex flex-col h-full">
          <!-- Header -->
          <div class="flex items-center justify-between p-4 border-b border-gray-200">
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 2L3 7v11a1 1 0 001 1h3v-6h6v6h3a1 1 0 001-1V7l-7-5z"/>
                </svg>
              </div>
              <span class="text-lg font-bold text-gray-900">EventManager</span>
            </div>
            <button
              @click="isOpen = false"
              class="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 touch-target"
            >
              <XMarkIcon class="h-6 w-6" />
            </button>
          </div>

          <!-- Navigation -->
          <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto scroll-smooth-mobile">
            <!-- Main Navigation -->
            <div class="space-y-1">
              <router-link
                to="/"
                @click="isOpen = false"
                class="flex items-center px-3 py-3 text-base font-medium text-gray-700 rounded-md hover:text-indigo-600 hover:bg-gray-50 touch-target"
                :class="{ 'text-indigo-600 bg-indigo-50': $route.name === 'home' }"
              >
                <HomeIcon class="mr-3 h-5 w-5" />
                Home
              </router-link>

              <router-link
                to="/events"
                @click="isOpen = false"
                class="flex items-center px-3 py-3 text-base font-medium text-gray-700 rounded-md hover:text-indigo-600 hover:bg-gray-50 touch-target"
                :class="{ 'text-indigo-600 bg-indigo-50': $route.name === 'events' }"
              >
                <CalendarIcon class="mr-3 h-5 w-5" />
                Events
              </router-link>

              <!-- Authenticated User Links -->
              <template v-if="authStore.isAuthenticated">
                <router-link
                  to="/dashboard"
                  @click="isOpen = false"
                  class="flex items-center px-3 py-3 text-base font-medium text-gray-700 rounded-md hover:text-indigo-600 hover:bg-gray-50 touch-target"
                  :class="{ 'text-indigo-600 bg-indigo-50': $route.name === 'dashboard' }"
                >
                  <ChartBarIcon class="mr-3 h-5 w-5" />
                  Dashboard
                </router-link>

                <router-link
                  to="/my-tickets"
                  @click="isOpen = false"
                  class="flex items-center px-3 py-3 text-base font-medium text-gray-700 rounded-md hover:text-indigo-600 hover:bg-gray-50 touch-target"
                  :class="{ 'text-indigo-600 bg-indigo-50': $route.name === 'my-tickets' }"
                >
                  <TicketIcon class="mr-3 h-5 w-5" />
                  My Tickets
                </router-link>

                <!-- Organizer/Admin Links -->
                <div v-if="authStore.hasRole(['organizer', 'admin'])" class="pt-4 border-t border-gray-200">
                  <p class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
                    Organizer
                  </p>
                  
                  <router-link
                    to="/organizer/events"
                    @click="isOpen = false"
                    class="flex items-center px-3 py-3 text-base font-medium text-gray-700 rounded-md hover:text-indigo-600 hover:bg-gray-50 touch-target"
                  >
                    <PresentationChartBarIcon class="mr-3 h-5 w-5" />
                    My Events
                  </router-link>

                  <router-link
                    to="/organizer/events/create"
                    @click="isOpen = false"
                    class="flex items-center px-3 py-3 text-base font-medium text-gray-700 rounded-md hover:text-indigo-600 hover:bg-gray-50 touch-target"
                  >
                    <PlusIcon class="mr-3 h-5 w-5" />
                    Create Event
                  </router-link>
                </div>

                <!-- Admin Links -->
                <div v-if="authStore.hasRole('admin')" class="pt-4 border-t border-gray-200">
                  <p class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
                    Admin
                  </p>
                  
                  <router-link
                    to="/admin"
                    @click="isOpen = false"
                    class="flex items-center px-3 py-3 text-base font-medium text-gray-700 rounded-md hover:text-indigo-600 hover:bg-gray-50 touch-target"
                  >
                    <CogIcon class="mr-3 h-5 w-5" />
                    Admin Panel
                  </router-link>
                </div>
              </template>

              <!-- Guest Links -->
              <template v-else>
                <router-link
                  to="/login"
                  @click="isOpen = false"
                  class="flex items-center px-3 py-3 text-base font-medium text-gray-700 rounded-md hover:text-indigo-600 hover:bg-gray-50 touch-target"
                >
                  <ArrowRightOnRectangleIcon class="mr-3 h-5 w-5" />
                  Sign In
                </router-link>

                <router-link
                  to="/register"
                  @click="isOpen = false"
                  class="flex items-center px-3 py-3 text-base font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 touch-target"
                >
                  <UserPlusIcon class="mr-3 h-5 w-5" />
                  Sign Up
                </router-link>
              </template>
            </div>
          </nav>

          <!-- Footer -->
          <div v-if="authStore.isAuthenticated" class="border-t border-gray-200 p-4">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                <UserIcon class="w-5 h-5 text-indigo-600" />
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">
                  {{ authStore.userName }}
                </p>
                <p class="text-sm text-gray-500 truncate">
                  {{ authStore.userEmail }}
                </p>
              </div>
            </div>
            
            <button
              @click="handleLogout"
              class="w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-red-600 bg-red-50 rounded-md hover:bg-red-100 touch-target"
            >
              <ArrowRightOnRectangleIcon class="mr-2 h-4 w-4" />
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  Bars3Icon,
  XMarkIcon,
  HomeIcon,
  CalendarIcon,
  ChartBarIcon,
  TicketIcon,
  PresentationChartBarIcon,
  PlusIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  UserPlusIcon,
  UserIcon
} from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()
const isOpen = ref(false)

const handleLogout = async () => {
  await authStore.logout()
  isOpen.value = false
  router.push('/')
}

// Close menu when route changes
router.afterEach(() => {
  isOpen.value = false
})
</script>
