<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Ticket extends Model
{
    use HasFactory;

    protected $fillable = [
        'ticket_number',
        'booking_id',
        'event_id',
        'ticket_type_id',
        'holder_name',
        'holder_email',
        'holder_phone',
        'price',
        'qr_code',
        'qr_code_image_path',
        'status',
        'scanned_at',
        'scanned_by',
        'scan_notes',
        'custom_fields',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'scanned_at' => 'datetime',
        'custom_fields' => 'array',
    ];

    /**
     * Boot method to auto-generate ticket number and QR code
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($ticket) {
            if (empty($ticket->ticket_number)) {
                $ticket->ticket_number = 'TK' . date('Ymd') . str_pad(static::count() + 1, 6, '0', STR_PAD_LEFT);
            }

            if (empty($ticket->qr_code)) {
                $ticket->qr_code = Str::uuid()->toString();
            }
        });
    }

    /**
     * Relationships
     */
    public function booking()
    {
        return $this->belongsTo(Booking::class);
    }

    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    public function ticketType()
    {
        return $this->belongsTo(TicketType::class);
    }

    public function scannedBy()
    {
        return $this->belongsTo(User::class, 'scanned_by');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeUsed($query)
    {
        return $query->where('status', 'used');
    }

    public function scopeForEvent($query, $eventId)
    {
        return $query->where('event_id', $eventId);
    }

    /**
     * Accessors
     */
    public function getIsUsedAttribute()
    {
        return $this->status === 'used';
    }

    public function getIsActiveAttribute()
    {
        return $this->status === 'active';
    }

    public function getQrCodeUrlAttribute()
    {
        return route('ticket.verify', ['qr_code' => $this->qr_code]);
    }

    /**
     * Methods
     */
    public function markAsUsed($scannedBy = null, $notes = null)
    {
        $this->update([
            'status' => 'used',
            'scanned_at' => now(),
            'scanned_by' => $scannedBy,
            'scan_notes' => $notes,
        ]);
    }
}
