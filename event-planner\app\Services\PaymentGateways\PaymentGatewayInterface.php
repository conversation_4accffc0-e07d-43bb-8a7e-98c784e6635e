<?php

namespace App\Services\PaymentGateways;

interface PaymentGatewayInterface
{
    /**
     * Create payment order
     */
    public function createOrder(array $data): array;

    /**
     * Verify payment
     */
    public function verifyPayment(array $data): array;

    /**
     * Process refund
     */
    public function refund(array $data): array;

    /**
     * Get payment status
     */
    public function getPaymentStatus(string $paymentId): array;

    /**
     * Test connection
     */
    public function testConnection(): array;
}
