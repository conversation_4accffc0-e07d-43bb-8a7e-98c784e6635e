<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\EventCategory;

class CategoryController extends Controller
{
    /**
     * Get all categories
     */
    public function index()
    {
        $categories = EventCategory::withCount('events')
                                  ->orderBy('name')
                                  ->get();

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * Get single category with events
     */
    public function show(EventCategory $category)
    {
        $category->load(['events' => function($query) {
            $query->where('status', 'published')
                  ->where('end_date', '>=', now())
                  ->orderBy('start_date');
        }]);

        return response()->json([
            'success' => true,
            'data' => $category
        ]);
    }
}
