<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-lg text-center max-w-2xl">
            <h1 class="text-3xl font-bold text-blue-600 mb-6">🔍 Debug Information</h1>
            
            <div class="space-y-4 text-left">
                <div class="bg-gray-100 p-4 rounded">
                    <h3 class="font-bold">Browser Information:</h3>
                    <p id="browser-info">Loading...</p>
                </div>
                
                <div class="bg-gray-100 p-4 rounded">
                    <h3 class="font-bold">JavaScript Status:</h3>
                    <p id="js-status">✅ JavaScript is working</p>
                </div>
                
                <div class="bg-gray-100 p-4 rounded">
                    <h3 class="font-bold">Network Test:</h3>
                    <p id="network-status">Testing...</p>
                </div>
                
                <div class="bg-gray-100 p-4 rounded">
                    <h3 class="font-bold">Vue.js CDN Test:</h3>
                    <p id="vue-status">Testing...</p>
                </div>
                
                <div class="bg-gray-100 p-4 rounded">
                    <h3 class="font-bold">Console Logs:</h3>
                    <div id="console-logs" class="text-sm bg-black text-green-400 p-2 rounded font-mono max-h-32 overflow-y-auto"></div>
                </div>
            </div>
            
            <div class="mt-6 space-x-4">
                <a href="/" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Back to Main App</a>
                <a href="/simple" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Laravel Test</a>
                <button onclick="testVue()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">Test Vue.js</button>
            </div>
        </div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const logs = [];
        
        function addLog(type, message) {
            logs.push(`[${type}] ${message}`);
            updateConsoleLogs();
        }
        
        console.log = function(...args) {
            addLog('LOG', args.join(' '));
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addLog('ERROR', args.join(' '));
            originalError.apply(console, args);
        };
        
        function updateConsoleLogs() {
            const element = document.getElementById('console-logs');
            if (element) {
                element.innerHTML = logs.slice(-10).join('<br>');
                element.scrollTop = element.scrollHeight;
            }
        }
        
        // Browser info
        document.getElementById('browser-info').innerHTML = `
            ${navigator.userAgent}<br>
            <strong>Cookies enabled:</strong> ${navigator.cookieEnabled}<br>
            <strong>Language:</strong> ${navigator.language}<br>
            <strong>Platform:</strong> ${navigator.platform}
        `;
        
        // Network test
        fetch('/test')
            .then(response => response.json())
            .then(data => {
                document.getElementById('network-status').innerHTML = `✅ ${data.message}`;
            })
            .catch(error => {
                document.getElementById('network-status').innerHTML = `❌ Network error: ${error.message}`;
            });
        
        // Vue.js CDN test
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/vue@3/dist/vue.global.prod.js';
        script.onload = function() {
            if (typeof Vue !== 'undefined') {
                document.getElementById('vue-status').innerHTML = '✅ Vue.js CDN loaded successfully';
                console.log('Vue.js version:', Vue.version);
            } else {
                document.getElementById('vue-status').innerHTML = '❌ Vue.js CDN failed to load';
            }
        };
        script.onerror = function() {
            document.getElementById('vue-status').innerHTML = '❌ Vue.js CDN failed to load (network error)';
        };
        document.head.appendChild(script);
        
        function testVue() {
            if (typeof Vue !== 'undefined') {
                try {
                    const { createApp } = Vue;
                    const app = createApp({
                        data() {
                            return { message: 'Vue.js is working!' }
                        },
                        template: '<div class="bg-green-100 p-4 rounded mt-4"><h3 class="text-green-800">{{ message }}</h3></div>'
                    });
                    
                    const container = document.createElement('div');
                    container.id = 'vue-test';
                    document.body.appendChild(container);
                    app.mount('#vue-test');
                    
                    alert('Vue.js test successful! Check below for the mounted component.');
                } catch (error) {
                    alert('Vue.js test failed: ' + error.message);
                    console.error('Vue test error:', error);
                }
            } else {
                alert('Vue.js is not loaded yet. Wait a moment and try again.');
            }
        }
        
        console.log('Debug page loaded successfully');
    </script>
</body>
</html>
