<template>
  <AdminLayout>
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">System Settings</h1>
          <p class="text-gray-600">Configure API integrations, payment gateways, and system preferences</p>
        </div>
        <div class="flex space-x-3">
          <button @click="testAllConnections"
                  class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            Test All Connections
          </button>
          <button @click="saveAllSettings"
                  :disabled="!hasUnsavedChanges"
                  class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
            Save All Changes
          </button>
        </div>
      </div>

      <!-- Settings Navigation -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8 px-6">
            <button v-for="tab in settingsTabs" :key="tab.key"
                    @click="activeTab = tab.key"
                    :class="[
                      activeTab === tab.key
                        ? 'border-indigo-500 text-indigo-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                      'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm'
                    ]">
              <div class="flex items-center">
                <component :is="tab.icon" class="w-5 h-5 mr-2" />
                {{ tab.name }}
                <span v-if="getTabStatus(tab.key)"
                      :class="getTabStatus(tab.key) === 'connected' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                      class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getTabStatus(tab.key) === 'connected' ? 'Connected' : 'Not Connected' }}
                </span>
              </div>
            </button>
          </nav>
        </div>

        <!-- Settings Content -->
        <div class="p-6">
          <!-- Authentication Settings -->
          <div v-if="activeTab === 'auth'" class="space-y-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">OTP-less Authentication</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Provider</label>
                  <select v-model="settings.auth.provider"
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">Select Provider</option>
                    <option value="otpless">OTPless</option>
                    <option value="firebase">Firebase Auth</option>
                    <option value="auth0">Auth0</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                  <input v-model="settings.auth.api_key" type="password"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">App ID</label>
                  <input v-model="settings.auth.app_id" type="text"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Callback URL</label>
                  <input v-model="settings.auth.callback_url" type="url"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
              </div>
              <div class="mt-4 flex space-x-3">
                <button @click="testConnection('auth')"
                        class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                  Test Connection
                </button>
                <button @click="saveSettings('auth')"
                        class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                  Save
                </button>
              </div>
            </div>
          </div>

          <!-- Payment Settings -->
          <div v-if="activeTab === 'payment'" class="space-y-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Gateway Configuration</h3>

              <!-- Primary Gateway -->
              <div class="mb-6">
                <h4 class="text-md font-medium text-gray-800 mb-3">Primary Gateway</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Gateway</label>
                    <select v-model="settings.payment.primary_gateway"
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                      <option value="">Select Gateway</option>
                      <option value="razorpay">Razorpay</option>
                      <option value="phonepe">PhonePe</option>
                      <option value="payu">PayU</option>
                      <option value="stripe">Stripe</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Mode</label>
                    <select v-model="settings.payment.mode"
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                      <option value="test">Test Mode</option>
                      <option value="live">Live Mode</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                    <input v-model="settings.payment.api_key" type="password"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Secret Key</label>
                    <input v-model="settings.payment.secret_key" type="password"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Webhook Secret</label>
                    <input v-model="settings.payment.webhook_secret" type="password"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                    <select v-model="settings.payment.currency"
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                      <option value="INR">INR (₹)</option>
                      <option value="USD">USD ($)</option>
                      <option value="EUR">EUR (€)</option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- UPI Settings -->
              <div class="mb-6">
                <h4 class="text-md font-medium text-gray-800 mb-3">UPI Configuration</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">UPI ID</label>
                    <input v-model="settings.payment.upi_id" type="text"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Merchant Name</label>
                    <input v-model="settings.payment.merchant_name" type="text"
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                </div>
              </div>

              <div class="flex space-x-3">
                <button @click="testConnection('payment')"
                        class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                  Test Payment
                </button>
                <button @click="saveSettings('payment')"
                        class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                  Save
                </button>
              </div>
            </div>
          </div>

          <!-- SMS Settings -->
          <div v-if="activeTab === 'sms'" class="space-y-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">SMS Gateway Configuration</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Provider</label>
                  <select v-model="settings.sms.provider"
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">Select Provider</option>
                    <option value="twilio">Twilio</option>
                    <option value="msg91">MSG91</option>
                    <option value="textlocal">TextLocal</option>
                    <option value="aws_sns">AWS SNS</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                  <input v-model="settings.sms.api_key" type="password"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Sender ID</label>
                  <input v-model="settings.sms.sender_id" type="text"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Template ID (Optional)</label>
                  <input v-model="settings.sms.template_id" type="text"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
              </div>
              <div class="mt-4 flex space-x-3">
                <button @click="testConnection('sms')"
                        class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                  Send Test SMS
                </button>
                <button @click="saveSettings('sms')"
                        class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                  Save
                </button>
              </div>
            </div>
          </div>

          <!-- WhatsApp Settings -->
          <div v-if="activeTab === 'whatsapp'" class="space-y-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">WhatsApp Business API</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Provider</label>
                  <select v-model="settings.whatsapp.provider"
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">Select Provider</option>
                    <option value="gupshup">Gupshup</option>
                    <option value="gallabox">Gallabox</option>
                    <option value="twilio">Twilio</option>
                    <option value="meta">Meta Business</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">API Token</label>
                  <input v-model="settings.whatsapp.api_token" type="password"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number ID</label>
                  <input v-model="settings.whatsapp.phone_number_id" type="text"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Business Account ID</label>
                  <input v-model="settings.whatsapp.business_account_id" type="text"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
              </div>

              <!-- Message Templates -->
              <div class="mt-6">
                <h4 class="text-md font-medium text-gray-800 mb-3">Message Templates</h4>
                <div class="space-y-4">
                  <div v-for="template in settings.whatsapp.templates" :key="template.name"
                       class="border border-gray-200 rounded-lg p-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
                        <input v-model="template.name" type="text"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Template ID</label>
                        <input v-model="template.id" type="text"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Language</label>
                        <select v-model="template.language"
                                class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                          <option value="en">English</option>
                          <option value="hi">Hindi</option>
                          <option value="en_US">English (US)</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-4 flex space-x-3">
                <button @click="testConnection('whatsapp')"
                        class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                  Send Test Message
                </button>
                <button @click="saveSettings('whatsapp')"
                        class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                  Save
                </button>
              </div>
            </div>
          </div>

          <!-- Email Settings -->
          <div v-if="activeTab === 'email'" class="space-y-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Email Configuration</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">SMTP Host</label>
                  <input v-model="settings.email.smtp_host" type="text"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">SMTP Port</label>
                  <input v-model="settings.email.smtp_port" type="number"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                  <input v-model="settings.email.username" type="text"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                  <input v-model="settings.email.password" type="password"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">From Email</label>
                  <input v-model="settings.email.from_email" type="email"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">From Name</label>
                  <input v-model="settings.email.from_name" type="text"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Encryption</label>
                  <select v-model="settings.email.encryption"
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="tls">TLS</option>
                    <option value="ssl">SSL</option>
                    <option value="">None</option>
                  </select>
                </div>
              </div>
              <div class="mt-4 flex space-x-3">
                <button @click="testConnection('email')"
                        class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                  Send Test Email
                </button>
                <button @click="saveSettings('email')"
                        class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                  Save
                </button>
              </div>
            </div>
          </div>

          <!-- Analytics Settings -->
          <div v-if="activeTab === 'analytics'" class="space-y-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Analytics & Tracking</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Google Analytics ID</label>
                  <input v-model="settings.analytics.google_analytics_id" type="text"
                         placeholder="G-XXXXXXXXXX"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Meta Pixel ID</label>
                  <input v-model="settings.analytics.meta_pixel_id" type="text"
                         placeholder="123456789012345"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Google Tag Manager ID</label>
                  <input v-model="settings.analytics.google_tag_manager_id" type="text"
                         placeholder="GTM-XXXXXXX"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
              </div>
              <div class="mt-4 flex space-x-3">
                <button @click="testConnection('analytics')"
                        class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                  Verify Tracking
                </button>
                <button @click="saveSettings('analytics')"
                        class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                  Save
                </button>
              </div>
            </div>
          </div>

          <!-- Google Maps Settings -->
          <div v-if="activeTab === 'maps'" class="space-y-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">Google Maps Configuration</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700 mb-2">Google Maps API Key</label>
                  <input v-model="settings.maps.google_maps_api_key" type="password"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  <p class="text-xs text-gray-500 mt-1">Required for displaying event locations on maps</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Default Zoom Level</label>
                  <input v-model="settings.maps.default_zoom" type="number" min="1" max="20"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Default Center (Latitude)</label>
                  <input v-model="settings.maps.default_center.lat" type="number" step="0.000001"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Default Center (Longitude)</label>
                  <input v-model="settings.maps.default_center.lng" type="number" step="0.000001"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
              </div>
              <div class="mt-4 flex space-x-3">
                <button @click="testConnection('maps')"
                        class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                  Test Maps API
                </button>
                <button @click="saveSettings('maps')"
                        class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                  Save
                </button>
              </div>
            </div>
          </div>

          <!-- SEO Settings -->
          <div v-if="activeTab === 'seo'" class="space-y-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">SEO & Meta Configuration</h3>
              <div class="space-y-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Default Meta Title</label>
                  <input v-model="settings.seo.default_meta_title" type="text"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  <p class="text-xs text-gray-500 mt-1">Used when events don't have custom meta titles</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Default Meta Description</label>
                  <textarea v-model="settings.seo.default_meta_description" rows="3"
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
                  <p class="text-xs text-gray-500 mt-1">Used when events don't have custom meta descriptions</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Default OG Image URL</label>
                  <input v-model="settings.seo.default_og_image" type="url"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  <p class="text-xs text-gray-500 mt-1">Default image for social media sharing</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                  <input v-model="settings.seo.site_name" type="text"
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div class="flex items-center">
                  <input v-model="settings.seo.enable_json_ld" type="checkbox"
                         class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                  <label class="ml-2 text-sm text-gray-700">Enable JSON-LD structured data</label>
                </div>
              </div>
              <div class="mt-4 flex space-x-3">
                <button @click="testConnection('seo')"
                        class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                  Validate SEO
                </button>
                <button @click="saveSettings('seo')"
                        class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import AdminLayout from '@/components/admin/AdminLayout.vue'

const activeTab = ref('auth')
const hasUnsavedChanges = ref(false)

const settingsTabs = ref([
  {
    key: 'auth',
    name: 'Authentication',
    icon: 'svg'
  },
  {
    key: 'payment',
    name: 'Payments',
    icon: 'svg'
  },
  {
    key: 'sms',
    name: 'SMS Gateway',
    icon: 'svg'
  },
  {
    key: 'whatsapp',
    name: 'WhatsApp API',
    icon: 'svg'
  },
  {
    key: 'email',
    name: 'Email Settings',
    icon: 'svg'
  },
  {
    key: 'analytics',
    name: 'Analytics',
    icon: 'svg'
  },
  {
    key: 'maps',
    name: 'Google Maps',
    icon: 'svg'
  },
  {
    key: 'seo',
    name: 'SEO/Meta',
    icon: 'svg'
  }
])

const settings = reactive({
  auth: {
    provider: 'otpless',
    api_key: '',
    app_id: '',
    callback_url: 'https://yourdomain.com/auth/callback'
  },
  payment: {
    primary_gateway: 'razorpay',
    mode: 'test',
    api_key: '',
    secret_key: '',
    webhook_secret: '',
    currency: 'INR',
    upi_id: '',
    merchant_name: ''
  },
  sms: {
    provider: 'msg91',
    api_key: '',
    sender_id: '',
    template_id: ''
  },
  whatsapp: {
    provider: 'gupshup',
    api_token: '',
    phone_number_id: '',
    business_account_id: '',
    templates: [
      {
        name: 'booking_confirmation',
        id: '',
        language: 'en'
      },
      {
        name: 'event_reminder',
        id: '',
        language: 'en'
      },
      {
        name: 'ticket_delivery',
        id: '',
        language: 'en'
      }
    ]
  },
  email: {
    smtp_host: 'smtp.gmail.com',
    smtp_port: 587,
    username: '',
    password: '',
    from_email: '',
    from_name: 'Event Manager',
    encryption: 'tls'
  },
  analytics: {
    google_analytics_id: '',
    meta_pixel_id: '',
    google_tag_manager_id: ''
  },
  maps: {
    google_maps_api_key: '',
    default_zoom: 15,
    default_center: {
      lat: 28.6139,
      lng: 77.2090
    }
  },
  seo: {
    default_meta_title: 'Event Manager - Book Amazing Events',
    default_meta_description: 'Discover and book amazing events near you',
    default_og_image: '',
    enable_json_ld: true,
    site_name: 'Event Manager'
  }
})

const connectionStatus = reactive({
  auth: 'disconnected',
  payment: 'disconnected',
  sms: 'disconnected',
  whatsapp: 'disconnected',
  email: 'disconnected',
  analytics: 'connected',
  maps: 'connected',
  seo: 'connected'
})

const getTabStatus = (tabKey) => {
  return connectionStatus[tabKey]
}

const testConnection = async (service) => {
  // Simulate API testing
  connectionStatus[service] = 'testing'

  setTimeout(() => {
    // Simulate random success/failure
    connectionStatus[service] = Math.random() > 0.3 ? 'connected' : 'disconnected'

    const status = connectionStatus[service]
    const message = status === 'connected'
      ? `${service.toUpperCase()} connection successful!`
      : `${service.toUpperCase()} connection failed. Please check your credentials.`

    alert(message)
  }, 2000)
}

const saveSettings = (service) => {
  // Simulate saving settings
  hasUnsavedChanges.value = false
  alert(`${service.toUpperCase()} settings saved successfully!`)
}

const saveAllSettings = () => {
  // Simulate saving all settings
  hasUnsavedChanges.value = false
  alert('All settings saved successfully!')
}

const testAllConnections = async () => {
  const services = ['auth', 'payment', 'sms', 'whatsapp', 'email']

  for (const service of services) {
    if (settings[service] && Object.values(settings[service]).some(val => val !== '')) {
      await testConnection(service)
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }
}

// Watch for changes to mark as unsaved
const markAsUnsaved = () => {
  hasUnsavedChanges.value = true
}

// Add watchers for all settings
Object.keys(settings).forEach(key => {
  // This would normally use a deep watcher
  // watch(() => settings[key], markAsUnsaved, { deep: true })
})
</script>