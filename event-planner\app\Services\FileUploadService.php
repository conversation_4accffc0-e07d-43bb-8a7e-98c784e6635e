<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class FileUploadService
{
    protected $imageManager;
    protected $allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    protected $allowedDocumentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
    protected $maxFileSize = 10 * 1024 * 1024; // 10MB

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Upload single file
     */
    public function uploadFile(UploadedFile $file, string $directory = 'uploads', array $options = []): array
    {
        try {
            // Validate file
            $validation = $this->validateFile($file, $options);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'message' => $validation['message'],
                ];
            }

            // Generate unique filename
            $filename = $this->generateFilename($file);
            $path = $directory . '/' . $filename;

            // Store file
            $storedPath = Storage::disk('public')->putFileAs($directory, $file, $filename);

            $fileData = [
                'original_name' => $file->getClientOriginalName(),
                'filename' => $filename,
                'path' => $storedPath,
                'url' => Storage::disk('public')->url($storedPath),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'extension' => $file->getClientOriginalExtension(),
                'type' => $this->getFileType($file),
            ];

            // Process image if it's an image file
            if ($this->isImage($file)) {
                $imageData = $this->processImage($file, $storedPath, $options);
                $fileData = array_merge($fileData, $imageData);
            }

            return [
                'success' => true,
                'data' => $fileData,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'File upload failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Upload multiple files
     */
    public function uploadMultipleFiles(array $files, string $directory = 'uploads', array $options = []): array
    {
        $results = [];
        $successCount = 0;
        $errors = [];

        foreach ($files as $index => $file) {
            if ($file instanceof UploadedFile) {
                $result = $this->uploadFile($file, $directory, $options);
                
                if ($result['success']) {
                    $results[] = $result['data'];
                    $successCount++;
                } else {
                    $errors[] = "File {$index}: " . $result['message'];
                }
            }
        }

        return [
            'success' => $successCount > 0,
            'uploaded' => $successCount,
            'total' => count($files),
            'data' => $results,
            'errors' => $errors,
        ];
    }

    /**
     * Process image (resize, create thumbnails)
     */
    protected function processImage(UploadedFile $file, string $storedPath, array $options = []): array
    {
        $imageData = [];
        $fullPath = Storage::disk('public')->path($storedPath);

        try {
            $image = $this->imageManager->read($fullPath);
            
            // Get image dimensions
            $imageData['width'] = $image->width();
            $imageData['height'] = $image->height();

            // Create thumbnails if requested
            if (isset($options['create_thumbnails']) && $options['create_thumbnails']) {
                $thumbnails = $this->createThumbnails($image, $storedPath, $options['thumbnail_sizes'] ?? []);
                $imageData['thumbnails'] = $thumbnails;
            }

            // Resize main image if requested
            if (isset($options['resize'])) {
                $resizeOptions = $options['resize'];
                $resizedImage = $image->resize(
                    $resizeOptions['width'] ?? null,
                    $resizeOptions['height'] ?? null,
                    function ($constraint) use ($resizeOptions) {
                        if ($resizeOptions['maintain_aspect'] ?? true) {
                            $constraint->aspectRatio();
                        }
                        if ($resizeOptions['prevent_upsizing'] ?? true) {
                            $constraint->upsize();
                        }
                    }
                );

                $resizedImage->save($fullPath, $resizeOptions['quality'] ?? 85);
                
                $imageData['width'] = $resizedImage->width();
                $imageData['height'] = $resizedImage->height();
            }

        } catch (\Exception $e) {
            // Log error but don't fail the upload
            \Log::warning('Image processing failed', [
                'file' => $storedPath,
                'error' => $e->getMessage(),
            ]);
        }

        return $imageData;
    }

    /**
     * Create thumbnails
     */
    protected function createThumbnails($image, string $originalPath, array $sizes = []): array
    {
        $defaultSizes = [
            'small' => ['width' => 150, 'height' => 150],
            'medium' => ['width' => 300, 'height' => 300],
            'large' => ['width' => 600, 'height' => 600],
        ];

        $sizes = array_merge($defaultSizes, $sizes);
        $thumbnails = [];

        foreach ($sizes as $name => $size) {
            try {
                $thumbnailPath = $this->getThumbnailPath($originalPath, $name);
                $thumbnailFullPath = Storage::disk('public')->path($thumbnailPath);

                // Create directory if it doesn't exist
                $directory = dirname($thumbnailFullPath);
                if (!file_exists($directory)) {
                    mkdir($directory, 0755, true);
                }

                $thumbnail = clone $image;
                $thumbnail->resize($size['width'], $size['height'], function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

                $thumbnail->save($thumbnailFullPath, 85);

                $thumbnails[$name] = [
                    'path' => $thumbnailPath,
                    'url' => Storage::disk('public')->url($thumbnailPath),
                    'width' => $thumbnail->width(),
                    'height' => $thumbnail->height(),
                ];

            } catch (\Exception $e) {
                \Log::warning('Thumbnail creation failed', [
                    'size' => $name,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $thumbnails;
    }

    /**
     * Delete file and its thumbnails
     */
    public function deleteFile(string $path): bool
    {
        try {
            // Delete main file
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
            }

            // Delete thumbnails
            $this->deleteThumbnails($path);

            return true;

        } catch (\Exception $e) {
            \Log::error('File deletion failed', [
                'path' => $path,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Delete thumbnails
     */
    protected function deleteThumbnails(string $originalPath): void
    {
        $sizes = ['small', 'medium', 'large'];
        
        foreach ($sizes as $size) {
            $thumbnailPath = $this->getThumbnailPath($originalPath, $size);
            if (Storage::disk('public')->exists($thumbnailPath)) {
                Storage::disk('public')->delete($thumbnailPath);
            }
        }
    }

    /**
     * Validate file
     */
    protected function validateFile(UploadedFile $file, array $options = []): array
    {
        // Check file size
        $maxSize = $options['max_size'] ?? $this->maxFileSize;
        if ($file->getSize() > $maxSize) {
            return [
                'valid' => false,
                'message' => 'File size exceeds maximum allowed size of ' . $this->formatFileSize($maxSize),
            ];
        }

        // Check file type
        $allowedTypes = $options['allowed_types'] ?? array_merge($this->allowedImageTypes, $this->allowedDocumentTypes);
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($extension, $allowedTypes)) {
            return [
                'valid' => false,
                'message' => 'File type not allowed. Allowed types: ' . implode(', ', $allowedTypes),
            ];
        }

        // Check if file is actually uploaded
        if (!$file->isValid()) {
            return [
                'valid' => false,
                'message' => 'Invalid file upload',
            ];
        }

        return ['valid' => true];
    }

    /**
     * Generate unique filename
     */
    protected function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $name = Str::slug($name);
        
        return $name . '_' . time() . '_' . Str::random(8) . '.' . $extension;
    }

    /**
     * Get thumbnail path
     */
    protected function getThumbnailPath(string $originalPath, string $size): string
    {
        $pathInfo = pathinfo($originalPath);
        return $pathInfo['dirname'] . '/thumbnails/' . $size . '_' . $pathInfo['basename'];
    }

    /**
     * Check if file is an image
     */
    protected function isImage(UploadedFile $file): bool
    {
        $extension = strtolower($file->getClientOriginalExtension());
        return in_array($extension, $this->allowedImageTypes);
    }

    /**
     * Get file type
     */
    protected function getFileType(UploadedFile $file): string
    {
        if ($this->isImage($file)) {
            return 'image';
        }

        $extension = strtolower($file->getClientOriginalExtension());
        if (in_array($extension, $this->allowedDocumentTypes)) {
            return 'document';
        }

        return 'other';
    }

    /**
     * Format file size
     */
    protected function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
