import{d as ye,r as _,c as T,u as he,a as ke,o as K,b as ue,e as ge,f as d,g as a,h as e,i as z,j as we,F as J,k as W,l as X,w as I,m as O,t as u,n as R,p as F,q as _e,s as ie,v as me,x as c,y as M,z as L,A as re,B as de,C as $e}from"./vue-vendor-BupLktX_.js";import{C as le,a as ce,L as pe,P as Ce,b as Se,p as ve,c as xe,d as fe,i as Me,B as Ue}from"./chart-vendor-Db3utXXw.js";const be=ye("auth",()=>{const P=_(null),v=_(localStorage.getItem("auth_token")),U=_(!1),x=_(null),o=T(()=>!!v.value&&!!P.value),V=T(()=>{var C;return((C=P.value)==null?void 0:C.role)||"attendee"}),g=T(()=>{var C;return((C=P.value)==null?void 0:C.name)||""}),y=T(()=>{var C;return((C=P.value)==null?void 0:C.email)||""}),r=async C=>{U.value=!0,x.value=null;try{const k=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(C)}),l=await k.json();if(!k.ok)throw new Error(l.message||"Login failed");if(l.success)return P.value=l.data.user,v.value=l.data.token,localStorage.setItem("auth_token",l.data.token),localStorage.setItem("user",JSON.stringify(l.data.user)),{success:!0,user:l.data.user};throw new Error(l.message||"Login failed")}catch(k){return x.value=k.message||"Login failed",{success:!1,error:x.value}}finally{U.value=!1}},f=async C=>{U.value=!0,x.value=null;try{const k=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(C)}),l=await k.json();if(!k.ok)throw new Error(l.message||"Registration failed");if(l.success)return P.value=l.data.user,v.value=l.data.token,localStorage.setItem("auth_token",l.data.token),localStorage.setItem("user",JSON.stringify(l.data.user)),{success:!0,user:l.data.user};throw new Error(l.message||"Registration failed")}catch(k){return x.value=k.message||"Registration failed",{success:!1,error:x.value}}finally{U.value=!1}},h=async()=>{P.value=null,v.value=null,x.value=null,localStorage.removeItem("auth_token"),localStorage.removeItem("user")};return{user:P,token:v,isLoading:U,error:x,isAuthenticated:o,userRole:V,userName:g,userEmail:y,login:r,register:f,logout:h,fetchUser:async()=>{if(v.value)try{const C=localStorage.getItem("user");C&&(P.value=JSON.parse(C))}catch(C){console.error("Failed to fetch user:",C),h()}},hasRole:C=>P.value?(typeof C=="string"&&(C=[C]),C.includes(P.value.role)):!1,updateProfile:async C=>{U.value=!0,x.value=null;try{if(P.value)return P.value={...P.value,...C},localStorage.setItem("user",JSON.stringify(P.value)),{success:!0};throw new Error("No user logged in")}catch(k){return x.value=k.message||"Profile update failed",{success:!1,error:x.value}}finally{U.value=!1}},initializeAuth:()=>{const C=localStorage.getItem("user"),k=localStorage.getItem("auth_token");if(C&&k)try{P.value=JSON.parse(C),v.value=k}catch(l){console.error("Failed to parse stored user data:",l),h()}else{const l={id:1,name:"Admin User",email:"<EMAIL>",role:"admin"},D="admin-demo-token-12345";P.value=l,v.value=D,localStorage.setItem("auth_token",D),localStorage.setItem("user",JSON.stringify(l)),console.log("Auto-initialized admin demo user")}}}}),Be={class:"min-h-screen bg-gray-50 flex"},Te={class:"mt-8 px-4"},Ve={class:"space-y-2"},je={key:0,class:"mr-3 h-5 w-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ae={key:1,class:"mr-3 h-5 w-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Pe={key:2,class:"mr-3 h-5 w-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ze={key:3,class:"mr-3 h-5 w-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},De={key:4,class:"mr-3 h-5 w-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ie={key:5,class:"mr-3 h-5 w-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ee={key:6,class:"mr-3 h-5 w-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Le={key:7,class:"mr-3 h-5 w-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Re={key:8,class:"mr-3 h-5 w-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ne={key:9,class:"mr-3 h-5 w-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Fe={key:10,class:"mr-3 h-5 w-5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Oe={key:11,class:"ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full"},Je={class:"mt-8 pt-6 border-t border-gray-700"},We={class:"px-3 py-2"},He={class:"mt-1 text-sm text-gray-300"},Ze={class:"flex-1 flex flex-col lg:pl-0"},Ge={class:"sticky top-0 z-40 flex h-16 bg-white border-b border-gray-200 lg:border-none"},Xe={class:"flex-1 px-4 flex justify-between items-center"},qe={class:"flex-1"},Qe={class:"flex","aria-label":"Breadcrumb"},Ke={class:"flex items-center space-x-2"},Ye={class:"flex items-center"},et={class:"flex items-center"},tt={class:"ml-2 text-sm font-medium text-gray-500"},st={class:"mt-1 text-2xl font-semibold text-gray-900"},ot={class:"ml-4 flex items-center space-x-4"},nt={class:"relative p-2 text-gray-400 hover:text-gray-500"},lt={key:0,class:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"},at={class:"relative"},it={key:0,class:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50"},rt={class:"relative"},dt={class:"h-8 w-8 bg-indigo-600 rounded-full flex items-center justify-center"},ut={class:"text-white font-medium"},gt={class:"hidden md:block text-gray-700 font-medium"},mt={key:0,class:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50"},ct={class:"flex-1 overflow-y-auto"},pt={class:"py-6"},vt={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},oe={__name:"AdminLayout",setup(P){const v=he(),U=ke(),x=be(),o=_(!1),V=_(!1),g=_(!1),y=_(3),r=T(()=>{const k=[{name:"Dashboard",href:"/admin",icon:"HomeIcon"},{name:"Events",href:"/admin/events",icon:"CalendarIcon"},{name:"Bookings",href:"/admin/bookings",icon:"TicketIcon",badge:"12"},{name:"Users",href:"/admin/users",icon:"UsersIcon"},{name:"Scanner",href:"/admin/scanner",icon:"QrCodeIcon"},{name:"Reports",href:"/admin/reports",icon:"ChartBarIcon"},{name:"Notifications",href:"/admin/notifications",icon:"BellIcon"},{name:"Media Library",href:"/admin/media",icon:"PhotographIcon"}],l=localStorage.getItem("user_role")||"attendee";return["admin"].includes(l)&&k.push({name:"Roles",href:"/admin/roles",icon:"ShieldCheckIcon"},{name:"Settings",href:"/admin/settings",icon:"CogIcon"},{name:"System Logs",href:"/admin/logs",icon:"DocumentTextIcon"},{name:"Maintenance",href:"/admin/maintenance",icon:"CogIcon"}),k}),f=T(()=>{var k;return((k=x.user)==null?void 0:k.role)||"Admin"}),h=T(()=>{var k;return((k=x.user)==null?void 0:k.name)||"Admin User"}),j=T(()=>h.value.split(" ").map(l=>l[0]).join("").toUpperCase()),t=T(()=>{const k=v.name;return{"admin-dashboard":"Dashboard","admin-events":"Events Management","admin-bookings":"Bookings Management","admin-users":"User Management","admin-reports":"Reports & Analytics","admin-roles":"Role Management","admin-settings":"System Settings"}[k]||"Admin Panel"}),s=T(()=>v.path.split("/").filter(Boolean).slice(1).map(D=>D.charAt(0).toUpperCase()+D.slice(1).replace("-"," "))),E=async()=>{await x.logout(),U.push("/login")},C=k=>{k.target.closest(".relative")||(V.value=!1,g.value=!1)};return K(()=>{document.addEventListener("click",C)}),ue(()=>{document.removeEventListener("click",C)}),(k,l)=>{const D=ge("router-link");return a(),d("div",Be,[e("div",{class:R([o.value?"translate-x-0":"-translate-x-full","fixed inset-y-0 left-0 z-50 w-64 bg-gray-900 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"])},[l[16]||(l[16]=we('<div class="flex items-center justify-center h-16 px-4 bg-gray-800"><div class="flex items-center space-x-2"><div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center"><svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path d="M10 2L3 7v11a1 1 0 001 1h3v-6h6v6h3a1 1 0 001-1V7l-7-5z"></path></svg></div><span class="text-xl font-bold text-white">Admin Panel</span></div></div>',1)),e("nav",Te,[e("div",Ve,[(a(!0),d(J,null,W(r.value,A=>(a(),X(D,{key:A.name,to:A.href,class:R([k.$route.path.startsWith(A.href)?"bg-gray-800 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white","group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"])},{default:I(()=>[A.icon==="HomeIcon"?(a(),d("svg",je,l[4]||(l[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"},null,-1)]))):A.icon==="CalendarIcon"?(a(),d("svg",Ae,l[5]||(l[5]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"},null,-1)]))):A.icon==="TicketIcon"?(a(),d("svg",Pe,l[6]||(l[6]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"},null,-1)]))):A.icon==="UsersIcon"?(a(),d("svg",ze,l[7]||(l[7]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"},null,-1)]))):A.icon==="QrCodeIcon"?(a(),d("svg",De,l[8]||(l[8]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 16h4.01M12 8h4.01M8 12h.01M16 8h.01M8 16h.01M8 8h.01"},null,-1)]))):A.icon==="ShieldCheckIcon"?(a(),d("svg",Ie,l[9]||(l[9]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"},null,-1)]))):A.icon==="ChartBarIcon"?(a(),d("svg",Ee,l[10]||(l[10]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},null,-1)]))):A.icon==="BellIcon"?(a(),d("svg",Le,l[11]||(l[11]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10.07 2.82l-.908.908a1.5 1.5 0 000 2.121l9.192 9.192a1.5 1.5 0 002.121 0l.908-.908a1.5 1.5 0 000-2.121L12.191 2.82a1.5 1.5 0 00-2.121 0z"},null,-1)]))):A.icon==="PhotographIcon"?(a(),d("svg",Re,l[12]||(l[12]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"},null,-1)]))):A.icon==="CogIcon"?(a(),d("svg",Ne,l[13]||(l[13]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},null,-1),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1)]))):A.icon==="DocumentTextIcon"?(a(),d("svg",Fe,l[14]||(l[14]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},null,-1)]))):z("",!0),O(" "+u(A.name)+" ",1),A.badge?(a(),d("span",Oe,u(A.badge),1)):z("",!0)]),_:2},1032,["to","class"]))),128))]),e("div",Je,[e("div",We,[l[15]||(l[15]=e("p",{class:"text-xs font-semibold text-gray-400 uppercase tracking-wider"},"Role",-1)),e("p",He,u(f.value),1)])])])],2),e("div",Ze,[e("div",Ge,[e("button",{onClick:l[0]||(l[0]=A=>o.value=!o.value),class:"px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 lg:hidden"},l[17]||(l[17]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h7"})],-1)])),e("div",Xe,[e("div",qe,[e("nav",Qe,[e("ol",Ke,[e("li",null,[e("div",Ye,[F(D,{to:"/admin",class:"text-gray-400 hover:text-gray-500"},{default:I(()=>l[18]||(l[18]=[e("svg",{class:"flex-shrink-0 h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"})],-1)])),_:1,__:[18]})])]),(a(!0),d(J,null,W(s.value,(A,G)=>(a(),d("li",{key:G},[e("div",et,[l[19]||(l[19]=e("svg",{class:"flex-shrink-0 h-5 w-5 text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)),e("span",tt,u(A),1)])]))),128))])]),e("h1",st,u(t.value),1)]),e("div",ot,[e("button",nt,[l[20]||(l[20]=e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10.07 2.82l-.908.908a1.5 1.5 0 000 2.121l9.192 9.192a1.5 1.5 0 002.121 0l.908-.908a1.5 1.5 0 000-2.121L12.191 2.82a1.5 1.5 0 00-2.121 0z"})],-1)),y.value>0?(a(),d("span",lt,u(y.value),1)):z("",!0)]),e("div",at,[e("button",{onClick:l[1]||(l[1]=A=>V.value=!V.value),class:"bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"}," Quick Actions "),V.value?(a(),d("div",it,[F(D,{to:"/admin/events/create",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"},{default:I(()=>l[21]||(l[21]=[O(" Create Event ")])),_:1,__:[21]}),F(D,{to:"/admin/users/create",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"},{default:I(()=>l[22]||(l[22]=[O(" Add User ")])),_:1,__:[22]}),F(D,{to:"/admin/reports",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"},{default:I(()=>l[23]||(l[23]=[O(" View Reports ")])),_:1,__:[23]})])):z("",!0)]),e("div",rt,[e("button",{onClick:l[2]||(l[2]=A=>g.value=!g.value),class:"flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-500"},[e("div",dt,[e("span",ut,u(j.value),1)]),e("span",gt,u(h.value),1),l[24]||(l[24]=e("svg",{class:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1))]),g.value?(a(),d("div",mt,[F(D,{to:"/admin/profile",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"},{default:I(()=>l[25]||(l[25]=[O(" Your Profile ")])),_:1,__:[25]}),F(D,{to:"/admin/settings",class:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"},{default:I(()=>l[26]||(l[26]=[O(" Settings ")])),_:1,__:[26]}),e("button",{onClick:E,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"}," Sign out ")])):z("",!0)])])])]),e("main",ct,[e("div",pt,[e("div",vt,[_e(k.$slots,"default")])])])]),o.value?(a(),d("div",{key:0,onClick:l[3]||(l[3]=A=>o.value=!1),class:"fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"})):z("",!0)])}}},xt={class:"relative"},ft={__name:"BookingTrendsChart",props:{data:{type:Object,default:()=>({labels:[],datasets:[]})},timeRange:{type:String,default:"30d"}},setup(P){le.register(ce,pe,Ce,Se,ve,xe,fe,Me);const v=P,U=_(null);let x=null;const o={labels:["Jan 1","Jan 5","Jan 10","Jan 15","Jan 20","Jan 25","Jan 30","Feb 1","Feb 5","Feb 10","Feb 15","Feb 20","Feb 25","Feb 28"],datasets:[{label:"Bookings",data:[12,19,15,25,22,30,28,35,32,45,38,52,48,55],borderColor:"rgb(99, 102, 241)",backgroundColor:"rgba(99, 102, 241, 0.1)",fill:!0,tension:.4,pointBackgroundColor:"rgb(99, 102, 241)",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6},{label:"Revenue (₹000s)",data:[8,12,10,18,15,22,20,25,23,32,28,38,35,42],borderColor:"rgb(16, 185, 129)",backgroundColor:"rgba(16, 185, 129, 0.1)",fill:!0,tension:.4,pointBackgroundColor:"rgb(16, 185, 129)",pointBorderColor:"#fff",pointBorderWidth:2,pointRadius:4,pointHoverRadius:6}]},V={responsive:!0,maintainAspectRatio:!1,interaction:{mode:"index",intersect:!1},plugins:{legend:{position:"top",labels:{usePointStyle:!0,padding:20,font:{size:12,weight:"500"}}},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",borderColor:"rgba(99, 102, 241, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!0,callbacks:{label:function(r){let f=r.dataset.label||"";return f&&(f+=": "),r.dataset.label==="Revenue (₹000s)"?f+="₹"+(r.parsed.y*1e3).toLocaleString():f+=r.parsed.y,f}}}},scales:{x:{display:!0,grid:{display:!1},ticks:{font:{size:11},color:"#6B7280"}},y:{display:!0,grid:{color:"rgba(0, 0, 0, 0.05)",drawBorder:!1},ticks:{font:{size:11},color:"#6B7280",callback:function(r){return r}}}},elements:{line:{borderWidth:3}}},g=()=>{x&&x.destroy();const r=U.value.getContext("2d");x=new le(r,{type:"line",data:v.data.labels.length>0?v.data:o,options:V})},y=()=>{x&&(x.data=v.data.labels.length>0?v.data:o,x.update("active"))};return ie(()=>v.data,y,{deep:!0}),ie(()=>v.timeRange,y),K(()=>{g()}),ue(()=>{x&&x.destroy()}),(r,f)=>(a(),d("div",xt,[e("canvas",{ref_key:"chartCanvas",ref:U},null,512)]))}},bt={class:"relative"},yt={__name:"EventPerformanceChart",props:{data:{type:Object,default:()=>({labels:[],datasets:[]})}},setup(P){le.register(ce,pe,Ue,ve,xe,fe);const v=P,U=_(null);let x=null;const o={labels:["Tech Conference","Music Festival","Art Exhibition","Food Festival","Sports Event"],datasets:[{label:"Bookings",data:[156,134,98,87,76],backgroundColor:["rgba(99, 102, 241, 0.8)","rgba(16, 185, 129, 0.8)","rgba(245, 158, 11, 0.8)","rgba(239, 68, 68, 0.8)","rgba(139, 92, 246, 0.8)"],borderColor:["rgb(99, 102, 241)","rgb(16, 185, 129)","rgb(245, 158, 11)","rgb(239, 68, 68)","rgb(139, 92, 246)"],borderWidth:2,borderRadius:6,borderSkipped:!1}]},V={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#fff",bodyColor:"#fff",borderColor:"rgba(99, 102, 241, 0.2)",borderWidth:1,cornerRadius:8,displayColors:!1,callbacks:{title:function(r){return r[0].label},label:function(r){return`Bookings: ${r.parsed.y}`}}}},scales:{x:{display:!0,grid:{display:!1},ticks:{font:{size:11},color:"#6B7280",maxRotation:45}},y:{display:!0,grid:{color:"rgba(0, 0, 0, 0.05)",drawBorder:!1},ticks:{font:{size:11},color:"#6B7280",beginAtZero:!0}}},elements:{bar:{borderWidth:2}}},g=()=>{x&&x.destroy();const r=U.value.getContext("2d");x=new le(r,{type:"bar",data:v.data.labels.length>0?v.data:o,options:V})};return ie(()=>v.data,()=>{x&&(x.data=v.data.labels.length>0?v.data:o,x.update("active"))},{deep:!0}),K(()=>{g()}),ue(()=>{x&&x.destroy()}),(r,f)=>(a(),d("div",bt,[e("canvas",{ref_key:"chartCanvas",ref:U},null,512)]))}},ht={class:"space-y-6"},kt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},wt={class:"p-5"},_t={class:"flex items-center"},$t={class:"flex-shrink-0"},Ct={class:"ml-5 w-0 flex-1"},St={class:"text-sm font-medium text-gray-500 truncate"},Mt={class:"flex items-baseline"},Ut={class:"text-2xl font-semibold text-gray-900"},Bt={key:0,class:"self-center flex-shrink-0 h-4 w-4 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},Tt={key:1,class:"self-center flex-shrink-0 h-4 w-4 text-red-500",fill:"currentColor",viewBox:"0 0 20 20"},Vt={class:"bg-gray-50 px-5 py-3"},jt={class:"text-sm"},At={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Pt={class:"bg-white shadow rounded-lg"},zt={class:"px-6 py-4 border-b border-gray-200 flex justify-between items-center"},Dt={class:"flex space-x-2"},It={class:"p-6"},Et={class:"h-64"},Lt={class:"bg-white shadow rounded-lg"},Rt={class:"p-6"},Nt={class:"h-64"},Ft={class:"bg-white shadow rounded-lg"},Ot={class:"p-6"},Jt={class:"flow-root"},Wt={class:"-mb-8"},Ht={class:"relative pb-8"},Zt={key:0,class:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"},Gt={class:"relative flex space-x-3"},Xt={class:"h-4 w-4 text-white",fill:"currentColor",viewBox:"0 0 20 20"},qt={key:0,"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"},Qt={key:1,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},Kt={key:2,d:"M10 12a2 2 0 100-4 2 2 0 000 4z"},Yt={class:"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4"},es={class:"text-sm text-gray-500"},ts={class:"text-right text-sm whitespace-nowrap text-gray-500"},ss={class:"bg-white shadow rounded-lg"},os={class:"p-6"},ns={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ls={__name:"Dashboard",setup(P){const v=_("30d"),U=T(()=>({"7d":{labels:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],datasets:[{label:"Bookings",data:[12,19,15,25,22,30,28],borderColor:"rgb(99, 102, 241)",backgroundColor:"rgba(99, 102, 241, 0.1)",fill:!0,tension:.4},{label:"Revenue (₹000s)",data:[8,12,10,18,15,22,20],borderColor:"rgb(16, 185, 129)",backgroundColor:"rgba(16, 185, 129, 0.1)",fill:!0,tension:.4}]},"30d":{labels:["Jan 1","Jan 5","Jan 10","Jan 15","Jan 20","Jan 25","Jan 30","Feb 1","Feb 5","Feb 10","Feb 15","Feb 20","Feb 25","Feb 28"],datasets:[{label:"Bookings",data:[12,19,15,25,22,30,28,35,32,45,38,52,48,55],borderColor:"rgb(99, 102, 241)",backgroundColor:"rgba(99, 102, 241, 0.1)",fill:!0,tension:.4},{label:"Revenue (₹000s)",data:[8,12,10,18,15,22,20,25,23,32,28,38,35,42],borderColor:"rgb(16, 185, 129)",backgroundColor:"rgba(16, 185, 129, 0.1)",fill:!0,tension:.4}]},"90d":{labels:["Week 1","Week 2","Week 3","Week 4","Week 5","Week 6","Week 7","Week 8","Week 9","Week 10","Week 11","Week 12"],datasets:[{label:"Bookings",data:[45,52,48,61,58,67,63,72,69,78,75,82],borderColor:"rgb(99, 102, 241)",backgroundColor:"rgba(99, 102, 241, 0.1)",fill:!0,tension:.4},{label:"Revenue (₹000s)",data:[32,38,35,45,42,48,45,52,49,56,53,59],borderColor:"rgb(16, 185, 129)",backgroundColor:"rgba(16, 185, 129, 0.1)",fill:!0,tension:.4}]}})[v.value]),x=_({labels:["Tech Conference","Music Festival","Art Exhibition","Food Festival","Sports Event"],datasets:[{label:"Bookings",data:[156,134,98,87,76],backgroundColor:["rgba(99, 102, 241, 0.8)","rgba(16, 185, 129, 0.8)","rgba(245, 158, 11, 0.8)","rgba(239, 68, 68, 0.8)","rgba(139, 92, 246, 0.8)"],borderColor:["rgb(99, 102, 241)","rgb(16, 185, 129)","rgb(245, 158, 11)","rgb(239, 68, 68)","rgb(139, 92, 246)"],borderWidth:2,borderRadius:6}]}),o=_([{name:"Total Events",value:"24",change:"+12%",changeType:"increase",href:"/admin/events",icon:"svg",iconColor:"text-indigo-600",iconBackground:"bg-indigo-100"},{name:"Total Bookings",value:"1,247",change:"+8%",changeType:"increase",href:"/admin/bookings",icon:"svg",iconColor:"text-green-600",iconBackground:"bg-green-100"},{name:"Revenue",value:"₹2,45,680",change:"+15%",changeType:"increase",href:"/admin/reports",icon:"svg",iconColor:"text-yellow-600",iconBackground:"bg-yellow-100"},{name:"Active Users",value:"89",change:"-2%",changeType:"decrease",href:"/admin/users",icon:"svg",iconColor:"text-purple-600",iconBackground:"bg-purple-100"}]);_([{id:1,title:"Tech Conference 2025",date:"2025-02-15T09:00:00Z",bookings:156},{id:2,title:"Music Festival",date:"2025-03-20T18:00:00Z",bookings:134},{id:3,title:"Art Exhibition",date:"2025-04-10T10:00:00Z",bookings:98},{id:4,title:"Food Festival",date:"2025-05-05T12:00:00Z",bookings:87}]);const V=_([{id:1,type:"booking",description:"New booking for Tech Conference 2025 by John Doe",timestamp:new Date(Date.now()-5*60*1e3)},{id:2,type:"event",description:"Music Festival event was updated",timestamp:new Date(Date.now()-15*60*1e3)},{id:3,type:"booking",description:"Booking cancelled for Art Exhibition by Jane Smith",timestamp:new Date(Date.now()-30*60*1e3)},{id:4,type:"user",description:"New user registered: Mike Johnson",timestamp:new Date(Date.now()-60*60*1e3)}]),g=y=>{const f=Math.floor((new Date-y)/(1e3*60));if(f<1)return"Just now";if(f<60)return`${f}m ago`;const h=Math.floor(f/60);return h<24?`${h}h ago`:`${Math.floor(h/24)}d ago`};return K(()=>{const y=be();if(!y.isAuthenticated){const r={id:1,name:"Admin User",email:"<EMAIL>",role:"admin"},f="admin-demo-token-12345";y.user=r,y.token=f,localStorage.setItem("auth_token",f),localStorage.setItem("user",JSON.stringify(r)),console.log("Auto-logged in as admin for demo")}console.log("Admin Dashboard loaded")}),(y,r)=>{const f=ge("router-link");return a(),X(oe,null,{default:I(()=>[e("div",ht,[e("div",kt,[(a(!0),d(J,null,W(o.value,h=>(a(),d("div",{key:h.name,class:"bg-white overflow-hidden shadow rounded-lg"},[e("div",wt,[e("div",_t,[e("div",$t,[e("div",{class:R([h.iconBackground,"p-3 rounded-md"])},[(a(),X(me(h.icon),{class:R([h.iconColor,"h-6 w-6"])},null,8,["class"]))],2)]),e("div",Ct,[e("dl",null,[e("dt",St,u(h.name),1),e("dd",Mt,[e("div",Ut,u(h.value),1),e("div",{class:R([h.changeType==="increase"?"text-green-600":"text-red-600","ml-2 flex items-baseline text-sm font-semibold"])},[h.changeType==="increase"?(a(),d("svg",Bt,r[3]||(r[3]=[e("path",{"fill-rule":"evenodd",d:"M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]))):(a(),d("svg",Tt,r[4]||(r[4]=[e("path",{"fill-rule":"evenodd",d:"M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z","clip-rule":"evenodd"},null,-1)]))),O(" "+u(h.change),1)],2)])])])])]),e("div",Vt,[e("div",jt,[F(f,{to:h.href,class:"font-medium text-indigo-600 hover:text-indigo-500"},{default:I(()=>r[5]||(r[5]=[O(" View all ")])),_:2,__:[5]},1032,["to"])])])]))),128))]),e("div",At,[e("div",Pt,[e("div",zt,[r[6]||(r[6]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"Booking Trends"),e("p",{class:"text-sm text-gray-500"},"Bookings and revenue over time")],-1)),e("div",Dt,[e("button",{onClick:r[0]||(r[0]=h=>v.value="7d"),class:R([v.value==="7d"?"bg-indigo-600 text-white":"bg-gray-100 text-gray-700","px-3 py-1 rounded text-sm font-medium"])},"7D",2),e("button",{onClick:r[1]||(r[1]=h=>v.value="30d"),class:R([v.value==="30d"?"bg-indigo-600 text-white":"bg-gray-100 text-gray-700","px-3 py-1 rounded text-sm font-medium"])},"30D",2),e("button",{onClick:r[2]||(r[2]=h=>v.value="90d"),class:R([v.value==="90d"?"bg-indigo-600 text-white":"bg-gray-100 text-gray-700","px-3 py-1 rounded text-sm font-medium"])},"90D",2)])]),e("div",It,[e("div",Et,[F(ft,{data:U.value,"time-range":v.value},null,8,["data","time-range"])])])]),e("div",Lt,[r[7]||(r[7]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Event Performance"),e("p",{class:"text-sm text-gray-500"},"Top events by bookings this month")],-1)),e("div",Rt,[e("div",Nt,[F(yt,{data:x.value},null,8,["data"])])])])]),e("div",Ft,[r[8]||(r[8]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Recent Activity")],-1)),e("div",Ot,[e("div",Jt,[e("ul",Wt,[(a(!0),d(J,null,W(V.value,(h,j)=>(a(),d("li",{key:h.id},[e("div",Ht,[j!==V.value.length-1?(a(),d("span",Zt)):z("",!0),e("div",Gt,[e("div",null,[e("span",{class:R([h.type==="booking"?"bg-green-500":h.type==="event"?"bg-blue-500":"bg-gray-500","h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white"])},[(a(),d("svg",Xt,[h.type==="booking"?(a(),d("path",qt)):h.type==="event"?(a(),d("path",Qt)):(a(),d("path",Kt))]))],2)]),e("div",Yt,[e("div",null,[e("p",es,u(h.description),1)]),e("div",ts,u(g(h.timestamp)),1)])])])]))),128))])])])]),e("div",ss,[r[12]||(r[12]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Quick Actions")],-1)),e("div",os,[e("div",ns,[F(f,{to:"/admin/events/create",class:"flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-500 hover:shadow-md transition-all"},{default:I(()=>r[9]||(r[9]=[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-indigo-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})])])],-1),e("div",{class:"ml-4"},[e("p",{class:"text-sm font-medium text-gray-900"},"Create Event"),e("p",{class:"text-sm text-gray-500"},"Add a new event")],-1)])),_:1,__:[9]}),F(f,{to:"/admin/users/create",class:"flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-500 hover:shadow-md transition-all"},{default:I(()=>r[10]||(r[10]=[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})])])],-1),e("div",{class:"ml-4"},[e("p",{class:"text-sm font-medium text-gray-900"},"Add User"),e("p",{class:"text-sm text-gray-500"},"Create new user account")],-1)])),_:1,__:[10]}),F(f,{to:"/admin/reports",class:"flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-500 hover:shadow-md transition-all"},{default:I(()=>r[11]||(r[11]=[e("div",{class:"flex-shrink-0"},[e("div",{class:"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1),e("div",{class:"ml-4"},[e("p",{class:"text-sm font-medium text-gray-900"},"View Reports"),e("p",{class:"text-sm text-gray-500"},"Analytics & insights")],-1)])),_:1,__:[11]})])])])])]),_:1})}}},Da=Object.freeze(Object.defineProperty({__proto__:null,default:ls},Symbol.toStringTag,{value:"Module"})),as={class:"space-y-6"},is={class:"flex justify-between items-center"},rs={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ds={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},us={class:"flex items-center"},gs={class:"ml-4"},ms={class:"text-2xl font-bold text-gray-900"},cs={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},ps={class:"flex items-center"},vs={class:"ml-4"},xs={class:"text-2xl font-bold text-gray-900"},fs={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},bs={class:"flex items-center"},ys={class:"ml-4"},hs={class:"text-2xl font-bold text-gray-900"},ks={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},ws={class:"flex items-center"},_s={class:"ml-4"},$s={class:"text-2xl font-bold text-gray-900"},Cs={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},Ss={class:"flex flex-col lg:flex-row gap-4 items-center justify-between"},Ms={class:"flex-1 max-w-md"},Us={class:"relative"},Bs={class:"flex gap-4"},Ts={class:"bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden"},Vs={class:"px-6 py-4 border-b border-gray-200"},js={class:"text-lg font-medium text-gray-900"},As={key:0,class:"flex justify-center py-12"},Ps={key:1,class:"overflow-x-auto"},zs={class:"min-w-full divide-y divide-gray-200"},Ds={class:"bg-white divide-y divide-gray-200"},Is={class:"px-6 py-4 whitespace-nowrap"},Es={class:"flex items-center"},Ls={class:"w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-4"},Rs={class:"text-white font-semibold"},Ns={class:"text-sm font-medium text-gray-900"},Fs={class:"text-sm text-gray-500"},Os={class:"px-6 py-4 whitespace-nowrap"},Js={class:"text-sm text-gray-900"},Ws={class:"text-sm text-gray-500"},Hs={class:"px-6 py-4 whitespace-nowrap"},Zs={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Gs={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Xs={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},qs={class:"flex justify-end space-x-2"},Qs=["onClick"],Ks=["onClick"],Ys={key:2,class:"text-center py-12"},eo={class:"mt-6"},to={key:3,class:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"},so={class:"flex-1 flex justify-between sm:hidden"},oo=["disabled"],no=["disabled"],lo={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},ao={class:"text-sm text-gray-700"},io={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},ro=["disabled"],uo=["onClick"],go=["disabled"],mo={__name:"Events",setup(P){const v=_(!0),U=_(""),x=_(""),o=_(""),V=_("created_at"),g=_(1),y=_(10),r=_([{id:1,title:"Tech Conference 2025",slug:"tech-conference-2025",venue_name:"Convention Center",start_date:"2025-02-15T09:00:00Z",status:"published",category:"conference",total_bookings:156,total_revenue:39e4},{id:2,title:"Music Festival",slug:"music-festival",venue_name:"City Park",start_date:"2025-03-20T18:00:00Z",status:"published",category:"festival",total_bookings:134,total_revenue:201e3},{id:3,title:"Art Exhibition",slug:"art-exhibition",venue_name:"Art Gallery",start_date:"2025-04-10T10:00:00Z",status:"draft",category:"exhibition",total_bookings:0,total_revenue:0}]),f=T(()=>r.value.length),h=T(()=>r.value.filter(S=>S.status==="published").length),j=T(()=>r.value.filter(S=>S.status==="draft").length),t=T(()=>r.value.reduce((S,i)=>S+(i.total_bookings||0),0)),s=T(()=>{let S=r.value;return U.value&&(S=S.filter(i=>i.title.toLowerCase().includes(U.value.toLowerCase())||i.venue_name.toLowerCase().includes(U.value.toLowerCase()))),x.value&&(S=S.filter(i=>i.status===x.value)),o.value&&(S=S.filter(i=>i.category===o.value)),S.sort((i,N)=>{switch(V.value){case"start_date":return new Date(i.start_date)-new Date(N.start_date);case"title":return i.title.localeCompare(N.title);case"bookings":return(N.total_bookings||0)-(i.total_bookings||0);default:return N.id-i.id}}),S}),E=T(()=>Math.ceil(s.value.length/y.value)),C=T(()=>{const S=(g.value-1)*y.value,i=S+y.value;return s.value.slice(S,i)}),k=T(()=>{const S=[],i=Math.max(1,g.value-2),N=Math.min(E.value,g.value+2);for(let B=i;B<=N;B++)S.push(B);return S}),l=S=>new Date(S).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),D=S=>new Date(S).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}),A=S=>({published:"bg-green-100 text-green-800",draft:"bg-yellow-100 text-yellow-800",cancelled:"bg-red-100 text-red-800"})[S]||"bg-gray-100 text-gray-800",G=()=>{g.value=1},q=()=>{g.value=1},te=S=>{S.status=S.status==="published"?"draft":"published"},Z=S=>{if(confirm(`Are you sure you want to delete "${S.title}"?`)){const i=r.value.findIndex(N=>N.id===S.id);i!==-1&&r.value.splice(i,1)}},Y=()=>{g.value>1&&g.value--},ee=()=>{g.value<E.value&&g.value++},se=S=>{g.value=S};return K(()=>{setTimeout(()=>{v.value=!1},1e3)}),(S,i)=>{const N=ge("router-link");return a(),X(oe,null,{default:I(()=>[e("div",as,[e("div",is,[i[5]||(i[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Events Management"),e("p",{class:"text-gray-600"},"Manage all events, create new ones, and track performance")],-1)),F(N,{to:"/admin/events/create",class:"bg-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-indigo-700 transition-colors"},{default:I(()=>i[4]||(i[4]=[O(" Create Event ")])),_:1,__:[4]})]),e("div",rs,[e("div",ds,[e("div",us,[i[7]||(i[7]=e("div",{class:"p-2 bg-blue-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",gs,[i[6]||(i[6]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Events",-1)),e("p",ms,u(f.value),1)])])]),e("div",cs,[e("div",ps,[i[9]||(i[9]=e("div",{class:"p-2 bg-green-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",vs,[i[8]||(i[8]=e("p",{class:"text-sm font-medium text-gray-600"},"Published",-1)),e("p",xs,u(h.value),1)])])]),e("div",fs,[e("div",bs,[i[11]||(i[11]=e("div",{class:"p-2 bg-yellow-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",ys,[i[10]||(i[10]=e("p",{class:"text-sm font-medium text-gray-600"},"Draft",-1)),e("p",hs,u(j.value),1)])])]),e("div",ks,[e("div",ws,[i[13]||(i[13]=e("div",{class:"p-2 bg-purple-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),e("div",_s,[i[12]||(i[12]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Bookings",-1)),e("p",$s,u(t.value),1)])])])]),e("div",Cs,[e("div",Ss,[e("div",Ms,[e("div",Us,[c(e("input",{"onUpdate:modelValue":i[0]||(i[0]=B=>U.value=B),onInput:G,type:"text",placeholder:"Search events...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,544),[[M,U.value]]),i[14]||(i[14]=e("svg",{class:"absolute left-3 top-2.5 h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])]),e("div",Bs,[c(e("select",{"onUpdate:modelValue":i[1]||(i[1]=B=>x.value=B),onChange:q,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},i[15]||(i[15]=[e("option",{value:""},"All Status",-1),e("option",{value:"published"},"Published",-1),e("option",{value:"draft"},"Draft",-1),e("option",{value:"cancelled"},"Cancelled",-1)]),544),[[L,x.value]]),c(e("select",{"onUpdate:modelValue":i[2]||(i[2]=B=>o.value=B),onChange:q,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},i[16]||(i[16]=[e("option",{value:""},"All Categories",-1),e("option",{value:"conference"},"Conference",-1),e("option",{value:"festival"},"Festival",-1),e("option",{value:"exhibition"},"Exhibition",-1),e("option",{value:"workshop"},"Workshop",-1)]),544),[[L,o.value]]),c(e("select",{"onUpdate:modelValue":i[3]||(i[3]=B=>V.value=B),onChange:q,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},i[17]||(i[17]=[e("option",{value:"created_at"},"Latest",-1),e("option",{value:"start_date"},"Event Date",-1),e("option",{value:"title"},"Name A-Z",-1),e("option",{value:"bookings"},"Most Bookings",-1)]),544),[[L,V.value]])])])]),e("div",Ts,[e("div",Vs,[e("h3",js,"Events ("+u(s.value.length)+")",1)]),v.value?(a(),d("div",As,i[18]||(i[18]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"},null,-1)]))):s.value.length>0?(a(),d("div",Ps,[e("table",zs,[i[21]||(i[21]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Event"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Date & Time"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Bookings"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Revenue"),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",Ds,[(a(!0),d(J,null,W(C.value,B=>(a(),d("tr",{key:B.id,class:"hover:bg-gray-50"},[e("td",Is,[e("div",Es,[e("div",Ls,[e("span",Rs,u(B.title.charAt(0)),1)]),e("div",null,[e("div",Ns,u(B.title),1),e("div",Fs,u(B.venue_name),1)])])]),e("td",Os,[e("div",Js,u(l(B.start_date)),1),e("div",Ws,u(D(B.start_date)),1)]),e("td",Hs,[e("span",{class:R([A(B.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},u(B.status),3)]),e("td",Zs,u(B.total_bookings||0),1),e("td",Gs," ₹"+u((B.total_revenue||0).toLocaleString()),1),e("td",Xs,[e("div",qs,[F(N,{to:`/events/${B.slug}`,class:"text-indigo-600 hover:text-indigo-900"},{default:I(()=>i[19]||(i[19]=[O("View")])),_:2,__:[19]},1032,["to"]),F(N,{to:`/admin/events/${B.id}/edit`,class:"text-indigo-600 hover:text-indigo-900"},{default:I(()=>i[20]||(i[20]=[O("Edit")])),_:2,__:[20]},1032,["to"]),e("button",{onClick:Q=>te(B),class:"text-green-600 hover:text-green-900"},u(B.status==="published"?"Unpublish":"Publish"),9,Qs),e("button",{onClick:Q=>Z(B),class:"text-red-600 hover:text-red-900"},"Delete",8,Ks)])])]))),128))])])])):(a(),d("div",Ys,[i[23]||(i[23]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),i[24]||(i[24]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No events found",-1)),i[25]||(i[25]=e("p",{class:"mt-1 text-sm text-gray-500"},"Get started by creating a new event.",-1)),e("div",eo,[F(N,{to:"/admin/events/create",class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"},{default:I(()=>i[22]||(i[22]=[O(" Create Event ")])),_:1,__:[22]})])])),s.value.length>y.value?(a(),d("div",to,[e("div",so,[e("button",{onClick:Y,disabled:g.value===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Previous ",8,oo),e("button",{onClick:ee,disabled:g.value===E.value,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Next ",8,no)]),e("div",lo,[e("div",null,[e("p",ao," Showing "+u((g.value-1)*y.value+1)+" to "+u(Math.min(g.value*y.value,s.value.length))+" of "+u(s.value.length)+" results ",1)]),e("div",null,[e("nav",io,[e("button",{onClick:Y,disabled:g.value===1,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"}," Previous ",8,ro),(a(!0),d(J,null,W(k.value,B=>(a(),d("button",{key:B,onClick:Q=>se(B),class:R([B===g.value?"bg-indigo-50 border-indigo-500 text-indigo-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50","relative inline-flex items-center px-4 py-2 border text-sm font-medium"])},u(B),11,uo))),128)),e("button",{onClick:ee,disabled:g.value===E.value,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"}," Next ",8,go)])])])])):z("",!0)])])]),_:1})}}},Ia=Object.freeze(Object.defineProperty({__proto__:null,default:mo},Symbol.toStringTag,{value:"Module"})),co={class:"space-y-6"},po={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},vo={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},xo={class:"flex items-center"},fo={class:"ml-4"},bo={class:"text-2xl font-bold text-gray-900"},yo={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},ho={class:"flex items-center"},ko={class:"ml-4"},wo={class:"text-2xl font-bold text-gray-900"},_o={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},$o={class:"flex items-center"},Co={class:"ml-4"},So={class:"text-2xl font-bold text-gray-900"},Mo={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},Uo={class:"flex items-center"},Bo={class:"ml-4"},To={class:"text-2xl font-bold text-gray-900"},Vo={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},jo={class:"flex flex-col lg:flex-row gap-4 items-center justify-between"},Ao={class:"flex-1 max-w-md"},Po={class:"relative"},zo={class:"flex gap-4"},Do=["value"],Io={class:"bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden"},Eo={class:"px-6 py-4 border-b border-gray-200"},Lo={class:"text-lg font-medium text-gray-900"},Ro={key:0,class:"flex justify-center py-12"},No={key:1,class:"overflow-x-auto"},Fo={class:"min-w-full divide-y divide-gray-200"},Oo={class:"bg-gray-50"},Jo={class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},Wo={class:"bg-white divide-y divide-gray-200"},Ho={class:"px-6 py-4 whitespace-nowrap"},Zo=["value"],Go={class:"px-6 py-4 whitespace-nowrap"},Xo={class:"flex items-center"},qo={class:"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center mr-3"},Qo={class:"text-gray-600 font-medium text-sm"},Ko={class:"text-sm font-medium text-gray-900"},Yo={class:"text-sm text-gray-500"},en={class:"px-6 py-4 whitespace-nowrap"},tn={class:"text-sm font-medium text-gray-900"},sn={class:"text-sm text-gray-500"},on={class:"px-6 py-4 whitespace-nowrap"},nn={class:"text-sm text-gray-900"},ln={class:"text-gray-500"},an={class:"px-6 py-4 whitespace-nowrap"},rn={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},dn={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},un={class:"flex justify-end space-x-2"},gn=["onClick"],mn=["onClick"],cn=["onClick"],pn=["onClick"],vn={key:2,class:"text-center py-12"},xn={key:3,class:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"},fn={class:"flex-1 flex justify-between sm:hidden"},bn=["disabled"],yn=["disabled"],hn={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},kn={class:"text-sm text-gray-700"},wn={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},_n=["disabled"],$n=["onClick"],Cn=["disabled"],Sn={key:0,class:"fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white shadow-lg rounded-lg border border-gray-200 p-4"},Mn={class:"flex items-center space-x-4"},Un={class:"text-sm text-gray-600"},Bn={__name:"Bookings",setup(P){const v=_(!0),U=_(""),x=_(""),o=_(""),V=_(""),g=_(1),y=_(10),r=_(!1),f=_([]),h=_([{id:1,title:"Tech Conference 2025"},{id:2,title:"Music Festival"},{id:3,title:"Art Exhibition"}]),j=_([{id:1,booking_reference:"TC2025-001",attendee_name:"John Doe",attendee_email:"<EMAIL>",event:{id:1,title:"Tech Conference 2025",start_date:"2025-02-15T09:00:00Z"},ticket_type:{name:"Regular"},quantity:2,total_amount:5e3,status:"confirmed",created_at:"2025-01-12T10:30:00Z"},{id:2,booking_reference:"MF2025-002",attendee_name:"Jane Smith",attendee_email:"<EMAIL>",event:{id:2,title:"Music Festival",start_date:"2025-03-20T18:00:00Z"},ticket_type:{name:"VIP"},quantity:1,total_amount:3500,status:"pending",created_at:"2025-01-11T15:45:00Z"}]),t=T(()=>j.value.length),s=T(()=>j.value.filter(b=>b.status==="confirmed").length),E=T(()=>j.value.filter(b=>b.status==="pending").length),C=T(()=>j.value.reduce((b,m)=>b+m.total_amount,0)),k=T(()=>{let b=j.value;if(U.value){const m=U.value.toLowerCase();b=b.filter($=>$.attendee_name.toLowerCase().includes(m)||$.attendee_email.toLowerCase().includes(m)||$.booking_reference.toLowerCase().includes(m))}return x.value&&(b=b.filter(m=>m.status===x.value)),o.value&&(b=b.filter(m=>m.event.id===parseInt(o.value))),b}),l=T(()=>Math.ceil(k.value.length/y.value)),D=T(()=>{const b=(g.value-1)*y.value,m=b+y.value;return k.value.slice(b,m)}),A=T(()=>{const b=[],m=Math.max(1,g.value-2),$=Math.min(l.value,g.value+2);for(let H=m;H<=$;H++)b.push(H);return b}),G=b=>new Date(b).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),q=b=>({confirmed:"bg-green-100 text-green-800",pending:"bg-yellow-100 text-yellow-800",cancelled:"bg-red-100 text-red-800",attended:"bg-blue-100 text-blue-800"})[b]||"bg-gray-100 text-gray-800",te=()=>{g.value=1},Z=()=>{g.value=1},Y=()=>{r.value?f.value=D.value.map(b=>b.id):f.value=[]},ee=()=>{f.value=[],r.value=!1},se=b=>{alert(`Viewing booking: ${b.booking_reference}`)},S=b=>{b.status="attended",alert(`${b.attendee_name} marked as attended`)},i=b=>{alert(`Ticket resent to ${b.attendee_email}`)},N=b=>{confirm(`Cancel booking for ${b.attendee_name}?`)&&(b.status="cancelled")},B=()=>{alert("Exporting all bookings to CSV...")},Q=()=>{alert("Sending bulk reminders...")},ne=()=>{alert(`Sending reminders to ${f.value.length} selected bookings`)},ae=()=>{alert(`Exporting ${f.value.length} selected bookings`)},w=()=>{g.value>1&&g.value--},n=()=>{g.value<l.value&&g.value++},p=b=>{g.value=b};return K(()=>{setTimeout(()=>{v.value=!1},1e3)}),(b,m)=>(a(),X(oe,null,{default:I(()=>[e("div",co,[e("div",{class:"flex justify-between items-center"},[m[6]||(m[6]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Bookings Management"),e("p",{class:"text-gray-600"},"Manage all bookings, process refunds, and track attendance")],-1)),e("div",{class:"flex space-x-3"},[e("button",{onClick:B,class:"bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors"}," Export CSV "),e("button",{onClick:Q,class:"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"}," Send Reminders ")])]),e("div",po,[e("div",vo,[e("div",xo,[m[8]||(m[8]=e("div",{class:"p-2 bg-blue-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),e("div",fo,[m[7]||(m[7]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Bookings",-1)),e("p",bo,u(t.value),1)])])]),e("div",yo,[e("div",ho,[m[10]||(m[10]=e("div",{class:"p-2 bg-green-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",ko,[m[9]||(m[9]=e("p",{class:"text-sm font-medium text-gray-600"},"Confirmed",-1)),e("p",wo,u(s.value),1)])])]),e("div",_o,[e("div",$o,[m[12]||(m[12]=e("div",{class:"p-2 bg-yellow-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",Co,[m[11]||(m[11]=e("p",{class:"text-sm font-medium text-gray-600"},"Pending",-1)),e("p",So,u(E.value),1)])])]),e("div",Mo,[e("div",Uo,[m[14]||(m[14]=e("div",{class:"p-2 bg-purple-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",Bo,[m[13]||(m[13]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Revenue",-1)),e("p",To,"₹"+u(C.value.toLocaleString()),1)])])])]),e("div",Vo,[e("div",jo,[e("div",Ao,[e("div",Po,[c(e("input",{"onUpdate:modelValue":m[0]||(m[0]=$=>U.value=$),onInput:te,type:"text",placeholder:"Search by name, email, or booking reference...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,544),[[M,U.value]]),m[15]||(m[15]=e("svg",{class:"absolute left-3 top-2.5 h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])]),e("div",zo,[c(e("select",{"onUpdate:modelValue":m[1]||(m[1]=$=>x.value=$),onChange:Z,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},m[16]||(m[16]=[e("option",{value:""},"All Status",-1),e("option",{value:"confirmed"},"Confirmed",-1),e("option",{value:"pending"},"Pending",-1),e("option",{value:"cancelled"},"Cancelled",-1),e("option",{value:"attended"},"Attended",-1)]),544),[[L,x.value]]),c(e("select",{"onUpdate:modelValue":m[2]||(m[2]=$=>o.value=$),onChange:Z,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},[m[17]||(m[17]=e("option",{value:""},"All Events",-1)),(a(!0),d(J,null,W(h.value,$=>(a(),d("option",{key:$.id,value:$.id},u($.title),9,Do))),128))],544),[[L,o.value]]),c(e("select",{"onUpdate:modelValue":m[3]||(m[3]=$=>V.value=$),onChange:Z,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},m[18]||(m[18]=[e("option",{value:""},"All Dates",-1),e("option",{value:"today"},"Today",-1),e("option",{value:"week"},"This Week",-1),e("option",{value:"month"},"This Month",-1)]),544),[[L,V.value]])])])]),e("div",Io,[e("div",Eo,[e("h3",Lo,"Bookings ("+u(k.value.length)+")",1)]),v.value?(a(),d("div",Ro,m[19]||(m[19]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"},null,-1)]))):k.value.length>0?(a(),d("div",No,[e("table",Fo,[e("thead",Oo,[e("tr",null,[e("th",Jo,[c(e("input",{type:"checkbox","onUpdate:modelValue":m[4]||(m[4]=$=>r.value=$),onChange:Y,class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,544),[[re,r.value]])]),m[20]||(m[20]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Customer",-1)),m[21]||(m[21]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Event",-1)),m[22]||(m[22]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Booking Details",-1)),m[23]||(m[23]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status",-1)),m[24]||(m[24]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Amount",-1)),m[25]||(m[25]=e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions",-1))])]),e("tbody",Wo,[(a(!0),d(J,null,W(D.value,$=>(a(),d("tr",{key:$.id,class:"hover:bg-gray-50"},[e("td",Ho,[c(e("input",{type:"checkbox","onUpdate:modelValue":m[5]||(m[5]=H=>f.value=H),value:$.id,class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,8,Zo),[[re,f.value]])]),e("td",Go,[e("div",Xo,[e("div",qo,[e("span",Qo,u($.attendee_name.charAt(0)),1)]),e("div",null,[e("div",Ko,u($.attendee_name),1),e("div",Yo,u($.attendee_email),1)])])]),e("td",en,[e("div",tn,u($.event.title),1),e("div",sn,u(G($.event.start_date)),1)]),e("td",on,[e("div",nn,[e("div",null,"Ref: "+u($.booking_reference),1),e("div",null,u($.ticket_type.name)+" × "+u($.quantity),1),e("div",ln,u(G($.created_at)),1)])]),e("td",an,[e("span",{class:R([q($.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},u($.status),3)]),e("td",rn," ₹"+u($.total_amount.toLocaleString()),1),e("td",dn,[e("div",un,[e("button",{onClick:H=>se($),class:"text-indigo-600 hover:text-indigo-900"},"View",8,gn),$.status==="confirmed"?(a(),d("button",{key:0,onClick:H=>S($),class:"text-green-600 hover:text-green-900"},"Check-in",8,mn)):z("",!0),e("button",{onClick:H=>i($),class:"text-blue-600 hover:text-blue-900"},"Resend",8,cn),$.status!=="cancelled"?(a(),d("button",{key:1,onClick:H=>N($),class:"text-red-600 hover:text-red-900"},"Cancel",8,pn)):z("",!0)])])]))),128))])])])):(a(),d("div",vn,m[26]||(m[26]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No bookings found",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"No bookings match your current filters.",-1)]))),k.value.length>y.value?(a(),d("div",xn,[e("div",fn,[e("button",{onClick:w,disabled:g.value===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Previous ",8,bn),e("button",{onClick:n,disabled:g.value===l.value,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Next ",8,yn)]),e("div",hn,[e("div",null,[e("p",kn," Showing "+u((g.value-1)*y.value+1)+" to "+u(Math.min(g.value*y.value,k.value.length))+" of "+u(k.value.length)+" results ",1)]),e("div",null,[e("nav",wn,[e("button",{onClick:w,disabled:g.value===1,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"}," Previous ",8,_n),(a(!0),d(J,null,W(A.value,$=>(a(),d("button",{key:$,onClick:H=>p($),class:R([$===g.value?"bg-indigo-50 border-indigo-500 text-indigo-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50","relative inline-flex items-center px-4 py-2 border text-sm font-medium"])},u($),11,$n))),128)),e("button",{onClick:n,disabled:g.value===l.value,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"}," Next ",8,Cn)])])])])):z("",!0)]),f.value.length>0?(a(),d("div",Sn,[e("div",Mn,[e("span",Un,u(f.value.length)+" selected",1),e("button",{onClick:ne,class:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"}," Send Reminders "),e("button",{onClick:ae,class:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," Export Selected "),e("button",{onClick:ee,class:"text-gray-600 hover:text-gray-800 text-sm"}," Clear ")])])):z("",!0)])]),_:1}))}},Ea=Object.freeze(Object.defineProperty({__proto__:null,default:Bn},Symbol.toStringTag,{value:"Module"})),Tn={class:"space-y-6"},Vn={class:"flex justify-between items-center"},jn={class:"flex space-x-3"},An={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Pn={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},zn={class:"flex items-center"},Dn={class:"ml-4"},In={class:"text-2xl font-bold text-gray-900"},En={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},Ln={class:"flex items-center"},Rn={class:"ml-4"},Nn={class:"text-2xl font-bold text-gray-900"},Fn={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},On={class:"flex items-center"},Jn={class:"ml-4"},Wn={class:"text-2xl font-bold text-gray-900"},Hn={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},Zn={class:"flex items-center"},Gn={class:"ml-4"},Xn={class:"text-2xl font-bold text-gray-900"},qn={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},Qn={class:"flex flex-col lg:flex-row gap-4 items-center justify-between"},Kn={class:"flex-1 max-w-md"},Yn={class:"relative"},el={class:"flex gap-4"},tl={class:"bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden"},sl={class:"px-6 py-4 border-b border-gray-200"},ol={class:"text-lg font-medium text-gray-900"},nl={key:0,class:"flex justify-center py-12"},ll={key:1,class:"overflow-x-auto"},al={class:"min-w-full divide-y divide-gray-200"},il={class:"bg-white divide-y divide-gray-200"},rl={class:"px-6 py-4 whitespace-nowrap"},dl={class:"flex items-center"},ul={class:"w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center mr-4"},gl={class:"text-gray-600 font-medium text-sm"},ml={class:"text-sm font-medium text-gray-900"},cl={class:"text-sm text-gray-500"},pl={class:"px-6 py-4 whitespace-nowrap"},vl={class:"px-6 py-4 whitespace-nowrap"},xl={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},fl={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},bl={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},yl={class:"flex justify-end space-x-2"},hl=["onClick"],kl=["onClick"],wl=["onClick"],_l=["onClick"],$l=["onClick"],Cl={key:2,class:"text-center py-12"},Sl={key:3,class:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"},Ml={class:"flex-1 flex justify-between sm:hidden"},Ul=["disabled"],Bl=["disabled"],Tl={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Vl={class:"text-sm text-gray-700"},jl={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Al=["disabled"],Pl=["onClick"],zl=["disabled"],Dl={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Il={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},El={class:"mt-3"},Ll={class:"space-y-4"},Rl={class:"flex justify-end space-x-3 mt-6"},Nl={__name:"Users",setup(P){const v=_(!0),U=_(""),x=_(""),o=_(""),V=_("created_at"),g=_(1),y=_(10),r=_(!1),f=de({name:"",email:"",role:"",password:""}),h=_([{id:1,name:"John Doe",email:"<EMAIL>",role:"admin",status:"active",total_bookings:5,last_login:"2025-01-12T10:30:00Z",created_at:"2024-12-01T10:30:00Z"},{id:2,name:"Jane Smith",email:"<EMAIL>",role:"manager",status:"active",total_bookings:12,last_login:"2025-01-11T15:45:00Z",created_at:"2024-12-05T15:45:00Z"},{id:3,name:"Mike Johnson",email:"<EMAIL>",role:"attendee",status:"active",total_bookings:3,last_login:"2025-01-10T09:20:00Z",created_at:"2025-01-01T09:20:00Z"},{id:4,name:"Sarah Wilson",email:"<EMAIL>",role:"organizer",status:"suspended",total_bookings:0,last_login:null,created_at:"2025-01-05T14:15:00Z"}]),j=T(()=>h.value.length),t=T(()=>h.value.filter(w=>w.status==="active").length),s=T(()=>{const w=new Date().getMonth();return h.value.filter(n=>new Date(n.created_at).getMonth()===w).length}),E=T(()=>h.value.filter(w=>w.role==="admin").length),C=T(()=>{let w=h.value;if(U.value){const n=U.value.toLowerCase();w=w.filter(p=>p.name.toLowerCase().includes(n)||p.email.toLowerCase().includes(n))}return x.value&&(w=w.filter(n=>n.role===x.value)),o.value&&(w=w.filter(n=>n.status===o.value)),w.sort((n,p)=>{switch(V.value){case"name":return n.name.localeCompare(p.name);case"email":return n.email.localeCompare(p.email);case"last_login":return!n.last_login&&!p.last_login?0:n.last_login?p.last_login?new Date(p.last_login)-new Date(n.last_login):-1:1;default:return new Date(p.created_at)-new Date(n.created_at)}}),w}),k=T(()=>Math.ceil(C.value.length/y.value)),l=T(()=>{const w=(g.value-1)*y.value,n=w+y.value;return C.value.slice(w,n)}),D=T(()=>{const w=[],n=Math.max(1,g.value-2),p=Math.min(k.value,g.value+2);for(let b=n;b<=p;b++)w.push(b);return w}),A=w=>new Date(w).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),G=w=>({admin:"bg-red-100 text-red-800",manager:"bg-blue-100 text-blue-800",organizer:"bg-purple-100 text-purple-800",attendee:"bg-gray-100 text-gray-800"})[w]||"bg-gray-100 text-gray-800",q=w=>({active:"bg-green-100 text-green-800",inactive:"bg-gray-100 text-gray-800",suspended:"bg-red-100 text-red-800"})[w]||"bg-gray-100 text-gray-800",te=()=>{g.value=1},Z=()=>{g.value=1},Y=w=>{alert(`Viewing user: ${w.name}`)},ee=w=>{alert(`Editing user: ${w.name}`)},se=w=>{confirm(`Suspend user ${w.name}?`)&&(w.status="suspended")},S=w=>{w.status="active"},i=w=>{if(confirm(`Delete user ${w.name}? This action cannot be undone.`)){const n=h.value.findIndex(p=>p.id===w.id);n!==-1&&h.value.splice(n,1)}},N=()=>{const w={id:Date.now(),name:f.name,email:f.email,role:f.role,status:"active",total_bookings:0,last_login:null,created_at:new Date().toISOString()};h.value.unshift(w),r.value=!1,Object.assign(f,{name:"",email:"",role:"",password:""})},B=()=>{alert("Exporting users to CSV...")},Q=()=>{g.value>1&&g.value--},ne=()=>{g.value<k.value&&g.value++},ae=w=>{g.value=w};return K(()=>{setTimeout(()=>{v.value=!1},1e3)}),(w,n)=>(a(),X(oe,null,{default:I(()=>[e("div",Tn,[e("div",Vn,[n[10]||(n[10]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"User Management"),e("p",{class:"text-gray-600"},"Manage users, roles, and permissions")],-1)),e("div",jn,[e("button",{onClick:B,class:"bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors"}," Export Users "),e("button",{onClick:n[0]||(n[0]=p=>r.value=!0),class:"bg-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-indigo-700 transition-colors"}," Add User ")])]),e("div",An,[e("div",Pn,[e("div",zn,[n[12]||(n[12]=e("div",{class:"p-2 bg-blue-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})])],-1)),e("div",Dn,[n[11]||(n[11]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Users",-1)),e("p",In,u(j.value),1)])])]),e("div",En,[e("div",Ln,[n[14]||(n[14]=e("div",{class:"p-2 bg-green-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",Rn,[n[13]||(n[13]=e("p",{class:"text-sm font-medium text-gray-600"},"Active Users",-1)),e("p",Nn,u(t.value),1)])])]),e("div",Fn,[e("div",On,[n[16]||(n[16]=e("div",{class:"p-2 bg-yellow-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",Jn,[n[15]||(n[15]=e("p",{class:"text-sm font-medium text-gray-600"},"New This Month",-1)),e("p",Wn,u(s.value),1)])])]),e("div",Hn,[e("div",Zn,[n[18]||(n[18]=e("div",{class:"p-2 bg-purple-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e("div",Gn,[n[17]||(n[17]=e("p",{class:"text-sm font-medium text-gray-600"},"Admin Users",-1)),e("p",Xn,u(E.value),1)])])])]),e("div",qn,[e("div",Qn,[e("div",Kn,[e("div",Yn,[c(e("input",{"onUpdate:modelValue":n[1]||(n[1]=p=>U.value=p),onInput:te,type:"text",placeholder:"Search users by name or email...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,544),[[M,U.value]]),n[19]||(n[19]=e("svg",{class:"absolute left-3 top-2.5 h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])]),e("div",el,[c(e("select",{"onUpdate:modelValue":n[2]||(n[2]=p=>x.value=p),onChange:Z,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},n[20]||(n[20]=[e("option",{value:""},"All Roles",-1),e("option",{value:"admin"},"Admin",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"organizer"},"Organizer",-1),e("option",{value:"attendee"},"Attendee",-1)]),544),[[L,x.value]]),c(e("select",{"onUpdate:modelValue":n[3]||(n[3]=p=>o.value=p),onChange:Z,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},n[21]||(n[21]=[e("option",{value:""},"All Status",-1),e("option",{value:"active"},"Active",-1),e("option",{value:"inactive"},"Inactive",-1),e("option",{value:"suspended"},"Suspended",-1)]),544),[[L,o.value]]),c(e("select",{"onUpdate:modelValue":n[4]||(n[4]=p=>V.value=p),onChange:Z,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},n[22]||(n[22]=[e("option",{value:"created_at"},"Latest",-1),e("option",{value:"name"},"Name A-Z",-1),e("option",{value:"email"},"Email A-Z",-1),e("option",{value:"last_login"},"Last Login",-1)]),544),[[L,V.value]])])])]),e("div",tl,[e("div",sl,[e("h3",ol,"Users ("+u(C.value.length)+")",1)]),v.value?(a(),d("div",nl,n[23]||(n[23]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"},null,-1)]))):C.value.length>0?(a(),d("div",ll,[e("table",al,[n[24]||(n[24]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"User"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Role"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Bookings"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Last Login"),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",il,[(a(!0),d(J,null,W(l.value,p=>(a(),d("tr",{key:p.id,class:"hover:bg-gray-50"},[e("td",rl,[e("div",dl,[e("div",ul,[e("span",gl,u(p.name.charAt(0)),1)]),e("div",null,[e("div",ml,u(p.name),1),e("div",cl,u(p.email),1)])])]),e("td",pl,[e("span",{class:R([G(p.role),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},u(p.role),3)]),e("td",vl,[e("span",{class:R([q(p.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},u(p.status),3)]),e("td",xl,u(p.total_bookings||0),1),e("td",fl,u(p.last_login?A(p.last_login):"Never"),1),e("td",bl,[e("div",yl,[e("button",{onClick:b=>Y(p),class:"text-indigo-600 hover:text-indigo-900"},"View",8,hl),e("button",{onClick:b=>ee(p),class:"text-indigo-600 hover:text-indigo-900"},"Edit",8,kl),p.status==="active"?(a(),d("button",{key:0,onClick:b=>se(p),class:"text-yellow-600 hover:text-yellow-900"},"Suspend",8,wl)):p.status==="suspended"?(a(),d("button",{key:1,onClick:b=>S(p),class:"text-green-600 hover:text-green-900"},"Activate",8,_l)):z("",!0),e("button",{onClick:b=>i(p),class:"text-red-600 hover:text-red-900"},"Delete",8,$l)])])]))),128))])])])):(a(),d("div",Cl,n[25]||(n[25]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No users found",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"No users match your current filters.",-1)]))),C.value.length>y.value?(a(),d("div",Sl,[e("div",Ml,[e("button",{onClick:Q,disabled:g.value===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Previous ",8,Ul),e("button",{onClick:ne,disabled:g.value===k.value,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Next ",8,Bl)]),e("div",Tl,[e("div",null,[e("p",Vl," Showing "+u((g.value-1)*y.value+1)+" to "+u(Math.min(g.value*y.value,C.value.length))+" of "+u(C.value.length)+" results ",1)]),e("div",null,[e("nav",jl,[e("button",{onClick:Q,disabled:g.value===1,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"}," Previous ",8,Al),(a(!0),d(J,null,W(D.value,p=>(a(),d("button",{key:p,onClick:b=>ae(p),class:R([p===g.value?"bg-indigo-50 border-indigo-500 text-indigo-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50","relative inline-flex items-center px-4 py-2 border text-sm font-medium"])},u(p),11,Pl))),128)),e("button",{onClick:ne,disabled:g.value===k.value,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"}," Next ",8,zl)])])])])):z("",!0)]),r.value?(a(),d("div",Dl,[e("div",Il,[e("div",El,[n[32]||(n[32]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Add New User",-1)),e("form",{onSubmit:$e(N,["prevent"])},[e("div",Ll,[e("div",null,[n[26]||(n[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Name",-1)),c(e("input",{"onUpdate:modelValue":n[5]||(n[5]=p=>f.name=p),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,f.name]])]),e("div",null,[n[27]||(n[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Email",-1)),c(e("input",{"onUpdate:modelValue":n[6]||(n[6]=p=>f.email=p),type:"email",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,f.email]])]),e("div",null,[n[29]||(n[29]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Role",-1)),c(e("select",{"onUpdate:modelValue":n[7]||(n[7]=p=>f.role=p),required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},n[28]||(n[28]=[e("option",{value:""},"Select Role",-1),e("option",{value:"admin"},"Admin",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"organizer"},"Organizer",-1),e("option",{value:"attendee"},"Attendee",-1)]),512),[[L,f.role]])]),e("div",null,[n[30]||(n[30]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Password",-1)),c(e("input",{"onUpdate:modelValue":n[8]||(n[8]=p=>f.password=p),type:"password",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,f.password]])])]),e("div",Rl,[e("button",{type:"button",onClick:n[9]||(n[9]=p=>r.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"}," Cancel "),n[31]||(n[31]=e("button",{type:"submit",class:"px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"}," Create User ",-1))])],32)])])])):z("",!0)])]),_:1}))}},La=Object.freeze(Object.defineProperty({__proto__:null,default:Nl},Symbol.toStringTag,{value:"Module"})),Fl={class:"space-y-6"},Ol={class:"flex justify-between items-center"},Jl={class:"flex space-x-3"},Wl=["disabled"],Hl={class:"bg-white shadow-sm rounded-lg border border-gray-200"},Zl={class:"border-b border-gray-200"},Gl={class:"-mb-px flex space-x-8 px-6"},Xl=["onClick"],ql={class:"flex items-center"},Ql={class:"p-6"},Kl={key:0,class:"space-y-6"},Yl={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ea={class:"mt-4 flex space-x-3"},ta={key:1,class:"space-y-6"},sa={class:"mb-6"},oa={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},na={class:"mb-6"},la={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},aa={class:"flex space-x-3"},ia={key:2,class:"space-y-6"},ra={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},da={class:"mt-4 flex space-x-3"},ua={key:3,class:"space-y-6"},ga={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ma={class:"mt-6"},ca={class:"space-y-4"},pa={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},va=["onUpdate:modelValue"],xa=["onUpdate:modelValue"],fa=["onUpdate:modelValue"],ba={class:"mt-4 flex space-x-3"},ya={key:4,class:"space-y-6"},ha={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ka={class:"mt-4 flex space-x-3"},wa={key:5,class:"space-y-6"},_a={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},$a={class:"mt-4 flex space-x-3"},Ca={key:6,class:"space-y-6"},Sa={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ma={class:"md:col-span-2"},Ua={class:"mt-4 flex space-x-3"},Ba={key:7,class:"space-y-6"},Ta={class:"space-y-6"},Va={class:"flex items-center"},ja={class:"mt-4 flex space-x-3"},Aa={__name:"Settings",setup(P){const v=_("auth"),U=_(!1),x=_([{key:"auth",name:"Authentication",icon:"svg"},{key:"payment",name:"Payments",icon:"svg"},{key:"sms",name:"SMS Gateway",icon:"svg"},{key:"whatsapp",name:"WhatsApp API",icon:"svg"},{key:"email",name:"Email Settings",icon:"svg"},{key:"analytics",name:"Analytics",icon:"svg"},{key:"maps",name:"Google Maps",icon:"svg"},{key:"seo",name:"SEO/Meta",icon:"svg"}]),o=de({auth:{provider:"otpless",api_key:"",app_id:"",callback_url:"https://yourdomain.com/auth/callback"},payment:{primary_gateway:"razorpay",mode:"test",api_key:"",secret_key:"",webhook_secret:"",currency:"INR",upi_id:"",merchant_name:""},sms:{provider:"msg91",api_key:"",sender_id:"",template_id:""},whatsapp:{provider:"gupshup",api_token:"",phone_number_id:"",business_account_id:"",templates:[{name:"booking_confirmation",id:"",language:"en"},{name:"event_reminder",id:"",language:"en"},{name:"ticket_delivery",id:"",language:"en"}]},email:{smtp_host:"smtp.gmail.com",smtp_port:587,username:"",password:"",from_email:"",from_name:"Event Manager",encryption:"tls"},analytics:{google_analytics_id:"",meta_pixel_id:"",google_tag_manager_id:""},maps:{google_maps_api_key:"",default_zoom:15,default_center:{lat:28.6139,lng:77.209}},seo:{default_meta_title:"Event Manager - Book Amazing Events",default_meta_description:"Discover and book amazing events near you",default_og_image:"",enable_json_ld:!0,site_name:"Event Manager"}}),V=de({auth:"disconnected",payment:"disconnected",sms:"disconnected",whatsapp:"disconnected",email:"disconnected",analytics:"connected",maps:"connected",seo:"connected"}),g=j=>V[j],y=async j=>{V[j]="testing",setTimeout(()=>{V[j]=Math.random()>.3?"connected":"disconnected";const s=V[j]==="connected"?`${j.toUpperCase()} connection successful!`:`${j.toUpperCase()} connection failed. Please check your credentials.`;alert(s)},2e3)},r=j=>{U.value=!1,alert(`${j.toUpperCase()} settings saved successfully!`)},f=()=>{U.value=!1,alert("All settings saved successfully!")},h=async()=>{const j=["auth","payment","sms","whatsapp","email"];for(const t of j)o[t]&&Object.values(o[t]).some(s=>s!=="")&&(await y(t),await new Promise(s=>setTimeout(s,1e3)))};return Object.keys(o).forEach(j=>{}),(j,t)=>(a(),X(oe,null,{default:I(()=>[e("div",Fl,[e("div",Ol,[t[55]||(t[55]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"System Settings"),e("p",{class:"text-gray-600"},"Configure API integrations, payment gateways, and system preferences")],-1)),e("div",Jl,[e("button",{onClick:h,class:"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"}," Test All Connections "),e("button",{onClick:f,disabled:!U.value,class:"bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"}," Save All Changes ",8,Wl)])]),e("div",Hl,[e("div",Zl,[e("nav",Gl,[(a(!0),d(J,null,W(x.value,s=>(a(),d("button",{key:s.key,onClick:E=>v.value=s.key,class:R([v.value===s.key?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"])},[e("div",ql,[(a(),X(me(s.icon),{class:"w-5 h-5 mr-2"})),O(" "+u(s.name)+" ",1),g(s.key)?(a(),d("span",{key:0,class:R([g(s.key)==="connected"?"bg-green-100 text-green-800":"bg-red-100 text-red-800","ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},u(g(s.key)==="connected"?"Connected":"Not Connected"),3)):z("",!0)])],10,Xl))),128))])]),e("div",Ql,[v.value==="auth"?(a(),d("div",Kl,[e("div",null,[t[61]||(t[61]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"OTP-less Authentication",-1)),e("div",Yl,[e("div",null,[t[57]||(t[57]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Provider",-1)),c(e("select",{"onUpdate:modelValue":t[0]||(t[0]=s=>o.auth.provider=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[56]||(t[56]=[e("option",{value:""},"Select Provider",-1),e("option",{value:"otpless"},"OTPless",-1),e("option",{value:"firebase"},"Firebase Auth",-1),e("option",{value:"auth0"},"Auth0",-1)]),512),[[L,o.auth.provider]])]),e("div",null,[t[58]||(t[58]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"API Key",-1)),c(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>o.auth.api_key=s),type:"password",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.auth.api_key]])]),e("div",null,[t[59]||(t[59]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"App ID",-1)),c(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>o.auth.app_id=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.auth.app_id]])]),e("div",null,[t[60]||(t[60]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Callback URL",-1)),c(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>o.auth.callback_url=s),type:"url",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.auth.callback_url]])])]),e("div",ea,[e("button",{onClick:t[4]||(t[4]=s=>y("auth")),class:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"}," Test Connection "),e("button",{onClick:t[5]||(t[5]=s=>r("auth")),class:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," Save ")])])])):z("",!0),v.value==="payment"?(a(),d("div",ta,[e("div",null,[t[75]||(t[75]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Payment Gateway Configuration",-1)),e("div",sa,[t[71]||(t[71]=e("h4",{class:"text-md font-medium text-gray-800 mb-3"},"Primary Gateway",-1)),e("div",oa,[e("div",null,[t[63]||(t[63]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Gateway",-1)),c(e("select",{"onUpdate:modelValue":t[6]||(t[6]=s=>o.payment.primary_gateway=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[62]||(t[62]=[e("option",{value:""},"Select Gateway",-1),e("option",{value:"razorpay"},"Razorpay",-1),e("option",{value:"phonepe"},"PhonePe",-1),e("option",{value:"payu"},"PayU",-1),e("option",{value:"stripe"},"Stripe",-1)]),512),[[L,o.payment.primary_gateway]])]),e("div",null,[t[65]||(t[65]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Mode",-1)),c(e("select",{"onUpdate:modelValue":t[7]||(t[7]=s=>o.payment.mode=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[64]||(t[64]=[e("option",{value:"test"},"Test Mode",-1),e("option",{value:"live"},"Live Mode",-1)]),512),[[L,o.payment.mode]])]),e("div",null,[t[66]||(t[66]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"API Key",-1)),c(e("input",{"onUpdate:modelValue":t[8]||(t[8]=s=>o.payment.api_key=s),type:"password",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.payment.api_key]])]),e("div",null,[t[67]||(t[67]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Secret Key",-1)),c(e("input",{"onUpdate:modelValue":t[9]||(t[9]=s=>o.payment.secret_key=s),type:"password",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.payment.secret_key]])]),e("div",null,[t[68]||(t[68]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Webhook Secret",-1)),c(e("input",{"onUpdate:modelValue":t[10]||(t[10]=s=>o.payment.webhook_secret=s),type:"password",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.payment.webhook_secret]])]),e("div",null,[t[70]||(t[70]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Currency",-1)),c(e("select",{"onUpdate:modelValue":t[11]||(t[11]=s=>o.payment.currency=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[69]||(t[69]=[e("option",{value:"INR"},"INR (₹)",-1),e("option",{value:"USD"},"USD ($)",-1),e("option",{value:"EUR"},"EUR (€)",-1)]),512),[[L,o.payment.currency]])])])]),e("div",na,[t[74]||(t[74]=e("h4",{class:"text-md font-medium text-gray-800 mb-3"},"UPI Configuration",-1)),e("div",la,[e("div",null,[t[72]||(t[72]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"UPI ID",-1)),c(e("input",{"onUpdate:modelValue":t[12]||(t[12]=s=>o.payment.upi_id=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.payment.upi_id]])]),e("div",null,[t[73]||(t[73]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Merchant Name",-1)),c(e("input",{"onUpdate:modelValue":t[13]||(t[13]=s=>o.payment.merchant_name=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.payment.merchant_name]])])])]),e("div",aa,[e("button",{onClick:t[14]||(t[14]=s=>y("payment")),class:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"}," Test Payment "),e("button",{onClick:t[15]||(t[15]=s=>r("payment")),class:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," Save ")])])])):z("",!0),v.value==="sms"?(a(),d("div",ia,[e("div",null,[t[81]||(t[81]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"SMS Gateway Configuration",-1)),e("div",ra,[e("div",null,[t[77]||(t[77]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Provider",-1)),c(e("select",{"onUpdate:modelValue":t[16]||(t[16]=s=>o.sms.provider=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[76]||(t[76]=[e("option",{value:""},"Select Provider",-1),e("option",{value:"twilio"},"Twilio",-1),e("option",{value:"msg91"},"MSG91",-1),e("option",{value:"textlocal"},"TextLocal",-1),e("option",{value:"aws_sns"},"AWS SNS",-1)]),512),[[L,o.sms.provider]])]),e("div",null,[t[78]||(t[78]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"API Key",-1)),c(e("input",{"onUpdate:modelValue":t[17]||(t[17]=s=>o.sms.api_key=s),type:"password",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.sms.api_key]])]),e("div",null,[t[79]||(t[79]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Sender ID",-1)),c(e("input",{"onUpdate:modelValue":t[18]||(t[18]=s=>o.sms.sender_id=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.sms.sender_id]])]),e("div",null,[t[80]||(t[80]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Template ID (Optional)",-1)),c(e("input",{"onUpdate:modelValue":t[19]||(t[19]=s=>o.sms.template_id=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.sms.template_id]])])]),e("div",da,[e("button",{onClick:t[20]||(t[20]=s=>y("sms")),class:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"}," Send Test SMS "),e("button",{onClick:t[21]||(t[21]=s=>r("sms")),class:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," Save ")])])])):z("",!0),v.value==="whatsapp"?(a(),d("div",ua,[e("div",null,[t[92]||(t[92]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"WhatsApp Business API",-1)),e("div",ga,[e("div",null,[t[83]||(t[83]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Provider",-1)),c(e("select",{"onUpdate:modelValue":t[22]||(t[22]=s=>o.whatsapp.provider=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[82]||(t[82]=[e("option",{value:""},"Select Provider",-1),e("option",{value:"gupshup"},"Gupshup",-1),e("option",{value:"gallabox"},"Gallabox",-1),e("option",{value:"twilio"},"Twilio",-1),e("option",{value:"meta"},"Meta Business",-1)]),512),[[L,o.whatsapp.provider]])]),e("div",null,[t[84]||(t[84]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"API Token",-1)),c(e("input",{"onUpdate:modelValue":t[23]||(t[23]=s=>o.whatsapp.api_token=s),type:"password",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.whatsapp.api_token]])]),e("div",null,[t[85]||(t[85]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Phone Number ID",-1)),c(e("input",{"onUpdate:modelValue":t[24]||(t[24]=s=>o.whatsapp.phone_number_id=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.whatsapp.phone_number_id]])]),e("div",null,[t[86]||(t[86]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Business Account ID",-1)),c(e("input",{"onUpdate:modelValue":t[25]||(t[25]=s=>o.whatsapp.business_account_id=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.whatsapp.business_account_id]])])]),e("div",ma,[t[91]||(t[91]=e("h4",{class:"text-md font-medium text-gray-800 mb-3"},"Message Templates",-1)),e("div",ca,[(a(!0),d(J,null,W(o.whatsapp.templates,s=>(a(),d("div",{key:s.name,class:"border border-gray-200 rounded-lg p-4"},[e("div",pa,[e("div",null,[t[87]||(t[87]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Template Name",-1)),c(e("input",{"onUpdate:modelValue":E=>s.name=E,type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,8,va),[[M,s.name]])]),e("div",null,[t[88]||(t[88]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Template ID",-1)),c(e("input",{"onUpdate:modelValue":E=>s.id=E,type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,8,xa),[[M,s.id]])]),e("div",null,[t[90]||(t[90]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Language",-1)),c(e("select",{"onUpdate:modelValue":E=>s.language=E,class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[89]||(t[89]=[e("option",{value:"en"},"English",-1),e("option",{value:"hi"},"Hindi",-1),e("option",{value:"en_US"},"English (US)",-1)]),8,fa),[[L,s.language]])])])]))),128))])]),e("div",ba,[e("button",{onClick:t[26]||(t[26]=s=>y("whatsapp")),class:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"}," Send Test Message "),e("button",{onClick:t[27]||(t[27]=s=>r("whatsapp")),class:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," Save ")])])])):z("",!0),v.value==="email"?(a(),d("div",ya,[e("div",null,[t[101]||(t[101]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Email Configuration",-1)),e("div",ha,[e("div",null,[t[93]||(t[93]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"SMTP Host",-1)),c(e("input",{"onUpdate:modelValue":t[28]||(t[28]=s=>o.email.smtp_host=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.email.smtp_host]])]),e("div",null,[t[94]||(t[94]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"SMTP Port",-1)),c(e("input",{"onUpdate:modelValue":t[29]||(t[29]=s=>o.email.smtp_port=s),type:"number",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.email.smtp_port]])]),e("div",null,[t[95]||(t[95]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Username",-1)),c(e("input",{"onUpdate:modelValue":t[30]||(t[30]=s=>o.email.username=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.email.username]])]),e("div",null,[t[96]||(t[96]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Password",-1)),c(e("input",{"onUpdate:modelValue":t[31]||(t[31]=s=>o.email.password=s),type:"password",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.email.password]])]),e("div",null,[t[97]||(t[97]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"From Email",-1)),c(e("input",{"onUpdate:modelValue":t[32]||(t[32]=s=>o.email.from_email=s),type:"email",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.email.from_email]])]),e("div",null,[t[98]||(t[98]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"From Name",-1)),c(e("input",{"onUpdate:modelValue":t[33]||(t[33]=s=>o.email.from_name=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.email.from_name]])]),e("div",null,[t[100]||(t[100]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Encryption",-1)),c(e("select",{"onUpdate:modelValue":t[34]||(t[34]=s=>o.email.encryption=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[99]||(t[99]=[e("option",{value:"tls"},"TLS",-1),e("option",{value:"ssl"},"SSL",-1),e("option",{value:""},"None",-1)]),512),[[L,o.email.encryption]])])]),e("div",ka,[e("button",{onClick:t[35]||(t[35]=s=>y("email")),class:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"}," Send Test Email "),e("button",{onClick:t[36]||(t[36]=s=>r("email")),class:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," Save ")])])])):z("",!0),v.value==="analytics"?(a(),d("div",wa,[e("div",null,[t[105]||(t[105]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Analytics & Tracking",-1)),e("div",_a,[e("div",null,[t[102]||(t[102]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Google Analytics ID",-1)),c(e("input",{"onUpdate:modelValue":t[37]||(t[37]=s=>o.analytics.google_analytics_id=s),type:"text",placeholder:"G-XXXXXXXXXX",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.analytics.google_analytics_id]])]),e("div",null,[t[103]||(t[103]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Meta Pixel ID",-1)),c(e("input",{"onUpdate:modelValue":t[38]||(t[38]=s=>o.analytics.meta_pixel_id=s),type:"text",placeholder:"123456789012345",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.analytics.meta_pixel_id]])]),e("div",null,[t[104]||(t[104]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Google Tag Manager ID",-1)),c(e("input",{"onUpdate:modelValue":t[39]||(t[39]=s=>o.analytics.google_tag_manager_id=s),type:"text",placeholder:"GTM-XXXXXXX",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.analytics.google_tag_manager_id]])])]),e("div",$a,[e("button",{onClick:t[40]||(t[40]=s=>y("analytics")),class:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"}," Verify Tracking "),e("button",{onClick:t[41]||(t[41]=s=>r("analytics")),class:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," Save ")])])])):z("",!0),v.value==="maps"?(a(),d("div",Ca,[e("div",null,[t[111]||(t[111]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Google Maps Configuration",-1)),e("div",Sa,[e("div",Ma,[t[106]||(t[106]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Google Maps API Key",-1)),c(e("input",{"onUpdate:modelValue":t[42]||(t[42]=s=>o.maps.google_maps_api_key=s),type:"password",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.maps.google_maps_api_key]]),t[107]||(t[107]=e("p",{class:"text-xs text-gray-500 mt-1"},"Required for displaying event locations on maps",-1))]),e("div",null,[t[108]||(t[108]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Default Zoom Level",-1)),c(e("input",{"onUpdate:modelValue":t[43]||(t[43]=s=>o.maps.default_zoom=s),type:"number",min:"1",max:"20",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.maps.default_zoom]])]),e("div",null,[t[109]||(t[109]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Default Center (Latitude)",-1)),c(e("input",{"onUpdate:modelValue":t[44]||(t[44]=s=>o.maps.default_center.lat=s),type:"number",step:"0.000001",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.maps.default_center.lat]])]),e("div",null,[t[110]||(t[110]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Default Center (Longitude)",-1)),c(e("input",{"onUpdate:modelValue":t[45]||(t[45]=s=>o.maps.default_center.lng=s),type:"number",step:"0.000001",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.maps.default_center.lng]])])]),e("div",Ua,[e("button",{onClick:t[46]||(t[46]=s=>y("maps")),class:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"}," Test Maps API "),e("button",{onClick:t[47]||(t[47]=s=>r("maps")),class:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," Save ")])])])):z("",!0),v.value==="seo"?(a(),d("div",Ba,[e("div",null,[t[120]||(t[120]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"SEO & Meta Configuration",-1)),e("div",Ta,[e("div",null,[t[112]||(t[112]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Default Meta Title",-1)),c(e("input",{"onUpdate:modelValue":t[48]||(t[48]=s=>o.seo.default_meta_title=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.seo.default_meta_title]]),t[113]||(t[113]=e("p",{class:"text-xs text-gray-500 mt-1"},"Used when events don't have custom meta titles",-1))]),e("div",null,[t[114]||(t[114]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Default Meta Description",-1)),c(e("textarea",{"onUpdate:modelValue":t[49]||(t[49]=s=>o.seo.default_meta_description=s),rows:"3",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.seo.default_meta_description]]),t[115]||(t[115]=e("p",{class:"text-xs text-gray-500 mt-1"},"Used when events don't have custom meta descriptions",-1))]),e("div",null,[t[116]||(t[116]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Default OG Image URL",-1)),c(e("input",{"onUpdate:modelValue":t[50]||(t[50]=s=>o.seo.default_og_image=s),type:"url",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.seo.default_og_image]]),t[117]||(t[117]=e("p",{class:"text-xs text-gray-500 mt-1"},"Default image for social media sharing",-1))]),e("div",null,[t[118]||(t[118]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Site Name",-1)),c(e("input",{"onUpdate:modelValue":t[51]||(t[51]=s=>o.seo.site_name=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[M,o.seo.site_name]])]),e("div",Va,[c(e("input",{"onUpdate:modelValue":t[52]||(t[52]=s=>o.seo.enable_json_ld=s),type:"checkbox",class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,512),[[re,o.seo.enable_json_ld]]),t[119]||(t[119]=e("label",{class:"ml-2 text-sm text-gray-700"},"Enable JSON-LD structured data",-1))])]),e("div",ja,[e("button",{onClick:t[53]||(t[53]=s=>y("seo")),class:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"}," Validate SEO "),e("button",{onClick:t[54]||(t[54]=s=>r("seo")),class:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," Save ")])])])):z("",!0)])])])]),_:1}))}},Ra=Object.freeze(Object.defineProperty({__proto__:null,default:Aa},Symbol.toStringTag,{value:"Module"}));export{Ea as B,Da as D,Ia as E,Ra as S,La as U,oe as _,ft as a,yt as b,be as u};
