<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Simple Header -->
    <header v-if="!isAuthPage" class="bg-white shadow-sm border-b border-gray-200">
      <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <router-link to="/" class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 2L3 7v11a1 1 0 001 1h3v-6h6v6h3a1 1 0 001-1V7l-7-5z"/>
                </svg>
              </div>
              <span class="text-xl font-bold text-gray-900">EventManager</span>
            </router-link>
          </div>
          <div class="flex items-center space-x-4">
            <router-link to="/" class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium">Home</router-link>
            <router-link to="/events" class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium">Events</router-link>
            <router-link to="/login" class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium">Login</router-link>
            <router-link to="/register" class="bg-indigo-600 text-white hover:bg-indigo-700 px-4 py-2 rounded-md text-sm font-medium">Sign Up</router-link>
          </div>
        </div>
      </nav>
    </header>

    <!-- Main Content -->
    <main :class="{ 'pt-0': isAuthPage, 'pt-16': !isAuthPage }">
      <router-view />
    </main>

    <!-- Loading Overlay -->
    <div v-if="isInitializing" class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
        <p class="mt-4 text-gray-600">Loading...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const authStore = useAuthStore()
const isInitializing = ref(true)

// Check if current route is an auth page
const isAuthPage = computed(() => {
  return ['login', 'register'].includes(route.name)
})

onMounted(async () => {
  // Initialize auth state from localStorage
  authStore.initializeAuth()

  // If user is logged in, fetch fresh user data
  if (authStore.isAuthenticated) {
    await authStore.fetchUser()
  }

  isInitializing.value = false
})
</script>

<style>
/* Global styles */
body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
