<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Header -->
    <AppHeader v-if="!isAuthPage" />

    <!-- Main Content -->
    <main :class="{ 'pt-0': isAuthPage, 'pt-16': !isAuthPage }">
      <router-view />
    </main>

    <!-- Footer -->
    <AppFooter v-if="!isAuthPage" />

    <!-- Loading Overlay -->
    <div v-if="isInitializing" class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
        <p class="mt-4 text-gray-600">Loading...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'

const route = useRoute()
const authStore = useAuthStore()
const isInitializing = ref(true)

// Check if current route is an auth page
const isAuthPage = computed(() => {
  return ['login', 'register'].includes(route.name)
})

onMounted(async () => {
  // Initialize auth state from localStorage
  authStore.initializeAuth()

  // If user is logged in, fetch fresh user data
  if (authStore.isAuthenticated) {
    await authStore.fetchUser()
  }

  isInitializing.value = false
})
</script>

<style>
/* Global styles */
body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
