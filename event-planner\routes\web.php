<?php

use Illuminate\Support\Facades\Route;

// Test route
Route::get('/test', function () {
    return response()->json(['message' => '<PERSON><PERSON> is working!']);
});

// Simple HTML test route
Route::get('/simple', function () {
    return '
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Test</title>
        <script src="https://cdn.tailwindcss.com"></script>
    </head>
    <body class="bg-gray-100">
        <div class="min-h-screen flex items-center justify-center">
            <div class="bg-white p-8 rounded-lg shadow-lg text-center">
                <h1 class="text-3xl font-bold text-green-600 mb-4">✅ Laravel Route Works!</h1>
                <p class="text-gray-600 mb-4">This page loads directly from Laravel without Vue.js</p>
                <div class="space-y-4">
                    <a href="/" class="block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Back to Main App</a>
                    <a href="/test.html" class="block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Test Vue.js Separately</a>
                    <a href="/api/events" class="block bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">Test API</a>
                </div>
            </div>
        </div>
    </body>
    </html>';
});

// Serve Vue.js application for all frontend routes
Route::get('/{any}', function () {
    return view('app');
})->where('any', '^(?!api).*$');
