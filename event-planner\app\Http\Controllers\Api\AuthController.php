<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;

class AuthController extends Controller
{
    /**
     * Admin login with email/password
     */
    public function adminLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            // Check if user has admin roles
            if (!$user->hasAnyRole(['Super Admin', 'Manager', 'Ticket Booker', 'Scanner'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access'
                ], 403);
            }

            // Update last login
            $user->update(['last_login_at' => now()]);

            $token = $user->createToken('admin-token')->plainTextToken;

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => $user->load('roles', 'permissions'),
                    'token' => $token,
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid credentials'
        ], 401);
    }

    /**
     * User registration/login with magic link (OTP-less)
     */
    public function requestMagicLink(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'name' => 'required_if:type,register|string|max:255',
            'type' => 'required|in:login,register'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $email = $request->email;
        $type = $request->type;

        if ($type === 'register') {
            // Check if user already exists
            if (User::where('email', $email)->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'User already exists'
                ], 409);
            }

            // Create new user
            $user = User::create([
                'name' => $request->name,
                'email' => $email,
                'password' => Hash::make(Str::random(32)), // Random password
                'login_type' => 'magic_link',
                'is_active' => true,
            ]);
        } else {
            // Find existing user
            $user = User::where('email', $email)->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found'
                ], 404);
            }
        }

        // Generate magic link token
        $token = Str::random(64);
        $user->update([
            'remember_token' => $token,
        ]);

        // Send magic link email (implement email service)
        $magicLink = url("/auth/magic-login?token={$token}&email={$email}");

        // TODO: Send email with magic link
        // Mail::to($user->email)->send(new MagicLinkMail($magicLink));

        return response()->json([
            'success' => true,
            'message' => 'Magic link sent to your email',
            'data' => [
                'magic_link' => $magicLink, // Remove in production
            ]
        ]);
    }

    /**
     * Verify magic link and login user
     */
    public function verifyMagicLink(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|string',
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::where('email', $request->email)
                   ->where('remember_token', $request->token)
                   ->first();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired magic link'
            ], 401);
        }

        // Clear the token and update login info
        $user->update([
            'remember_token' => null,
            'last_login_at' => now(),
            'email_verified' => true,
        ]);

        $token = $user->createToken('user-token')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => $user,
                'token' => $token,
            ]
        ]);
    }
}
