<?php

namespace App\Services;

use App\Models\Booking;
use App\Models\Payment;
use App\Services\PaymentGateways\RazorpayService;
use App\Services\PaymentGateways\StripeService;
use App\Services\PaymentGateways\PhonePeService;
use App\Services\PaymentGateways\PayUService;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    protected $gateways = [
        'razorpay' => RazorpayService::class,
        'stripe' => StripeService::class,
        'phonepe' => PhonePeService::class,
        'payu' => PayUService::class,
    ];

    /**
     * Create payment order
     */
    public function createPaymentOrder(Booking $booking, string $gateway = 'razorpay')
    {
        try {
            $gatewayService = $this->getGatewayService($gateway);
            
            // Create payment record
            $payment = Payment::create([
                'payment_id' => $this->generatePaymentId(),
                'booking_id' => $booking->id,
                'amount' => $booking->total_amount,
                'currency' => $booking->currency,
                'status' => 'pending',
                'gateway' => $gateway,
                'metadata' => [
                    'booking_number' => $booking->booking_number,
                    'event_title' => $booking->event->title,
                    'customer_info' => $booking->customer_info,
                ],
            ]);

            // Create order with gateway
            $orderData = $gatewayService->createOrder([
                'amount' => $booking->total_amount,
                'currency' => $booking->currency,
                'receipt' => $payment->payment_id,
                'notes' => [
                    'booking_id' => $booking->id,
                    'payment_id' => $payment->id,
                ],
            ]);

            // Update payment with gateway order details
            $payment->update([
                'gateway_order_id' => $orderData['order_id'],
                'gateway_response' => $orderData,
            ]);

            return [
                'success' => true,
                'payment' => $payment,
                'order_data' => $orderData,
            ];

        } catch (\Exception $e) {
            Log::error('Payment order creation failed', [
                'booking_id' => $booking->id,
                'gateway' => $gateway,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to create payment order: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify payment
     */
    public function verifyPayment(array $paymentData, string $gateway = 'razorpay')
    {
        try {
            $gatewayService = $this->getGatewayService($gateway);
            
            // Verify payment with gateway
            $verificationResult = $gatewayService->verifyPayment($paymentData);

            if (!$verificationResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Payment verification failed',
                ];
            }

            // Find payment record
            $payment = Payment::where('gateway_order_id', $paymentData['order_id'] ?? $paymentData['razorpay_order_id'])
                             ->first();

            if (!$payment) {
                return [
                    'success' => false,
                    'message' => 'Payment record not found',
                ];
            }

            // Update payment status
            $payment->update([
                'status' => 'completed',
                'gateway_payment_id' => $paymentData['payment_id'] ?? $paymentData['razorpay_payment_id'],
                'gateway_response' => array_merge($payment->gateway_response ?? [], $verificationResult['data']),
                'paid_at' => now(),
            ]);

            // Update booking status
            $booking = $payment->booking;
            $booking->update([
                'status' => 'confirmed',
                'payment_status' => 'completed',
                'payment_method' => $gateway,
                'payment_reference' => $payment->gateway_payment_id,
                'payment_date' => now(),
            ]);

            return [
                'success' => true,
                'payment' => $payment,
                'booking' => $booking,
            ];

        } catch (\Exception $e) {
            Log::error('Payment verification failed', [
                'payment_data' => $paymentData,
                'gateway' => $gateway,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Payment verification failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Process refund
     */
    public function processRefund(Payment $payment, float $amount = null)
    {
        try {
            $refundAmount = $amount ?? $payment->amount;
            $gatewayService = $this->getGatewayService($payment->gateway);

            // Process refund with gateway
            $refundResult = $gatewayService->refund([
                'payment_id' => $payment->gateway_payment_id,
                'amount' => $refundAmount,
                'reason' => 'requested_by_customer',
            ]);

            if (!$refundResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Refund processing failed',
                ];
            }

            // Update payment record
            $payment->update([
                'status' => 'refunded',
                'refund_amount' => $refundAmount,
                'refunded_at' => now(),
                'gateway_response' => array_merge($payment->gateway_response ?? [], $refundResult['data']),
            ]);

            // Update booking status
            $payment->booking->update([
                'status' => 'refunded',
                'payment_status' => 'refunded',
            ]);

            return [
                'success' => true,
                'payment' => $payment,
                'refund_data' => $refundResult['data'],
            ];

        } catch (\Exception $e) {
            Log::error('Refund processing failed', [
                'payment_id' => $payment->id,
                'amount' => $refundAmount ?? 0,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Refund processing failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus(string $paymentId, string $gateway)
    {
        try {
            $gatewayService = $this->getGatewayService($gateway);
            return $gatewayService->getPaymentStatus($paymentId);

        } catch (\Exception $e) {
            Log::error('Failed to get payment status', [
                'payment_id' => $paymentId,
                'gateway' => $gateway,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to get payment status',
            ];
        }
    }

    /**
     * Get gateway service instance
     */
    protected function getGatewayService(string $gateway)
    {
        if (!isset($this->gateways[$gateway])) {
            throw new \Exception("Unsupported payment gateway: {$gateway}");
        }

        $serviceClass = $this->gateways[$gateway];
        return new $serviceClass();
    }

    /**
     * Generate unique payment ID
     */
    protected function generatePaymentId(): string
    {
        return 'PAY' . date('Ymd') . str_pad(Payment::count() + 1, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get available payment gateways
     */
    public function getAvailableGateways(): array
    {
        return [
            'razorpay' => [
                'name' => 'Razorpay',
                'description' => 'Credit/Debit Cards, Net Banking, UPI, Wallets',
                'logo' => '/images/gateways/razorpay.png',
                'enabled' => config('services.razorpay.enabled', false),
            ],
            'stripe' => [
                'name' => 'Stripe',
                'description' => 'Credit/Debit Cards',
                'logo' => '/images/gateways/stripe.png',
                'enabled' => config('services.stripe.enabled', false),
            ],
            'phonepe' => [
                'name' => 'PhonePe',
                'description' => 'UPI, Cards, Net Banking',
                'logo' => '/images/gateways/phonepe.png',
                'enabled' => config('services.phonepe.enabled', false),
            ],
            'payu' => [
                'name' => 'PayU',
                'description' => 'Cards, Net Banking, EMI',
                'logo' => '/images/gateways/payu.png',
                'enabled' => config('services.payu.enabled', false),
            ],
        ];
    }
}
