<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Event Management
            'view-events',
            'create-events',
            'edit-events',
            'delete-events',
            'publish-events',

            // Booking Management
            'view-bookings',
            'create-bookings',
            'edit-bookings',
            'cancel-bookings',
            'refund-bookings',

            // Ticket Management
            'view-tickets',
            'scan-tickets',
            'validate-tickets',

            // User Management
            'view-users',
            'create-users',
            'edit-users',
            'delete-users',
            'manage-roles',

            // CMS Management
            'view-cms',
            'create-cms',
            'edit-cms',
            'delete-cms',
            'publish-cms',

            // API Settings
            'view-api-settings',
            'edit-api-settings',
            'test-api-settings',

            // Analytics & Reports
            'view-analytics',
            'view-reports',
            'export-data',

            // System Settings
            'view-settings',
            'edit-settings',
            'manage-system',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - Full access
        $superAdmin = Role::firstOrCreate(['name' => 'Super Admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Manager - Event and booking management
        $manager = Role::firstOrCreate(['name' => 'Manager']);
        $manager->givePermissionTo([
            'view-events', 'create-events', 'edit-events', 'publish-events',
            'view-bookings', 'create-bookings', 'edit-bookings', 'cancel-bookings',
            'view-tickets', 'scan-tickets', 'validate-tickets',
            'view-users', 'create-users', 'edit-users',
            'view-cms', 'create-cms', 'edit-cms', 'publish-cms',
            'view-analytics', 'view-reports', 'export-data',
        ]);

        // Ticket Booker - Manual booking capability
        $ticketBooker = Role::firstOrCreate(['name' => 'Ticket Booker']);
        $ticketBooker->givePermissionTo([
            'view-events',
            'view-bookings', 'create-bookings', 'edit-bookings',
            'view-tickets',
            'view-users', 'create-users', 'edit-users',
        ]);

        // Scanner - Ticket scanning only
        $scanner = Role::firstOrCreate(['name' => 'Scanner']);
        $scanner->givePermissionTo([
            'view-events',
            'view-tickets', 'scan-tickets', 'validate-tickets',
        ]);

        // Create default Super Admin user
        $superAdminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('password123'),
                'is_active' => true,
                'email_verified' => true,
            ]
        );

        $superAdminUser->assignRole('Super Admin');

        $this->command->info('Roles and permissions created successfully!');
        $this->command->info('Super Admin created: <EMAIL> / password123');
    }
}
