<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class TicketType extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'name',
        'description',
        'price',
        'quantity',
        'sold',
        'min_quantity',
        'max_quantity',
        'sale_start',
        'sale_end',
        'is_active',
        'sort_order',
        'benefits',
        'restrictions',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'sale_start' => 'datetime',
        'sale_end' => 'datetime',
        'is_active' => 'boolean',
        'benefits' => 'array',
        'restrictions' => 'array',
    ];

    /**
     * Relationships
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    public function bookingItems()
    {
        return $this->hasMany(BookingItem::class);
    }

    public function tickets()
    {
        return $this->hasMany(Ticket::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeVisible($query)
    {
        return $query->where('is_hidden', false);
    }

    public function scopeOnSale($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('sale_start_date')
              ->orWhere('sale_start_date', '<=', now());
        })->where(function ($q) {
            $q->whereNull('sale_end_date')
              ->orWhere('sale_end_date', '>=', now());
        });
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc');
    }

    /**
     * Accessors
     */
    public function getQuantityRemainingAttribute()
    {
        return $this->quantity_available - $this->quantity_sold;
    }

    public function getIsAvailableAttribute()
    {
        return $this->is_active &&
               $this->quantity_remaining > 0 &&
               (!$this->sale_start_date || $this->sale_start_date <= now()) &&
               (!$this->sale_end_date || $this->sale_end_date >= now());
    }

    public function getIsSoldOutAttribute()
    {
        return $this->quantity_remaining <= 0;
    }
}
