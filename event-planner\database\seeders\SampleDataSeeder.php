<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => \Hash::make('password'),
                'phone' => '+1234567890',
                'email_verified_at' => now(),
            ]
        );

        // Create regular user
        $user = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON> Doe',
                'password' => \Hash::make('password'),
                'phone' => '+1234567891',
                'email_verified_at' => now(),
            ]
        );

        // Create roles if they don't exist
        $superAdminRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'Super Admin']);
        $userRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'User']);

        // Assign roles
        $admin->assignRole($superAdminRole);
        $user->assignRole($userRole);

        // Create event categories
        $categories = [
            ['name' => 'Music & Concerts', 'description' => 'Live music events and concerts', 'color_code' => '#FF6B6B'],
            ['name' => 'Technology', 'description' => 'Tech conferences and workshops', 'color_code' => '#4ECDC4'],
            ['name' => 'Business', 'description' => 'Business networking and seminars', 'color_code' => '#45B7D1'],
            ['name' => 'Sports', 'description' => 'Sports events and competitions', 'color_code' => '#96CEB4'],
            ['name' => 'Arts & Culture', 'description' => 'Art exhibitions and cultural events', 'color_code' => '#FFEAA7'],
        ];

        foreach ($categories as $category) {
            \App\Models\EventCategory::firstOrCreate(
                ['name' => $category['name']],
                $category
            );
        }

        // Create sample events
        $events = [
            [
                'title' => 'Summer Music Festival 2025',
                'slug' => 'summer-music-festival-2025',
                'description' => 'Join us for the biggest music festival of the year featuring top artists from around the world.',
                'short_description' => 'The biggest music festival of 2025',
                'category_id' => 1,
                'venue_name' => 'Central Park',
                'venue_address' => '123 Park Avenue, New York, NY',
                'start_date' => now()->addDays(30),
                'end_date' => now()->addDays(32),
                'booking_start_date' => now(),
                'booking_end_date' => now()->addDays(25),
                'max_capacity' => 5000,
                'is_featured' => true,
                'is_published' => true,
                'status' => 'published',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Tech Conference 2025',
                'slug' => 'tech-conference-2025',
                'description' => 'Learn about the latest trends in technology from industry experts.',
                'short_description' => 'Latest tech trends and innovations',
                'category_id' => 2,
                'venue_name' => 'Convention Center',
                'venue_address' => '456 Tech Street, San Francisco, CA',
                'start_date' => now()->addDays(45),
                'end_date' => now()->addDays(47),
                'booking_start_date' => now(),
                'booking_end_date' => now()->addDays(40),
                'max_capacity' => 1000,
                'is_featured' => true,
                'is_published' => true,
                'status' => 'published',
                'created_by' => $admin->id,
            ],
            [
                'title' => 'Business Networking Event',
                'slug' => 'business-networking-event',
                'description' => 'Connect with business professionals and expand your network.',
                'short_description' => 'Professional networking opportunity',
                'category_id' => 3,
                'venue_name' => 'Business Center',
                'venue_address' => '789 Business Blvd, Chicago, IL',
                'start_date' => now()->addDays(15),
                'end_date' => now()->addDays(15),
                'booking_start_date' => now(),
                'booking_end_date' => now()->addDays(10),
                'max_capacity' => 200,
                'is_featured' => false,
                'is_published' => true,
                'status' => 'published',
                'created_by' => $admin->id,
            ],
        ];

        foreach ($events as $eventData) {
            \App\Models\Event::firstOrCreate(
                ['slug' => $eventData['slug']],
                $eventData
            );
        }

        echo "Sample data created successfully!\n";
        echo "Admin login: <EMAIL> / password\n";
        echo "User login: <EMAIL> / password\n";
    }
}
