import{r as v,B as $,l as N,w as j,h as e,f as r,i as b,k as p,F as m,m as z,t as a,C as f,x as R,y as E,g as o,n as S}from"./vue-vendor-BupLktX_.js";import{_ as F}from"./admin-9F2yeZU0.js";import"./chart-vendor-Db3utXXw.js";const O={class:"space-y-6"},T={class:"flex justify-between items-center"},L={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},q={class:"flex items-center justify-between"},I={class:"text-lg font-semibold text-gray-900"},G={class:"text-sm text-gray-600"},H={class:"text-xs text-gray-500 mt-2"},J={class:"bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden"},Q={class:"overflow-x-auto"},W={class:"min-w-full"},X={class:"bg-gray-50"},Y={class:"bg-white divide-y divide-gray-200"},Z={class:"bg-gray-25"},K={class:"px-6 py-4 text-sm font-semibold text-gray-900 bg-gray-50"},ee={class:"px-6 py-4 text-sm text-gray-900 pl-12"},se={class:"font-medium"},te={class:"text-xs text-gray-500"},ie=["checked","onChange","disabled"],ne={class:"px-6 py-4 bg-gray-50 border-t border-gray-200"},oe={class:"flex justify-between items-center"},re=["disabled"],ae={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},de={class:"bg-white shadow-sm rounded-lg border border-gray-200"},le={class:"divide-y divide-gray-200"},ce=["onClick"],ue={class:"flex items-center justify-between"},me={class:"flex items-center"},ge={class:"text-sm font-medium text-gray-900"},pe={class:"text-xs text-gray-500"},ve={class:"flex space-x-2"},ye=["onClick"],xe=["onClick"],be={class:"bg-white shadow-sm rounded-lg border border-gray-200"},fe={class:"px-6 py-4 border-b border-gray-200"},_e={class:"text-lg font-medium text-gray-900"},he={key:0,class:"p-6"},ke={class:"space-y-4"},we={class:"text-sm text-gray-600"},Ce={class:"text-sm text-gray-600"},Re={class:"space-y-2"},Ee={class:"text-sm text-gray-700"},Se={key:0,class:"pt-4 border-t border-gray-200"},Me={class:"flex space-x-3"},Pe={key:1,class:"p-6 text-center text-gray-500"},Ae={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Be={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},De={class:"mt-3"},Ue={class:"text-lg font-medium text-gray-900 mb-4"},Ve={class:"space-y-4"},$e={class:"flex justify-end space-x-3 mt-6"},Ne={type:"submit",class:"px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"},Te={__name:"Roles",setup(je){const y=v(!1),g=v(null),d=v(null),x=v(!1),l=$({name:"",description:""}),c=v([{id:1,name:"Super Admin",description:"Full system access with all permissions",user_count:1,is_system:!0,permissions:["*"]},{id:2,name:"Admin",description:"Administrative access to most features",user_count:3,is_system:!0,permissions:["events.create","events.edit","events.delete","bookings.view","bookings.edit","users.view","users.edit","reports.view"]},{id:3,name:"Manager",description:"Event and booking management",user_count:5,is_system:!1,permissions:["events.create","events.edit","bookings.view","bookings.edit","reports.view"]},{id:4,name:"Organizer",description:"Can create and manage their own events",user_count:12,is_system:!1,permissions:["events.create","events.edit","bookings.view"]},{id:5,name:"Attendee",description:"Basic user with booking capabilities",user_count:156,is_system:!0,permissions:["bookings.create","bookings.view"]}]),_=v([{name:"Events Management",permissions:[{key:"events.view",name:"View Events",description:"Can view all events"},{key:"events.create",name:"Create Events",description:"Can create new events"},{key:"events.edit",name:"Edit Events",description:"Can edit existing events"},{key:"events.delete",name:"Delete Events",description:"Can delete events"},{key:"events.publish",name:"Publish Events",description:"Can publish/unpublish events"}]},{name:"Bookings Management",permissions:[{key:"bookings.view",name:"View Bookings",description:"Can view all bookings"},{key:"bookings.create",name:"Create Bookings",description:"Can create bookings"},{key:"bookings.edit",name:"Edit Bookings",description:"Can edit booking details"},{key:"bookings.cancel",name:"Cancel Bookings",description:"Can cancel bookings"},{key:"bookings.refund",name:"Process Refunds",description:"Can process refunds"}]},{name:"User Management",permissions:[{key:"users.view",name:"View Users",description:"Can view user profiles"},{key:"users.create",name:"Create Users",description:"Can create new users"},{key:"users.edit",name:"Edit Users",description:"Can edit user details"},{key:"users.delete",name:"Delete Users",description:"Can delete users"},{key:"users.suspend",name:"Suspend Users",description:"Can suspend/activate users"}]},{name:"Reports & Analytics",permissions:[{key:"reports.view",name:"View Reports",description:"Can view analytics and reports"},{key:"reports.export",name:"Export Reports",description:"Can export data and reports"}]},{name:"System Settings",permissions:[{key:"settings.view",name:"View Settings",description:"Can view system settings"},{key:"settings.edit",name:"Edit Settings",description:"Can modify system settings"},{key:"roles.manage",name:"Manage Roles",description:"Can create and edit roles"}]}]),h=i=>({"Super Admin":"bg-red-500",Admin:"bg-blue-500",Manager:"bg-purple-500",Organizer:"bg-green-500",Attendee:"bg-gray-500"})[i]||"bg-gray-500",M=(i,s)=>{const t=c.value.find(n=>n.id===i);return t?t.permissions.includes("*")?!0:t.permissions.includes(s):!1},P=(i,s,t)=>{const n=c.value.find(u=>u.id===i);if(!(!n||n.name==="Super Admin")){if(t)n.permissions.includes(s)||n.permissions.push(s);else{const u=n.permissions.indexOf(s);u>-1&&n.permissions.splice(u,1)}x.value=!0}},A=()=>d.value?d.value.permissions.includes("*")?["All Permissions"]:d.value.permissions:[],B=i=>{if(i==="All Permissions")return i;for(const s of _.value){const t=s.permissions.find(n=>n.key===i);if(t)return t.name}return i},D=()=>{x.value=!1,alert("Permissions saved successfully!")},k=i=>{g.value=i,l.name=i.name,l.description=i.description},w=i=>{var s;if(i.is_system){alert("System roles cannot be deleted");return}if(confirm(`Delete role "${i.name}"? This will affect ${i.user_count} users.`)){const t=c.value.findIndex(n=>n.id===i.id);t>-1&&c.value.splice(t,1),((s=d.value)==null?void 0:s.id)===i.id&&(d.value=null)}},U=()=>{if(g.value)g.value.name=l.name,g.value.description=l.description;else{const i={id:Date.now(),name:l.name,description:l.description,user_count:0,is_system:!1,permissions:[]};c.value.push(i)}C()},C=()=>{y.value=!1,g.value=null,l.name="",l.description=""};return(i,s)=>(o(),N(F,null,{default:j(()=>[e("div",O,[e("div",T,[s[5]||(s[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Role & Permission Management"),e("p",{class:"text-gray-600"},"Manage user roles and their permissions across the system")],-1)),e("button",{onClick:s[0]||(s[0]=t=>y.value=!0),class:"bg-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-indigo-700 transition-colors"}," Create Role ")]),e("div",L,[(o(!0),r(m,null,p(c.value,t=>(o(),r("div",{key:t.id,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},[e("div",q,[e("div",null,[e("h3",I,a(t.name),1),e("p",G,a(t.description),1),e("p",H,a(t.user_count)+" users",1)]),e("div",{class:S([h(t.name),"w-3 h-3 rounded-full"])},null,2)])]))),128))]),e("div",J,[s[8]||(s[8]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Permission Matrix"),e("p",{class:"text-sm text-gray-600"},"Configure permissions for each role")],-1)),e("div",Q,[e("table",W,[e("thead",X,[e("tr",null,[s[6]||(s[6]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Module / Permission ",-1)),(o(!0),r(m,null,p(c.value,t=>(o(),r("th",{key:t.id,class:"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"},a(t.name),1))),128))])]),e("tbody",Y,[(o(!0),r(m,null,p(_.value,t=>(o(),r(m,{key:t.name},[e("tr",Z,[e("td",K,a(t.name),1),(o(!0),r(m,null,p(c.value,n=>(o(),r("td",{key:n.id,class:"bg-gray-50"}))),128))]),(o(!0),r(m,null,p(t.permissions,n=>(o(),r("tr",{key:n.key},[e("td",ee,[e("div",null,[e("div",se,a(n.name),1),e("div",te,a(n.description),1)])]),(o(!0),r(m,null,p(c.value,u=>(o(),r("td",{key:u.id,class:"px-6 py-4 text-center"},[e("input",{type:"checkbox",checked:M(u.id,n.key),onChange:V=>P(u.id,n.key,V.target.checked),disabled:u.name==="Super Admin",class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 disabled:opacity-50"},null,40,ie)]))),128))]))),128))],64))),128))])])]),e("div",ne,[e("div",oe,[s[7]||(s[7]=e("div",{class:"text-sm text-gray-600"},[e("span",{class:"font-medium"},"Note:"),z(" Super Admin role has all permissions by default and cannot be modified. ")],-1)),e("button",{onClick:D,disabled:!x.value,class:"bg-indigo-600 text-white px-4 py-2 rounded-md font-medium hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"}," Save Changes ",8,re)])])]),e("div",ae,[e("div",de,[s[9]||(s[9]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Roles")],-1)),e("div",le,[(o(!0),r(m,null,p(c.value,t=>(o(),r("div",{key:t.id,class:"px-6 py-4 hover:bg-gray-50 cursor-pointer",onClick:n=>d.value=t},[e("div",ue,[e("div",me,[e("div",{class:S([h(t.name),"w-3 h-3 rounded-full mr-3"])},null,2),e("div",null,[e("div",ge,a(t.name),1),e("div",pe,a(t.user_count)+" users",1)])]),e("div",ve,[e("button",{onClick:f(n=>k(t),["stop"]),class:"text-indigo-600 hover:text-indigo-900 text-sm"},"Edit",8,ye),t.is_system?b("",!0):(o(),r("button",{key:0,onClick:f(n=>w(t),["stop"]),class:"text-red-600 hover:text-red-900 text-sm"},"Delete",8,xe))])])],8,ce))),128))])]),e("div",be,[e("div",fe,[e("h3",_e,a(d.value?d.value.name:"Select a Role"),1)]),d.value?(o(),r("div",he,[e("div",ke,[e("div",null,[s[10]||(s[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Description",-1)),e("p",we,a(d.value.description),1)]),e("div",null,[s[11]||(s[11]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Users with this role",-1)),e("p",Ce,a(d.value.user_count)+" users",1)]),e("div",null,[s[13]||(s[13]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Permissions",-1)),e("div",Re,[(o(!0),r(m,null,p(A(),t=>(o(),r("div",{key:t,class:"flex items-center"},[s[12]||(s[12]=e("svg",{class:"w-4 h-4 text-green-500 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)),e("span",Ee,a(B(t)),1)]))),128))])]),d.value.is_system?b("",!0):(o(),r("div",Se,[e("div",Me,[e("button",{onClick:s[1]||(s[1]=t=>k(d.value)),class:"bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"}," Edit Role "),e("button",{onClick:s[2]||(s[2]=t=>w(d.value)),class:"bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700"}," Delete Role ")])]))])])):(o(),r("div",Pe," Select a role to view details "))])]),y.value||g.value?(o(),r("div",Ae,[e("div",Be,[e("div",De,[e("h3",Ue,a(g.value?"Edit Role":"Create New Role"),1),e("form",{onSubmit:f(U,["prevent"])},[e("div",Ve,[e("div",null,[s[14]||(s[14]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Role Name",-1)),R(e("input",{"onUpdate:modelValue":s[3]||(s[3]=t=>l.name=t),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[E,l.name]])]),e("div",null,[s[15]||(s[15]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Description",-1)),R(e("textarea",{"onUpdate:modelValue":s[4]||(s[4]=t=>l.description=t),rows:"3",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[E,l.description]])])]),e("div",$e,[e("button",{type:"button",onClick:C,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"}," Cancel "),e("button",Ne,a(g.value?"Update":"Create")+" Role ",1)])],32)])])])):b("",!0)])]),_:1}))}};export{Te as default};
