{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "pwa:generate-sw": "workbox generateSW workbox-config.js"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@vueuse/core": "^10.5.0", "axios": "^1.8.2", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/vite": "^4.0.0", "@types/node": "^20.8.10", "@vitejs/plugin-vue": "^4.4.1", "autoprefixer": "^10.4.16", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "tailwindcss": "^4.0.0", "vite": "^6.2.4", "vite-plugin-pwa": "^0.17.4", "workbox-cli": "^7.0.0"}}