{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "pwa:generate-sw": "workbox generateSW workbox-config.js"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vueuse/core": "^10.11.1", "axios": "^1.8.2", "lodash-es": "^4.17.21", "pinia": "^2.3.1", "vue": "^3.3.8", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/vite": "^4.0.0", "@types/node": "^20.8.10", "@vitejs/plugin-vue": "^4.4.1", "autoprefixer": "^10.4.16", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "tailwindcss": "^4.0.0", "vite": "^6.2.4", "vite-plugin-pwa": "^0.17.4", "workbox-cli": "^7.0.0"}}