<?php

// Simple test script to create an event via API
$url = 'http://localhost:8000/api/admin/events';
$token = '1|ra6M9U4sDIYT2PEgsHsW5BnViychQNPq4UiwRu0g2542cfa4';

$data = [
    'title' => 'Liquid N Lights Interactive Art Experience',
    'description' => 'Experience our signature interactive art installation that combines the nostalgia of Lite Brite with modern Instagram culture. Each glowing bulb is filled with drinks for guest interaction, creating a unique and memorable experience.',
    'short_description' => 'Interactive art installation with glowing drink-filled bulbs',
    'category_id' => 1,
    'venue_name' => 'Downtown Art Gallery',
    'venue_address' => '123 Art Street, Downtown, City 12345',
    'venue_latitude' => 40.7128,
    'venue_longitude' => -74.0060,
    'start_date' => '2025-08-15 18:00:00',
    'end_date' => '2025-08-15 23:00:00',
    'booking_start_date' => '2025-07-15 00:00:00',
    'booking_end_date' => '2025-08-14 23:59:59',
    'max_capacity' => 200,
    'is_featured' => true,
    'is_published' => true,
    'status' => 'published',
    'seo_meta' => [
        'title' => 'Liquid N Lights Art Experience - Interactive Installation',
        'description' => 'Join us for an unforgettable interactive art experience combining nostalgia with modern culture',
        'keywords' => 'art, interactive, installation, drinks, experience'
    ]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n";
