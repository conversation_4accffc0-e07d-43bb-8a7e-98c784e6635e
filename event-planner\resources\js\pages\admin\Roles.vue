<template>
  <AdminLayout>
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Role & Permission Management</h1>
          <p class="text-gray-600">Manage user roles and their permissions across the system</p>
        </div>
        <button @click="showCreateModal = true" 
                class="bg-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-indigo-700 transition-colors">
          Create Role
        </button>
      </div>

      <!-- Roles Overview -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div v-for="role in roles" :key="role.id" 
             class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-900">{{ role.name }}</h3>
              <p class="text-sm text-gray-600">{{ role.description }}</p>
              <p class="text-xs text-gray-500 mt-2">{{ role.user_count }} users</p>
            </div>
            <div :class="getRoleColorClass(role.name)" class="w-3 h-3 rounded-full"></div>
          </div>
        </div>
      </div>

      <!-- Permission Matrix -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Permission Matrix</h3>
          <p class="text-sm text-gray-600">Configure permissions for each role</p>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Module / Permission
                </th>
                <th v-for="role in roles" :key="role.id" 
                    class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ role.name }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <template v-for="module in permissionModules" :key="module.name">
                <!-- Module Header -->
                <tr class="bg-gray-25">
                  <td class="px-6 py-4 text-sm font-semibold text-gray-900 bg-gray-50">
                    {{ module.name }}
                  </td>
                  <td v-for="role in roles" :key="role.id" class="bg-gray-50"></td>
                </tr>
                
                <!-- Module Permissions -->
                <tr v-for="permission in module.permissions" :key="permission.key">
                  <td class="px-6 py-4 text-sm text-gray-900 pl-12">
                    <div>
                      <div class="font-medium">{{ permission.name }}</div>
                      <div class="text-xs text-gray-500">{{ permission.description }}</div>
                    </div>
                  </td>
                  <td v-for="role in roles" :key="role.id" class="px-6 py-4 text-center">
                    <input type="checkbox" 
                           :checked="hasPermission(role.id, permission.key)"
                           @change="togglePermission(role.id, permission.key, $event.target.checked)"
                           :disabled="role.name === 'Super Admin'"
                           class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 disabled:opacity-50">
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>

        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-600">
              <span class="font-medium">Note:</span> Super Admin role has all permissions by default and cannot be modified.
            </div>
            <button @click="savePermissions" 
                    :disabled="!hasChanges"
                    class="bg-indigo-600 text-white px-4 py-2 rounded-md font-medium hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">
              Save Changes
            </button>
          </div>
        </div>
      </div>

      <!-- Role Details -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Role List -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Roles</h3>
          </div>
          <div class="divide-y divide-gray-200">
            <div v-for="role in roles" :key="role.id" 
                 class="px-6 py-4 hover:bg-gray-50 cursor-pointer"
                 @click="selectedRole = role">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div :class="getRoleColorClass(role.name)" class="w-3 h-3 rounded-full mr-3"></div>
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ role.name }}</div>
                    <div class="text-xs text-gray-500">{{ role.user_count }} users</div>
                  </div>
                </div>
                <div class="flex space-x-2">
                  <button @click.stop="editRole(role)" 
                          class="text-indigo-600 hover:text-indigo-900 text-sm">Edit</button>
                  <button v-if="!role.is_system" 
                          @click.stop="deleteRole(role)" 
                          class="text-red-600 hover:text-red-900 text-sm">Delete</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Role Details -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
              {{ selectedRole ? selectedRole.name : 'Select a Role' }}
            </h3>
          </div>
          <div v-if="selectedRole" class="p-6">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <p class="text-sm text-gray-600">{{ selectedRole.description }}</p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Users with this role</label>
                <p class="text-sm text-gray-600">{{ selectedRole.user_count }} users</p>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Permissions</label>
                <div class="space-y-2">
                  <div v-for="permission in getSelectedRolePermissions()" :key="permission" 
                       class="flex items-center">
                    <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <span class="text-sm text-gray-700">{{ getPermissionName(permission) }}</span>
                  </div>
                </div>
              </div>

              <div v-if="!selectedRole.is_system" class="pt-4 border-t border-gray-200">
                <div class="flex space-x-3">
                  <button @click="editRole(selectedRole)" 
                          class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                    Edit Role
                  </button>
                  <button @click="deleteRole(selectedRole)" 
                          class="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700">
                    Delete Role
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="p-6 text-center text-gray-500">
            Select a role to view details
          </div>
        </div>
      </div>

      <!-- Create/Edit Role Modal -->
      <div v-if="showCreateModal || editingRole" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              {{ editingRole ? 'Edit Role' : 'Create New Role' }}
            </h3>
            <form @submit.prevent="saveRole">
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Role Name</label>
                  <input v-model="roleForm.name" type="text" required 
                         class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea v-model="roleForm.description" rows="3" 
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
                </div>
              </div>
              <div class="flex justify-end space-x-3 mt-6">
                <button type="button" @click="cancelEdit" 
                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                  Cancel
                </button>
                <button type="submit" 
                        class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700">
                  {{ editingRole ? 'Update' : 'Create' }} Role
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import AdminLayout from '@/components/admin/AdminLayout.vue'

const showCreateModal = ref(false)
const editingRole = ref(null)
const selectedRole = ref(null)
const hasChanges = ref(false)

const roleForm = reactive({
  name: '',
  description: ''
})

const roles = ref([
  {
    id: 1,
    name: 'Super Admin',
    description: 'Full system access with all permissions',
    user_count: 1,
    is_system: true,
    permissions: ['*'] // All permissions
  },
  {
    id: 2,
    name: 'Admin',
    description: 'Administrative access to most features',
    user_count: 3,
    is_system: true,
    permissions: ['events.create', 'events.edit', 'events.delete', 'bookings.view', 'bookings.edit', 'users.view', 'users.edit', 'reports.view']
  },
  {
    id: 3,
    name: 'Manager',
    description: 'Event and booking management',
    user_count: 5,
    is_system: false,
    permissions: ['events.create', 'events.edit', 'bookings.view', 'bookings.edit', 'reports.view']
  },
  {
    id: 4,
    name: 'Organizer',
    description: 'Can create and manage their own events',
    user_count: 12,
    is_system: false,
    permissions: ['events.create', 'events.edit', 'bookings.view']
  },
  {
    id: 5,
    name: 'Attendee',
    description: 'Basic user with booking capabilities',
    user_count: 156,
    is_system: true,
    permissions: ['bookings.create', 'bookings.view']
  }
])

const permissionModules = ref([
  {
    name: 'Events Management',
    permissions: [
      { key: 'events.view', name: 'View Events', description: 'Can view all events' },
      { key: 'events.create', name: 'Create Events', description: 'Can create new events' },
      { key: 'events.edit', name: 'Edit Events', description: 'Can edit existing events' },
      { key: 'events.delete', name: 'Delete Events', description: 'Can delete events' },
      { key: 'events.publish', name: 'Publish Events', description: 'Can publish/unpublish events' }
    ]
  },
  {
    name: 'Bookings Management',
    permissions: [
      { key: 'bookings.view', name: 'View Bookings', description: 'Can view all bookings' },
      { key: 'bookings.create', name: 'Create Bookings', description: 'Can create bookings' },
      { key: 'bookings.edit', name: 'Edit Bookings', description: 'Can edit booking details' },
      { key: 'bookings.cancel', name: 'Cancel Bookings', description: 'Can cancel bookings' },
      { key: 'bookings.refund', name: 'Process Refunds', description: 'Can process refunds' }
    ]
  },
  {
    name: 'User Management',
    permissions: [
      { key: 'users.view', name: 'View Users', description: 'Can view user profiles' },
      { key: 'users.create', name: 'Create Users', description: 'Can create new users' },
      { key: 'users.edit', name: 'Edit Users', description: 'Can edit user details' },
      { key: 'users.delete', name: 'Delete Users', description: 'Can delete users' },
      { key: 'users.suspend', name: 'Suspend Users', description: 'Can suspend/activate users' }
    ]
  },
  {
    name: 'Reports & Analytics',
    permissions: [
      { key: 'reports.view', name: 'View Reports', description: 'Can view analytics and reports' },
      { key: 'reports.export', name: 'Export Reports', description: 'Can export data and reports' }
    ]
  },
  {
    name: 'System Settings',
    permissions: [
      { key: 'settings.view', name: 'View Settings', description: 'Can view system settings' },
      { key: 'settings.edit', name: 'Edit Settings', description: 'Can modify system settings' },
      { key: 'roles.manage', name: 'Manage Roles', description: 'Can create and edit roles' }
    ]
  }
])

const getRoleColorClass = (roleName) => {
  const colors = {
    'Super Admin': 'bg-red-500',
    'Admin': 'bg-blue-500',
    'Manager': 'bg-purple-500',
    'Organizer': 'bg-green-500',
    'Attendee': 'bg-gray-500'
  }
  return colors[roleName] || 'bg-gray-500'
}

const hasPermission = (roleId, permissionKey) => {
  const role = roles.value.find(r => r.id === roleId)
  if (!role) return false
  
  // Super Admin has all permissions
  if (role.permissions.includes('*')) return true
  
  return role.permissions.includes(permissionKey)
}

const togglePermission = (roleId, permissionKey, hasPermission) => {
  const role = roles.value.find(r => r.id === roleId)
  if (!role || role.name === 'Super Admin') return
  
  if (hasPermission) {
    if (!role.permissions.includes(permissionKey)) {
      role.permissions.push(permissionKey)
    }
  } else {
    const index = role.permissions.indexOf(permissionKey)
    if (index > -1) {
      role.permissions.splice(index, 1)
    }
  }
  
  hasChanges.value = true
}

const getSelectedRolePermissions = () => {
  if (!selectedRole.value) return []
  
  if (selectedRole.value.permissions.includes('*')) {
    return ['All Permissions']
  }
  
  return selectedRole.value.permissions
}

const getPermissionName = (permissionKey) => {
  if (permissionKey === 'All Permissions') return permissionKey
  
  for (const module of permissionModules.value) {
    const permission = module.permissions.find(p => p.key === permissionKey)
    if (permission) return permission.name
  }
  return permissionKey
}

const savePermissions = () => {
  // In real app, this would save to backend
  hasChanges.value = false
  alert('Permissions saved successfully!')
}

const editRole = (role) => {
  editingRole.value = role
  roleForm.name = role.name
  roleForm.description = role.description
}

const deleteRole = (role) => {
  if (role.is_system) {
    alert('System roles cannot be deleted')
    return
  }
  
  if (confirm(`Delete role "${role.name}"? This will affect ${role.user_count} users.`)) {
    const index = roles.value.findIndex(r => r.id === role.id)
    if (index > -1) {
      roles.value.splice(index, 1)
    }
    if (selectedRole.value?.id === role.id) {
      selectedRole.value = null
    }
  }
}

const saveRole = () => {
  if (editingRole.value) {
    // Update existing role
    editingRole.value.name = roleForm.name
    editingRole.value.description = roleForm.description
  } else {
    // Create new role
    const newRole = {
      id: Date.now(),
      name: roleForm.name,
      description: roleForm.description,
      user_count: 0,
      is_system: false,
      permissions: []
    }
    roles.value.push(newRole)
  }
  
  cancelEdit()
}

const cancelEdit = () => {
  showCreateModal.value = false
  editingRole.value = null
  roleForm.name = ''
  roleForm.description = ''
}
</script>
