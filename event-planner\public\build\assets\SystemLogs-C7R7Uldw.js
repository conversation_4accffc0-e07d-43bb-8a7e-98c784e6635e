import{r as a,c as v,o as H,l as O,w as J,h as e,t as i,x as h,y as R,z as b,f as n,i as W,F as w,k as _,g as l,n as f}from"./vue-vendor-BupLktX_.js";import{_ as q}from"./admin-9F2yeZU0.js";import"./chart-vendor-Db3utXXw.js";const G={class:"space-y-6"},Q={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},K={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},X={class:"flex items-center"},Y={class:"ml-4"},Z={class:"text-2xl font-bold text-gray-900"},ee={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},te={class:"flex items-center"},se={class:"ml-4"},oe={class:"text-2xl font-bold text-gray-900"},ie={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},re={class:"flex items-center"},ae={class:"ml-4"},le={class:"text-2xl font-bold text-gray-900"},ne={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},de={class:"flex flex-col lg:flex-row gap-4 items-center justify-between"},ce={class:"flex-1 max-w-md"},ue={class:"relative"},ge={class:"flex gap-4"},pe={class:"bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden"},me={class:"px-6 py-4 border-b border-gray-200"},xe={class:"text-lg font-medium text-gray-900"},ye={key:0,class:"flex justify-center py-12"},ve={key:1,class:"overflow-x-auto"},he={class:"min-w-full divide-y divide-gray-200"},fe={class:"bg-white divide-y divide-gray-200"},be={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},we={class:"px-6 py-4 whitespace-nowrap"},_e={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ke={class:"px-6 py-4 text-sm text-gray-900"},Ce={class:"max-w-xs truncate"},Le={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},De={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Se={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Me=["onClick"],Pe={key:2,class:"text-center py-12"},je={key:3,class:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6"},Ae={class:"flex-1 flex justify-between sm:hidden"},Be=["disabled"],ze=["disabled"],Ve={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Ee={class:"text-sm text-gray-700"},Ne={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Te=["disabled"],Ue=["onClick"],Fe=["disabled"],$e={class:"bg-white shadow-sm rounded-lg border border-gray-200"},Ie={class:"p-6"},He={class:"space-y-4"},Oe={class:"flex items-center"},Je={class:"text-sm font-medium text-gray-900"},Re={class:"text-xs text-gray-500"},We={class:"text-right"},qe={class:"text-xs text-gray-500"},Ge={class:"text-xs text-gray-400"},Ze={__name:"SystemLogs",setup(Qe){const S=a(!1),g=a(""),p=a(""),m=a(""),k=a("24h"),r=a(1),c=a(20),M=a(89),P=a(12),j=a(3),A=a([{id:1,timestamp:new Date(Date.now()-5*60*1e3),level:"info",category:"booking",message:"New booking created for Tech Conference 2025",user:"John Doe",ip_address:"*************",details:{booking_id:"TC2025-001",amount:2500}},{id:2,timestamp:new Date(Date.now()-15*60*1e3),level:"warning",category:"payment",message:"Payment gateway timeout - retrying",user:"Jane Smith",ip_address:"*************",details:{gateway:"razorpay",timeout:"30s"}},{id:3,timestamp:new Date(Date.now()-30*60*1e3),level:"error",category:"auth",message:"Failed login attempt - invalid credentials",user:null,ip_address:"************",details:{attempts:3,email:"<EMAIL>"}},{id:4,timestamp:new Date(Date.now()-45*60*1e3),level:"info",category:"system",message:"Database backup completed successfully",user:"System",ip_address:null,details:{size:"2.3GB",duration:"45s"}}]),B=a([{id:1,type:"warning",title:"Multiple Failed Login Attempts",description:"IP ************ attempted 5 failed logins",timestamp:new Date(Date.now()-10*60*1e3),ip_address:"************"},{id:2,type:"info",title:"Admin Login",description:"Administrator logged in successfully",timestamp:new Date(Date.now()-2*60*60*1e3),ip_address:"*************"},{id:3,type:"critical",title:"Suspicious Activity Detected",description:"Unusual booking pattern detected",timestamp:new Date(Date.now()-4*60*60*1e3),ip_address:"*************"}]),d=v(()=>{let o=A.value;if(g.value){const t=g.value.toLowerCase();o=o.filter(s=>s.message.toLowerCase().includes(t)||s.user&&s.user.toLowerCase().includes(t)||s.category.toLowerCase().includes(t))}return p.value&&(o=o.filter(t=>t.level===p.value)),m.value&&(o=o.filter(t=>t.category===m.value)),o.sort((t,s)=>new Date(s.timestamp)-new Date(t.timestamp))}),x=v(()=>Math.ceil(d.value.length/c.value)),z=v(()=>{const o=(r.value-1)*c.value,t=o+c.value;return d.value.slice(o,t)}),V=v(()=>{const o=[],t=Math.max(1,r.value-2),s=Math.min(x.value,r.value+2);for(let u=t;u<=s;u++)o.push(u);return o}),C=o=>o.toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"}),E=o=>({info:"bg-blue-100 text-blue-800",warning:"bg-yellow-100 text-yellow-800",error:"bg-red-100 text-red-800",critical:"bg-purple-100 text-purple-800"})[o]||"bg-gray-100 text-gray-800",N=o=>o==="error"||o==="critical"?"bg-red-50":o==="warning"?"bg-yellow-50":"",T=o=>({info:"bg-blue-500",warning:"bg-yellow-500",critical:"bg-red-500"})[o]||"bg-gray-500",y=()=>{r.value=1},U=o=>{alert(`Log Details:

ID: ${o.id}
Message: ${o.message}
Details: ${JSON.stringify(o.details,null,2)}`)},F=()=>{alert("Exporting logs to CSV...")},$=()=>{confirm("Are you sure you want to clear logs older than 30 days? This action cannot be undone.")&&alert("Old logs cleared successfully!")},L=()=>{r.value>1&&r.value--},D=()=>{r.value<x.value&&r.value++},I=o=>{r.value=o};return H(()=>{}),(o,t)=>(l(),O(q,null,{default:J(()=>[e("div",G,[e("div",{class:"flex justify-between items-center"},[t[4]||(t[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"System Logs & Audit Trail"),e("p",{class:"text-gray-600"},"Monitor system activity, user actions, and security events")],-1)),e("div",{class:"flex space-x-3"},[e("button",{onClick:F,class:"bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors"}," Export Logs "),e("button",{onClick:$,class:"bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors"}," Clear Old Logs ")])]),e("div",Q,[t[11]||(t[11]=e("div",{class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},[e("div",{class:"flex items-center"},[e("div",{class:"p-2 bg-green-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])]),e("div",{class:"ml-4"},[e("p",{class:"text-sm font-medium text-gray-600"},"System Status"),e("p",{class:"text-2xl font-bold text-green-600"},"Healthy")])])],-1)),e("div",K,[e("div",X,[t[6]||(t[6]=e("div",{class:"p-2 bg-blue-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("div",Y,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600"},"Active Sessions",-1)),e("p",Z,i(M.value),1)])])]),e("div",ee,[e("div",te,[t[8]||(t[8]=e("div",{class:"p-2 bg-yellow-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),e("div",se,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600"},"Warnings (24h)",-1)),e("p",oe,i(P.value),1)])])]),e("div",ie,[e("div",re,[t[10]||(t[10]=e("div",{class:"p-2 bg-red-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",ae,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600"},"Errors (24h)",-1)),e("p",le,i(j.value),1)])])])]),e("div",ne,[e("div",de,[e("div",ce,[e("div",ue,[h(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>g.value=s),onInput:y,type:"text",placeholder:"Search logs...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,544),[[R,g.value]]),t[12]||(t[12]=e("svg",{class:"absolute left-3 top-2.5 h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])]),e("div",ge,[h(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>p.value=s),onChange:y,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[13]||(t[13]=[e("option",{value:""},"All Levels",-1),e("option",{value:"info"},"Info",-1),e("option",{value:"warning"},"Warning",-1),e("option",{value:"error"},"Error",-1),e("option",{value:"critical"},"Critical",-1)]),544),[[b,p.value]]),h(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>m.value=s),onChange:y,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[14]||(t[14]=[e("option",{value:""},"All Categories",-1),e("option",{value:"auth"},"Authentication",-1),e("option",{value:"booking"},"Booking",-1),e("option",{value:"payment"},"Payment",-1),e("option",{value:"system"},"System",-1),e("option",{value:"security"},"Security",-1)]),544),[[b,m.value]]),h(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>k.value=s),onChange:y,class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},t[15]||(t[15]=[e("option",{value:"1h"},"Last Hour",-1),e("option",{value:"24h"},"Last 24 Hours",-1),e("option",{value:"7d"},"Last 7 Days",-1),e("option",{value:"30d"},"Last 30 Days",-1)]),544),[[b,k.value]])])])]),e("div",pe,[e("div",me,[e("h3",xe,"System Logs ("+i(d.value.length)+")",1)]),S.value?(l(),n("div",ye,t[16]||(t[16]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"},null,-1)]))):d.value.length>0?(l(),n("div",ve,[e("table",he,[t[17]||(t[17]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Timestamp"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Level"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Category"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Message"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"User"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"IP Address"),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",fe,[(l(!0),n(w,null,_(z.value,s=>(l(),n("tr",{key:s.id,class:f([N(s.level),"hover:bg-gray-50"])},[e("td",be,i(C(s.timestamp)),1),e("td",we,[e("span",{class:f([E(s.level),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},i(s.level.toUpperCase()),3)]),e("td",_e,i(s.category),1),e("td",ke,[e("div",Ce,i(s.message),1)]),e("td",Le,i(s.user||"System"),1),e("td",De,i(s.ip_address||"-"),1),e("td",Se,[e("button",{onClick:u=>U(s),class:"text-indigo-600 hover:text-indigo-900"},"View Details",8,Me)])],2))),128))])])])):(l(),n("div",Pe,t[18]||(t[18]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No logs found",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"No logs match your current filters.",-1)]))),d.value.length>c.value?(l(),n("div",je,[e("div",Ae,[e("button",{onClick:L,disabled:r.value===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Previous ",8,Be),e("button",{onClick:D,disabled:r.value===x.value,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Next ",8,ze)]),e("div",Ve,[e("div",null,[e("p",Ee," Showing "+i((r.value-1)*c.value+1)+" to "+i(Math.min(r.value*c.value,d.value.length))+" of "+i(d.value.length)+" results ",1)]),e("div",null,[e("nav",Ne,[e("button",{onClick:L,disabled:r.value===1,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"}," Previous ",8,Te),(l(!0),n(w,null,_(V.value,s=>(l(),n("button",{key:s,onClick:u=>I(s),class:f([s===r.value?"bg-indigo-50 border-indigo-500 text-indigo-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50","relative inline-flex items-center px-4 py-2 border text-sm font-medium"])},i(s),11,Ue))),128)),e("button",{onClick:D,disabled:r.value===x.value,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"}," Next ",8,Fe)])])])])):W("",!0)]),e("div",$e,[t[19]||(t[19]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Recent Security Events")],-1)),e("div",Ie,[e("div",He,[(l(!0),n(w,null,_(B.value,s=>(l(),n("div",{key:s.id,class:"flex items-center justify-between p-4 border border-gray-200 rounded-lg"},[e("div",Oe,[e("div",{class:f([T(s.type),"w-3 h-3 rounded-full mr-3"])},null,2),e("div",null,[e("p",Je,i(s.title),1),e("p",Re,i(s.description),1)])]),e("div",We,[e("p",qe,i(C(s.timestamp)),1),e("p",Ge,i(s.ip_address),1)])]))),128))])])])])]),_:1}))}};export{Ze as default};
