<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ApiSetting;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Crypt;

class ApiSettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = ApiSetting::query();

        // Apply filters
        if ($request->has('service_type')) {
            $query->where('service_type', $request->service_type);
        }

        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'service_type');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $settings = $query->get();

        // Hide sensitive credentials in response
        $settings->each(function($setting) {
            $setting->credentials = array_map(function($value) {
                return is_string($value) && strlen($value) > 4 ?
                    substr($value, 0, 4) . str_repeat('*', strlen($value) - 4) :
                    '****';
            }, $setting->credentials ?? []);
        });

        return response()->json([
            'success' => true,
            'data' => $settings,
            'service_types' => $this->getServiceTypes()
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'service_type' => 'required|in:payment,sms,whatsapp,email,analytics,otpless,webhook',
            'provider' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'credentials' => 'required|array',
            'configuration' => 'nullable|array',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'is_sandbox' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if trying to set as default when another default exists
        if ($request->boolean('is_default')) {
            ApiSetting::where('service_type', $request->service_type)
                     ->where('is_default', true)
                     ->update(['is_default' => false]);
        }

        $data = $request->all();

        // Encrypt sensitive credentials
        $data['credentials'] = $this->encryptCredentials($data['credentials']);

        $setting = ApiSetting::create($data);

        return response()->json([
            'success' => true,
            'message' => 'API setting created successfully',
            'data' => $setting
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ApiSetting $apiSetting)
    {
        // Decrypt credentials for editing
        $apiSetting->credentials = $this->decryptCredentials($apiSetting->credentials);

        return response()->json([
            'success' => true,
            'data' => $apiSetting
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ApiSetting $apiSetting)
    {
        $validator = Validator::make($request->all(), [
            'service_type' => 'required|in:payment,sms,whatsapp,email,analytics,otpless,webhook',
            'provider' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'credentials' => 'required|array',
            'configuration' => 'nullable|array',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'is_sandbox' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if trying to set as default when another default exists
        if ($request->boolean('is_default') && !$apiSetting->is_default) {
            ApiSetting::where('service_type', $request->service_type)
                     ->where('id', '!=', $apiSetting->id)
                     ->where('is_default', true)
                     ->update(['is_default' => false]);
        }

        $data = $request->all();

        // Encrypt sensitive credentials
        $data['credentials'] = $this->encryptCredentials($data['credentials']);

        $apiSetting->update($data);

        return response()->json([
            'success' => true,
            'message' => 'API setting updated successfully',
            'data' => $apiSetting
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ApiSetting $apiSetting)
    {
        // If this is the default, make another one default
        if ($apiSetting->is_default) {
            $nextDefault = ApiSetting::where('service_type', $apiSetting->service_type)
                                   ->where('id', '!=', $apiSetting->id)
                                   ->where('is_active', true)
                                   ->first();

            if ($nextDefault) {
                $nextDefault->update(['is_default' => true]);
            }
        }

        $apiSetting->delete();

        return response()->json([
            'success' => true,
            'message' => 'API setting deleted successfully'
        ]);
    }

    /**
     * Test API connection
     */
    public function test(ApiSetting $apiSetting)
    {
        try {
            $credentials = $this->decryptCredentials($apiSetting->credentials);
            $testResult = $this->testApiConnection($apiSetting->service_type, $apiSetting->provider, $credentials);

            // Update test results
            $apiSetting->update([
                'last_tested_at' => now(),
                'test_results' => $testResult
            ]);

            return response()->json([
                'success' => $testResult['success'],
                'message' => $testResult['message'],
                'data' => $testResult
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available service types and providers
     */
    private function getServiceTypes()
    {
        return [
            'payment' => ['razorpay', 'phonepe', 'upi', 'stripe', 'paypal'],
            'sms' => ['twilio', 'msg91', 'textlocal', 'aws_sns'],
            'whatsapp' => ['gupshup', 'gallabox', 'interakt', 'twilio'],
            'email' => ['smtp', 'mailgun', 'sendgrid', 'aws_ses'],
            'analytics' => ['google_analytics', 'meta_pixel', 'google_tag_manager'],
            'otpless' => ['otpless', 'magic_link', 'firebase_auth'],
            'webhook' => ['zoom', 'calendar', 'social_sharing', 'custom']
        ];
    }

    /**
     * Encrypt sensitive credentials
     */
    private function encryptCredentials(array $credentials)
    {
        $encrypted = [];
        foreach ($credentials as $key => $value) {
            if (is_string($value) && !empty($value)) {
                $encrypted[$key] = Crypt::encryptString($value);
            } else {
                $encrypted[$key] = $value;
            }
        }
        return $encrypted;
    }

    /**
     * Decrypt credentials
     */
    private function decryptCredentials(array $credentials)
    {
        $decrypted = [];
        foreach ($credentials as $key => $value) {
            if (is_string($value) && !empty($value)) {
                try {
                    $decrypted[$key] = Crypt::decryptString($value);
                } catch (\Exception $e) {
                    $decrypted[$key] = $value; // Return as-is if decryption fails
                }
            } else {
                $decrypted[$key] = $value;
            }
        }
        return $decrypted;
    }

    /**
     * Test API connection based on service type
     */
    private function testApiConnection($serviceType, $provider, $credentials)
    {
        // This is a placeholder - implement actual API testing logic
        switch ($serviceType) {
            case 'payment':
                return $this->testPaymentApi($provider, $credentials);
            case 'sms':
                return $this->testSmsApi($provider, $credentials);
            case 'whatsapp':
                return $this->testWhatsAppApi($provider, $credentials);
            case 'email':
                return $this->testEmailApi($provider, $credentials);
            default:
                return [
                    'success' => true,
                    'message' => 'Test not implemented for this service type',
                    'tested_at' => now()
                ];
        }
    }

    /**
     * Test payment API
     */
    private function testPaymentApi($provider, $credentials)
    {
        // Implement actual payment API testing
        return [
            'success' => true,
            'message' => 'Payment API connection successful',
            'provider' => $provider,
            'tested_at' => now()
        ];
    }

    /**
     * Test SMS API
     */
    private function testSmsApi($provider, $credentials)
    {
        // Implement actual SMS API testing
        return [
            'success' => true,
            'message' => 'SMS API connection successful',
            'provider' => $provider,
            'tested_at' => now()
        ];
    }

    /**
     * Test WhatsApp API
     */
    private function testWhatsAppApi($provider, $credentials)
    {
        // Implement actual WhatsApp API testing
        return [
            'success' => true,
            'message' => 'WhatsApp API connection successful',
            'provider' => $provider,
            'tested_at' => now()
        ];
    }

    /**
     * Test Email API
     */
    private function testEmailApi($provider, $credentials)
    {
        // Implement actual Email API testing
        return [
            'success' => true,
            'message' => 'Email API connection successful',
            'provider' => $provider,
            'tested_at' => now()
        ];
    }
}
