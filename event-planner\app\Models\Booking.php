<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Booking extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'booking_number',
        'event_id',
        'user_id',
        'status',
        'total_tickets',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'currency',
        'customer_info',
        'payment_status',
        'payment_method',
        'payment_reference',
        'payment_date',
        'notes',
        'metadata',
        'checked_in_at',
        'qr_code',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'customer_info' => 'array',
        'metadata' => 'array',
        'payment_date' => 'datetime',
        'checked_in_at' => 'datetime',
    ];

    /**
     * Boot method to auto-generate booking number
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($booking) {
            if (empty($booking->booking_number)) {
                $booking->booking_number = 'BK' . date('Ymd') . str_pad(static::count() + 1, 4, '0', STR_PAD_LEFT);
            }
        });
    }

    /**
     * Relationships
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function bookingItems()
    {
        return $this->hasMany(BookingItem::class);
    }

    public function tickets()
    {
        return $this->hasMany(Ticket::class);
    }

    /**
     * Scopes
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Accessors
     */
    public function getTotalTicketsAttribute()
    {
        return $this->bookingItems()->sum('quantity');
    }

    public function getIsConfirmedAttribute()
    {
        return $this->status === 'confirmed';
    }

    public function getIsPaidAttribute()
    {
        return $this->payment_status === 'paid';
    }
}
