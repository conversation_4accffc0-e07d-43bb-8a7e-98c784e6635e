<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('short_description')->nullable();
            $table->foreignId('category_id')->constrained('event_categories')->onDelete('cascade');
            $table->string('venue_name');
            $table->text('venue_address');
            $table->decimal('venue_latitude', 10, 8)->nullable();
            $table->decimal('venue_longitude', 11, 8)->nullable();
            $table->datetime('start_date');
            $table->datetime('end_date');
            $table->datetime('booking_start_date');
            $table->datetime('booking_end_date');
            $table->string('featured_image')->nullable();
            $table->json('gallery_images')->nullable();
            $table->integer('max_capacity')->default(0);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_published')->default(false);
            $table->enum('status', ['draft', 'published', 'cancelled', 'completed'])->default('draft');
            $table->json('seo_meta')->nullable(); // For SEO metadata
            $table->json('custom_fields')->nullable(); // For additional event data
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['status', 'is_published']);
            $table->index(['start_date', 'end_date']);
            $table->index(['booking_start_date', 'booking_end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
