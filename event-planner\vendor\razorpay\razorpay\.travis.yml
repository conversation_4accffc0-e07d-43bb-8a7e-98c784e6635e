dist: precise
language: php
php:
- 7.3
- 7.4
- 8.0
- 8.1

install: composer install
before_script:
  - cp phpunit.xml.dist phpunit.xml
  # These two are required in the build step for non-composer-tests
  - mkdir -p libs
  - cd libs &&  wget https://github.com/rmccue/Requests/archive/v2.0.4.zip -O requests.zip && unzip requests.zip && rm requests.zip && cd ..
  - rm -rf libs/Requests-2.0.4/examples libs/Requests-2.0.4/docs libs/Requests-2.0.4/bin libs/Requests-2.0.4/tests
script:
  # Run a syntax validation check on all PHP files
  -  find . -path ./vendor -prune -o -iname '*.php' |xargs -n1 php -l
  - ./vendor/bin/phpunit

notifications:
  slack:
    secure: fLT1x7BCXi8+sP1Qk1lP74+60JIBCw2clUTSOmB0OuoQGWYIJ4qelKcGH5FFsADGuC1GX2pf0fKRiLdavVrGpBkD4MGFPpyYKPYb0S/FyArN3PjdaNvAqE1VgQCtKkbugP5bHH9bp631+lo2EGQVLWTjlwiijWbCEyDu3L0YVMY=
# We are doing the releases for non-composer folks
# So this includes the vendor directory
before_deploy:
  - echo $TRAVIS_TAG > version.txt
  - cat release.txt |zip -r@ "razorpay-php.zip"
deploy:
  provider: releases
  # Otherwise, we lose the vendor/ directory
  skip_cleanup: true
  api_key:
    secure: bHcu1jUASH6aVSD1LmzXdjQC4hc0o8EBbVs9X8e5j+/OC7+UuBBRu+jh6gQje/XWu9Nj1W2LkWhv0IKX1tJbcs0uRstggx+xC0ZayRzkscsqErqeM4WeyJjxe5ewb2eeGujtl9+WWFB3wpUQJtxXaaPuGYtroYGGYuI23wzKN4A=
  # travis doesn't support multi file deployes yet, not that we need them
  file: razorpay-php.zip
  on:
    # Only do the release for one build every tag
    php: 7.3
    # GitHub refuses to accept releases that are not tagged
    tags: true
    # Allow builds for non-master branches as well
    all_branches: true
    # Only do the releases if the repo is not a fork
    repo: razorpay/razorpay-php
