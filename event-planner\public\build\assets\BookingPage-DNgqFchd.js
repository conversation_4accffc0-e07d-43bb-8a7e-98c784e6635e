import{r as V,B as z,c as b,s as Z,o as G,a as J,u as W,f as r,h as e,p as k,w,m as p,e as X,t as i,i as y,n as S,C as L,F,k as E,x as d,z as ee,y as m,A as te,I as f,j as se,g as a,K as oe}from"./vue-vendor-BupLktX_.js";import{u as ne}from"./admin-9F2yeZU0.js";import{u as ie}from"./booking-fYtx-gxu.js";import"./api-D9eN0dN9.js";import"./chart-vendor-Db3utXXw.js";import"./utils-vendor-Dq7h7Pqt.js";const le={class:"min-h-screen bg-gray-50"},re={key:0,class:"flex justify-center items-center min-h-screen"},de={key:1,class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},ae={class:"mb-8"},ue={class:"mb-4"},me={class:"flex items-center space-x-2 text-sm text-gray-500"},ge={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},ce={class:"lg:col-span-2"},pe={class:"mb-8"},ye={class:"flex items-center"},xe={class:"flex items-center text-sm"},be={class:"flex items-center text-sm"},fe={class:"flex items-center text-sm"},ve={key:0,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},_e={class:"mb-6"},ke={class:"space-y-3"},we={class:"flex items-center cursor-pointer"},he=["value"],qe={class:"ml-3 flex-1"},Ce={class:"flex justify-between items-start"},Ve={class:"font-medium text-gray-900"},Se={key:0,class:"text-sm text-gray-600"},Be={class:"text-sm text-gray-500 mt-1"},Pe={class:"text-lg font-semibold text-gray-900"},je={class:"mb-6"},Ue=["value"],Ne={class:"mb-6"},Te={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ae={class:"mb-6"},De={class:"mb-6"},Me={class:"flex items-start"},ze={class:"flex justify-between"},Le=["disabled"],Fe={key:1,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},Ee={key:0,class:"text-center py-8"},Ie={class:"mb-6"},Re={class:"grid grid-cols-2 gap-4 mb-6"},$e={class:"mb-6"},Ye={class:"mb-6"},Oe={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},He={class:"md:col-span-2"},Ke={key:0,class:"mb-6 bg-red-50 border border-red-200 rounded-md p-4"},Qe={class:"flex"},Ze={class:"ml-3"},Ge={class:"text-sm text-red-800"},Je={class:"flex justify-between"},We=["disabled"],Xe={key:2,class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6"},et={class:"text-center py-8"},tt={class:"bg-gray-50 rounded-lg p-4 mb-6"},st={class:"text-lg font-mono font-semibold text-gray-900"},ot={class:"flex flex-col sm:flex-row gap-4 justify-center"},nt={class:"lg:col-span-1"},it={class:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8"},lt={key:0,class:"mb-6"},rt={class:"flex"},dt={class:"w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-4"},at={class:"text-white text-xl font-bold"},ut={class:"flex-1"},mt={class:"font-medium text-gray-900"},gt={class:"text-sm text-gray-600"},ct={class:"text-sm text-gray-600"},pt={key:1,class:"mb-6 p-3 bg-gray-50 rounded-lg"},yt={class:"font-medium text-gray-900"},xt={class:"text-sm text-gray-600"},bt={class:"space-y-2 mb-6"},ft={class:"flex justify-between text-sm"},vt={class:"text-gray-900"},_t={class:"flex justify-between text-sm"},kt={class:"text-gray-900"},wt={class:"border-t border-gray-200 pt-2"},ht={class:"flex justify-between font-semibold"},qt={class:"text-gray-900"},Nt={__name:"BookingPage",setup(Ct){const B=W(),I=J(),h=ne(),c=ie(),u=V(null),P=V(!0),g=V(1),o=z({ticket_type_id:null,quantity:1,attendee_name:"",attendee_email:"",attendee_phone:"",organization:"",special_requirements:"",terms_accepted:!1}),l=z({cardNumber:"",expiryDate:"",cvv:"",cardholderName:"",billingAddress:"",city:"",postalCode:""}),j=b(()=>{var n;return((n=u.value)==null?void 0:n.ticket_types)||[]}),v=b(()=>j.value.find(n=>n.id===o.ticket_type_id)),q=b(()=>v.value?v.value.price*o.quantity:0),U=b(()=>Math.round(q.value*.05)),C=b(()=>q.value+U.value),R=b(()=>o.ticket_type_id&&o.attendee_name&&o.attendee_email&&o.terms_accepted),$=n=>new Date(n).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),x=n=>n===0?"Free":`₹${n.toLocaleString()}`,Y=async()=>{try{const n=B.params.eventId;(n==="1"||n==="tech-conference-2025")&&(u.value={id:1,title:"Tech Conference 2025",slug:"tech-conference-2025",start_date:"2025-02-15T09:00:00Z",venue_name:"Convention Center",ticket_types:[{id:1,name:"Early Bird",description:"Limited time offer with full access",price:1999,quantity_remaining:50},{id:2,name:"Regular",description:"Standard conference access",price:2500,quantity_remaining:200},{id:3,name:"VIP",description:"Premium access with networking dinner",price:4999,quantity_remaining:25}]})}catch(n){console.error("Failed to load event:",n)}finally{P.value=!1}},O=async()=>{const n={event_id:u.value.id,ticket_type_id:o.ticket_type_id,quantity:o.quantity,attendee_name:o.attendee_name,attendee_email:o.attendee_email,attendee_phone:o.attendee_phone,organization:o.organization,special_requirements:o.special_requirements,total_amount:C.value};(await c.createBooking(n)).success&&(g.value=2)},H=async()=>{(await c.simulatePayment(l)).success&&(g.value=3)},K=()=>{alert("Ticket download started! Check your downloads folder.")};return Z(()=>h.isAuthenticated,n=>{n&&(o.attendee_name=h.userName,o.attendee_email=h.userEmail)},{immediate:!0}),G(()=>{if(!h.isAuthenticated){I.push(`/login?redirect=${B.fullPath}`);return}Y()}),(n,t)=>{var N,T,A,D,M;const _=X("router-link");return a(),r("div",le,[P.value?(a(),r("div",re,t[16]||(t[16]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"},null,-1)]))):(a(),r("div",de,[e("div",ae,[e("nav",ue,[e("ol",me,[e("li",null,[k(_,{to:"/",class:"hover:text-indigo-600"},{default:w(()=>t[17]||(t[17]=[p("Home")])),_:1,__:[17]})]),t[19]||(t[19]=e("li",null,[e("span",{class:"mx-2"},"/")],-1)),e("li",null,[k(_,{to:"/events",class:"hover:text-indigo-600"},{default:w(()=>t[18]||(t[18]=[p("Events")])),_:1,__:[18]})]),t[20]||(t[20]=e("li",null,[e("span",{class:"mx-2"},"/")],-1)),e("li",null,[k(_,{to:`/events/${((N=u.value)==null?void 0:N.slug)||((T=u.value)==null?void 0:T.id)}`,class:"hover:text-indigo-600"},{default:w(()=>{var s;return[p(i((s=u.value)==null?void 0:s.title),1)]}),_:1},8,["to"])]),t[21]||(t[21]=e("li",null,[e("span",{class:"mx-2"},"/")],-1)),t[22]||(t[22]=e("li",{class:"text-indigo-600"},"Booking",-1))])]),t[23]||(t[23]=e("h1",{class:"text-3xl font-bold text-gray-900"},"Complete Your Booking",-1))]),e("div",ge,[e("div",ce,[e("div",pe,[e("div",ye,[e("div",xe,[e("div",{class:S([g.value>=1?"bg-indigo-600 text-white":"bg-gray-200 text-gray-600","w-8 h-8 rounded-full flex items-center justify-center font-medium"])}," 1 ",2),t[24]||(t[24]=e("span",{class:"ml-2 text-gray-600"},"Details",-1))]),t[27]||(t[27]=e("div",{class:"flex-1 h-px bg-gray-200 mx-4"},null,-1)),e("div",be,[e("div",{class:S([g.value>=2?"bg-indigo-600 text-white":"bg-gray-200 text-gray-600","w-8 h-8 rounded-full flex items-center justify-center font-medium"])}," 2 ",2),t[25]||(t[25]=e("span",{class:"ml-2 text-gray-600"},"Payment",-1))]),t[28]||(t[28]=e("div",{class:"flex-1 h-px bg-gray-200 mx-4"},null,-1)),e("div",fe,[e("div",{class:S([g.value>=3?"bg-indigo-600 text-white":"bg-gray-200 text-gray-600","w-8 h-8 rounded-full flex items-center justify-center font-medium"])}," 3 ",2),t[26]||(t[26]=e("span",{class:"ml-2 text-gray-600"},"Confirmation",-1))])])]),g.value===1?(a(),r("div",ve,[t[39]||(t[39]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Booking Details",-1)),e("form",{onSubmit:L(O,["prevent"])},[e("div",_e,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 mb-3"},"Select Ticket Type",-1)),e("div",ke,[(a(!0),r(F,null,E(j.value,s=>(a(),r("div",{key:s.id,class:"border border-gray-200 rounded-lg p-4 hover:border-indigo-300 transition-colors"},[e("label",we,[d(e("input",{"onUpdate:modelValue":t[0]||(t[0]=Q=>o.ticket_type_id=Q),value:s.id,type:"radio",class:"text-indigo-600 focus:ring-indigo-500"},null,8,he),[[oe,o.ticket_type_id]]),e("div",qe,[e("div",Ce,[e("div",null,[e("div",Ve,i(s.name),1),s.description?(a(),r("div",Se,i(s.description),1)):y("",!0),e("div",Be,i(s.quantity_remaining||"Available")+" tickets remaining ",1)]),e("div",Pe,i(x(s.price)),1)])])])]))),128))])]),e("div",je,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Quantity",-1)),d(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>o.quantity=s),class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},[(a(),r(F,null,E(10,s=>e("option",{key:s,value:s},i(s)+" "+i(s===1?"ticket":"tickets"),9,Ue)),64))],512),[[ee,o.quantity]])]),e("div",Ne,[t[35]||(t[35]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Attendee Information",-1)),e("div",Te,[e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Full Name *",-1)),d(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>o.attendee_name=s),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[m,o.attendee_name]])]),e("div",null,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Email *",-1)),d(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>o.attendee_email=s),type:"email",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[m,o.attendee_email]])]),e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Phone",-1)),d(e("input",{"onUpdate:modelValue":t[4]||(t[4]=s=>o.attendee_phone=s),type:"tel",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[m,o.attendee_phone]])]),e("div",null,[t[34]||(t[34]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Organization (Optional)",-1)),d(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>o.organization=s),type:"text",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[m,o.organization]])])])]),e("div",Ae,[t[36]||(t[36]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Special Requirements (Optional)",-1)),d(e("textarea",{"onUpdate:modelValue":t[6]||(t[6]=s=>o.special_requirements=s),rows:"3",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500",placeholder:"Any dietary restrictions, accessibility needs, or other requirements..."},null,512),[[m,o.special_requirements]])]),e("div",De,[e("label",Me,[d(e("input",{"onUpdate:modelValue":t[7]||(t[7]=s=>o.terms_accepted=s),type:"checkbox",required:"",class:"mt-1 text-indigo-600 focus:ring-indigo-500"},null,512),[[te,o.terms_accepted]]),t[37]||(t[37]=e("span",{class:"ml-2 text-sm text-gray-600"},[p(" I agree to the "),e("a",{href:"#",class:"text-indigo-600 hover:text-indigo-500"},"Terms and Conditions"),p(" and "),e("a",{href:"#",class:"text-indigo-600 hover:text-indigo-500"},"Privacy Policy")],-1))])]),e("div",ze,[k(_,{to:`/events/${((A=u.value)==null?void 0:A.slug)||((D=u.value)==null?void 0:D.id)}`,class:"bg-gray-200 text-gray-700 px-6 py-2 rounded-md font-medium hover:bg-gray-300 transition-colors"},{default:w(()=>t[38]||(t[38]=[p(" Back to Event ")])),_:1,__:[38]},8,["to"]),e("button",{type:"submit",disabled:!R.value,class:"bg-indigo-600 text-white px-6 py-2 rounded-md font-medium hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"}," Continue to Payment ",8,Le)])],32)])):y("",!0),g.value===2?(a(),r("div",Fe,[t[51]||(t[51]=e("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Payment Information",-1)),f(c).paymentStatus==="processing"?(a(),r("div",Ee,t[40]||(t[40]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"},null,-1),e("p",{class:"text-gray-600"},"Processing your payment...",-1)]))):(a(),r("form",{key:1,onSubmit:L(H,["prevent"])},[e("div",Ie,[t[41]||(t[41]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Card Number *",-1)),d(e("input",{"onUpdate:modelValue":t[8]||(t[8]=s=>l.cardNumber=s),type:"text",required:"",placeholder:"1234 5678 9012 3456",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[m,l.cardNumber]]),t[42]||(t[42]=e("p",{class:"text-xs text-gray-500 mt-1"},"Use 4000 0000 0000 0002 to simulate payment failure",-1))]),e("div",Re,[e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Expiry Date *",-1)),d(e("input",{"onUpdate:modelValue":t[9]||(t[9]=s=>l.expiryDate=s),type:"text",required:"",placeholder:"MM/YY",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[m,l.expiryDate]])]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"CVV *",-1)),d(e("input",{"onUpdate:modelValue":t[10]||(t[10]=s=>l.cvv=s),type:"text",required:"",placeholder:"123",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[m,l.cvv]])])]),e("div",$e,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Cardholder Name *",-1)),d(e("input",{"onUpdate:modelValue":t[11]||(t[11]=s=>l.cardholderName=s),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[m,l.cardholderName]])]),e("div",Ye,[t[49]||(t[49]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Billing Address",-1)),e("div",Oe,[e("div",He,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Address *",-1)),d(e("input",{"onUpdate:modelValue":t[12]||(t[12]=s=>l.billingAddress=s),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[m,l.billingAddress]])]),e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"City *",-1)),d(e("input",{"onUpdate:modelValue":t[13]||(t[13]=s=>l.city=s),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[m,l.city]])]),e("div",null,[t[48]||(t[48]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Postal Code *",-1)),d(e("input",{"onUpdate:modelValue":t[14]||(t[14]=s=>l.postalCode=s),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[m,l.postalCode]])])])]),f(c).error?(a(),r("div",Ke,[e("div",Qe,[t[50]||(t[50]=e("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("div",Ze,[e("p",Ge,i(f(c).error),1)])])])):y("",!0),e("div",Je,[e("button",{type:"button",onClick:t[15]||(t[15]=s=>g.value=1),class:"bg-gray-200 text-gray-700 px-6 py-2 rounded-md font-medium hover:bg-gray-300 transition-colors"}," Back "),e("button",{type:"submit",disabled:f(c).isLoading,class:"bg-indigo-600 text-white px-6 py-2 rounded-md font-medium hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"},i(f(c).isLoading?"Processing...":`Pay ${x(C.value)}`),9,We)])],32))])):y("",!0),g.value===3?(a(),r("div",Xe,[e("div",et,[t[54]||(t[54]=se('<div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"><svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg></div><h2 class="text-2xl font-bold text-gray-900 mb-2">Booking Confirmed!</h2><p class="text-gray-600 mb-6">Your booking has been successfully processed.</p>',3)),e("div",tt,[t[52]||(t[52]=e("div",{class:"text-sm text-gray-600 mb-2"},"Booking Reference",-1)),e("div",st,i((M=f(c).currentBooking)==null?void 0:M.booking_reference),1)]),e("div",ot,[e("button",{onClick:K,class:"bg-indigo-600 text-white px-6 py-2 rounded-md font-medium hover:bg-indigo-700 transition-colors"}," Download Ticket "),k(_,{to:"/my-tickets",class:"bg-gray-200 text-gray-700 px-6 py-2 rounded-md font-medium hover:bg-gray-300 transition-colors"},{default:w(()=>t[53]||(t[53]=[p(" View My Tickets ")])),_:1,__:[53]})])])])):y("",!0)]),e("div",nt,[e("div",it,[t[58]||(t[58]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Booking Summary",-1)),u.value?(a(),r("div",lt,[e("div",rt,[e("div",dt,[e("span",at,i(u.value.title.charAt(0)),1)]),e("div",ut,[e("h4",mt,i(u.value.title),1),e("p",gt,i($(u.value.start_date)),1),e("p",ct,i(u.value.venue_name),1)])])])):y("",!0),v.value?(a(),r("div",pt,[e("div",yt,i(v.value.name),1),e("div",xt,i(x(v.value.price))+" × "+i(o.quantity),1)])):y("",!0),e("div",bt,[e("div",ft,[t[55]||(t[55]=e("span",{class:"text-gray-600"},"Subtotal",-1)),e("span",vt,i(x(q.value)),1)]),e("div",_t,[t[56]||(t[56]=e("span",{class:"text-gray-600"},"Service Fee",-1)),e("span",kt,i(x(U.value)),1)]),e("div",wt,[e("div",ht,[t[57]||(t[57]=e("span",{class:"text-gray-900"},"Total",-1)),e("span",qt,i(x(C.value)),1)])])]),t[59]||(t[59]=e("div",{class:"text-xs text-gray-500 text-center"},[e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})]),p(" Secure payment powered by SSL encryption ")],-1))])])])]))])}}};export{Nt as default};
