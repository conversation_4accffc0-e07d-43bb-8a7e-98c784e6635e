<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'short_description',
        'category_id',
        'venue_name',
        'venue_address',
        'venue_latitude',
        'venue_longitude',
        'start_date',
        'end_date',
        'booking_start_date',
        'booking_end_date',
        'featured_image',
        'gallery_images',
        'max_capacity',
        'is_featured',
        'is_published',
        'status',
        'seo_meta',
        'custom_fields',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'booking_start_date' => 'datetime',
        'booking_end_date' => 'datetime',
        'gallery_images' => 'array',
        'seo_meta' => 'array',
        'custom_fields' => 'array',
        'is_featured' => 'boolean',
        'is_published' => 'boolean',
        'venue_latitude' => 'decimal:8',
        'venue_longitude' => 'decimal:8',
    ];

    /**
     * Boot method to auto-generate slug
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($event) {
            if (empty($event->slug)) {
                $event->slug = Str::slug($event->title);
            }
        });
    }

    /**
     * Relationships
     */
    public function category()
    {
        return $this->belongsTo(EventCategory::class, 'category_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function ticketTypes()
    {
        return $this->hasMany(TicketType::class);
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    public function tickets()
    {
        return $this->hasMany(Ticket::class);
    }

    /**
     * Scopes
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true)->where('status', 'published');
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>', now());
    }

    public function scopeBookingOpen($query)
    {
        return $query->where('booking_start_date', '<=', now())
                    ->where('booking_end_date', '>=', now());
    }

    /**
     * Accessors & Mutators
     */
    public function getAvailableTicketsAttribute()
    {
        return $this->max_capacity - $this->tickets()->where('status', 'active')->count();
    }

    public function getTotalBookingsAttribute()
    {
        return $this->bookings()->where('status', 'confirmed')->count();
    }

    public function getTotalRevenueAttribute()
    {
        return $this->bookings()
                   ->where('status', 'confirmed')
                   ->where('payment_status', 'paid')
                   ->sum('final_amount');
    }
}
