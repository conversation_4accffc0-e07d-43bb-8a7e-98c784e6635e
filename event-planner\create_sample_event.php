<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Event;
use App\Models\EventCategory;
use App\Models\TicketType;
use App\Models\User;

// Get the first category and user
$category = EventCategory::first();
$user = User::first();

if (!$category || !$user) {
    echo "Please ensure categories and users exist first.\n";
    exit;
}

// Create a sample event
$event = Event::create([
    'title' => 'Liquid N Lights Interactive Art Experience',
    'slug' => 'liquid-n-lights-interactive-art-experience',
    'description' => 'Experience our signature interactive art installation that combines the nostalgia of Lite Brite with modern Instagram culture. Each glowing bulb is filled with drinks for guest interaction, creating a unique and memorable experience.',
    'short_description' => 'Interactive art installation with glowing drink-filled bulbs',
    'category_id' => $category->id,
    'venue_name' => 'Downtown Art Gallery',
    'venue_address' => '123 Art Street, Downtown, City 12345',
    'venue_latitude' => 40.7128,
    'venue_longitude' => -74.0060,
    'start_date' => '2025-08-15 18:00:00',
    'end_date' => '2025-08-15 23:00:00',
    'booking_start_date' => '2025-07-15 00:00:00',
    'booking_end_date' => '2025-08-14 23:59:59',
    'max_capacity' => 200,
    'is_featured' => true,
    'is_published' => true,
    'status' => 'published',
    'seo_meta' => [
        'title' => 'Liquid N Lights Art Experience - Interactive Installation',
        'description' => 'Join us for an unforgettable interactive art experience combining nostalgia with modern culture',
        'keywords' => 'art, interactive, installation, drinks, experience'
    ],
    'created_by' => $user->id,
]);

echo "Event created with ID: {$event->id}\n";

// Create ticket types for the event
$ticketTypes = [
    [
        'name' => 'Early Bird',
        'description' => 'Limited early bird tickets with special pricing',
        'price' => 2500.00,
        'quantity_available' => 50,
        'min_quantity_per_order' => 1,
        'max_quantity_per_order' => 4,
        'sale_start_date' => '2025-07-15 00:00:00',
        'sale_end_date' => '2025-07-31 23:59:59',
        'is_active' => true,
        'benefits' => ['Early access', 'Welcome drink', 'Exclusive photo session'],
        'sort_order' => 1,
    ],
    [
        'name' => 'General Admission',
        'description' => 'Standard admission to the interactive art experience',
        'price' => 3500.00,
        'quantity_available' => 120,
        'min_quantity_per_order' => 1,
        'max_quantity_per_order' => 6,
        'sale_start_date' => '2025-07-15 00:00:00',
        'sale_end_date' => '2025-08-14 23:59:59',
        'is_active' => true,
        'benefits' => ['Full access to installation', 'Complimentary drink'],
        'sort_order' => 2,
    ],
    [
        'name' => 'VIP Experience',
        'description' => 'Premium experience with exclusive perks',
        'price' => 5500.00,
        'quantity_available' => 30,
        'min_quantity_per_order' => 1,
        'max_quantity_per_order' => 2,
        'sale_start_date' => '2025-07-15 00:00:00',
        'sale_end_date' => '2025-08-14 23:59:59',
        'is_active' => true,
        'benefits' => ['Priority access', 'Premium drinks', 'Artist meet & greet', 'Exclusive merchandise'],
        'sort_order' => 3,
    ],
];

foreach ($ticketTypes as $ticketData) {
    $ticketData['event_id'] = $event->id;
    $ticket = TicketType::create($ticketData);
    echo "Ticket type created: {$ticket->name} (ID: {$ticket->id})\n";
}

echo "\nSample event and ticket types created successfully!\n";
echo "Event: {$event->title}\n";
echo "Categories: " . $event->ticketTypes->count() . " ticket types\n";
