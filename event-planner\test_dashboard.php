<?php

// Test script for dashboard APIs
$baseUrl = 'http://localhost:8000/api/admin/dashboard';
$token = '1|ra6M9U4sDIYT2PEgsHsW5BnViychQNPq4UiwRu0g2542cfa4';

function makeRequest($url, $token) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $token
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'response' => json_decode($response, true)
    ];
}

echo "=== Testing Dashboard APIs ===\n\n";

// Test 1: Dashboard Overview
echo "1. Testing Dashboard Overview:\n";
$result = makeRequest($baseUrl . '/overview', $token);
echo "HTTP Code: {$result['code']}\n";
if ($result['response']['success']) {
    $stats = $result['response']['data'];
    echo "✓ Dashboard overview retrieved:\n";
    echo "  Events: {$stats['events']['total']} total, {$stats['events']['published']} published, {$stats['events']['upcoming']} upcoming\n";
    echo "  Bookings: {$stats['bookings']['total']} total, {$stats['bookings']['confirmed']} confirmed, ₹{$stats['bookings']['revenue']} revenue\n";
    echo "  Tickets: {$stats['tickets']['total']} total, {$stats['tickets']['active']} active, {$stats['tickets']['used']} used\n";
    echo "  Users: {$stats['users']['total']} total, {$stats['users']['active']} active, {$stats['users']['admins']} admins\n";
    echo "  Content: {$stats['content']['categories']} categories, {$stats['content']['cms_pages']} published pages\n";
} else {
    echo "✗ Failed to get dashboard overview\n";
}
echo "\n";

// Test 2: Recent Activities
echo "2. Testing Recent Activities:\n";
$result = makeRequest($baseUrl . '/activities', $token);
echo "HTTP Code: {$result['code']}\n";
if ($result['response']['success']) {
    $activities = $result['response']['data'];
    echo "✓ Recent activities retrieved: " . count($activities) . " activities\n";
    foreach (array_slice($activities, 0, 3) as $activity) {
        echo "  - {$activity['title']} by {$activity['user']}\n";
    }
} else {
    echo "✗ Failed to get recent activities\n";
}
echo "\n";

// Test 3: Upcoming Events
echo "3. Testing Upcoming Events:\n";
$result = makeRequest($baseUrl . '/upcoming-events', $token);
echo "HTTP Code: {$result['code']}\n";
if ($result['response']['success']) {
    $events = $result['response']['data'];
    echo "✓ Upcoming events retrieved: " . count($events) . " events\n";
    foreach ($events as $event) {
        echo "  - {$event['title']} at {$event['venue']} on {$event['start_date']}\n";
        echo "    Tickets sold: {$event['tickets_sold']}/{$event['capacity']}\n";
    }
} else {
    echo "✗ Failed to get upcoming events\n";
}
echo "\n";

// Test 4: Sales Analytics
echo "4. Testing Sales Analytics:\n";
$result = makeRequest($baseUrl . '/analytics?period=30', $token);
echo "HTTP Code: {$result['code']}\n";
if ($result['response']['success']) {
    $analytics = $result['response']['data'];
    echo "✓ Sales analytics retrieved for {$analytics['period']} days:\n";
    echo "  - Daily sales data points: " . count($analytics['daily_sales']) . "\n";
    echo "  - Event performance data: " . count($analytics['event_performance']) . " events\n";
    echo "  - Category performance data: " . count($analytics['category_performance']) . " categories\n";
} else {
    echo "✗ Failed to get sales analytics\n";
}
echo "\n";

echo "=== Dashboard Test Complete ===\n";
