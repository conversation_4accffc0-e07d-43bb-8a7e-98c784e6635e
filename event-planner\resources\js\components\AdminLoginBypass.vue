<template>
  <div v-if="!isAuthenticated" class="fixed top-4 right-4 z-50">
    <div class="bg-white border border-gray-300 rounded-lg shadow-lg p-4">
      <h3 class="text-sm font-medium text-gray-900 mb-2">Demo Access</h3>
      <p class="text-xs text-gray-600 mb-3">Click to access admin panel</p>
      <button @click="loginAsAdmin" 
              class="w-full bg-indigo-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-indigo-700">
        Login as Admin
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const isAuthenticated = computed(() => authStore.isAuthenticated)

const loginAsAdmin = () => {
  const adminUser = {
    id: 1,
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin'
  }
  const authToken = 'admin-demo-token-12345'
  
  authStore.user = adminUser
  authStore.token = authToken
  
  localStorage.setItem('auth_token', authToken)
  localStorage.setItem('user', JSON.stringify(adminUser))
  
  // Refresh page to update UI
  window.location.reload()
}
</script>
