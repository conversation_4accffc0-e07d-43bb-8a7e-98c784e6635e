<template>
  <AdminLayout>
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Media Library</h1>
          <p class="text-gray-600">Manage event images, documents, and media files</p>
        </div>
        <div class="flex space-x-3">
          <button @click="showUploadModal = true" 
                  class="bg-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-indigo-700 transition-colors">
            Upload Files
          </button>
          <button @click="createFolder" 
                  class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors">
            New Folder
          </button>
        </div>
      </div>

      <!-- Storage Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Files</p>
              <p class="text-2xl font-bold text-gray-900">{{ totalFiles }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Images</p>
              <p class="text-2xl font-bold text-gray-900">{{ imageFiles }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Documents</p>
              <p class="text-2xl font-bold text-gray-900">{{ documentFiles }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
              <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Storage Used</p>
              <p class="text-2xl font-bold text-gray-900">{{ storageUsed }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- File Browser -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <!-- Toolbar -->
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
              <!-- Breadcrumb -->
              <nav class="flex" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                  <li>
                    <button @click="navigateToFolder('/')" class="text-gray-500 hover:text-gray-700">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                      </svg>
                    </button>
                  </li>
                  <li v-for="(folder, index) in breadcrumbs" :key="index" class="flex items-center">
                    <svg class="w-5 h-5 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <button @click="navigateToFolder(folder.path)" 
                            class="text-sm font-medium text-gray-500 hover:text-gray-700">
                      {{ folder.name }}
                    </button>
                  </li>
                </ol>
              </nav>
            </div>

            <div class="flex items-center space-x-4">
              <!-- View Toggle -->
              <div class="flex border border-gray-300 rounded-md">
                <button @click="viewMode = 'grid'" 
                        :class="viewMode === 'grid' ? 'bg-gray-100' : ''"
                        class="p-2 text-gray-500 hover:text-gray-700">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                  </svg>
                </button>
                <button @click="viewMode = 'list'" 
                        :class="viewMode === 'list' ? 'bg-gray-100' : ''"
                        class="p-2 text-gray-500 hover:text-gray-700">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                  </svg>
                </button>
              </div>

              <!-- Search -->
              <div class="relative">
                <input v-model="searchQuery" 
                       type="text" 
                       placeholder="Search files..."
                       class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- File Grid View -->
        <div v-if="viewMode === 'grid'" class="p-6">
          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <!-- Folders -->
            <div v-for="folder in filteredFolders" :key="folder.id" 
                 @click="navigateToFolder(folder.path)"
                 class="group cursor-pointer p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
              <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-blue-500 group-hover:text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"/>
                </svg>
                <p class="mt-2 text-sm font-medium text-gray-900 truncate">{{ folder.name }}</p>
                <p class="text-xs text-gray-500">{{ folder.items }} items</p>
              </div>
            </div>

            <!-- Files -->
            <div v-for="file in filteredFiles" :key="file.id" 
                 @click="selectFile(file)"
                 :class="selectedFiles.includes(file.id) ? 'ring-2 ring-indigo-500' : ''"
                 class="group cursor-pointer p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
              <div class="text-center">
                <!-- Image Preview -->
                <div v-if="file.type === 'image'" class="mx-auto h-12 w-12 bg-gray-100 rounded overflow-hidden">
                  <img :src="file.thumbnail" :alt="file.name" class="w-full h-full object-cover">
                </div>
                <!-- File Icon -->
                <svg v-else class="mx-auto h-12 w-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                </svg>
                <p class="mt-2 text-sm font-medium text-gray-900 truncate">{{ file.name }}</p>
                <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- File List View -->
        <div v-else class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input type="checkbox" @change="toggleSelectAll" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modified</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="item in [...filteredFolders, ...filteredFiles]" :key="item.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <input v-if="item.type !== 'folder'" 
                         type="checkbox" 
                         :checked="selectedFiles.includes(item.id)"
                         @change="toggleFileSelection(item.id)"
                         class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <svg v-if="item.type === 'folder'" class="w-5 h-5 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"/>
                    </svg>
                    <div v-else-if="item.type === 'image'" class="w-8 h-8 bg-gray-100 rounded mr-3 overflow-hidden">
                      <img :src="item.thumbnail" :alt="item.name" class="w-full h-full object-cover">
                    </div>
                    <svg v-else class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-900">{{ item.name }}</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ item.type === 'folder' ? 'Folder' : item.extension?.toUpperCase() || 'File' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ item.type === 'folder' ? `${item.items} items` : formatFileSize(item.size) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(item.modified_at) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button v-if="item.type !== 'folder'" @click="downloadFile(item)" 
                          class="text-indigo-600 hover:text-indigo-900 mr-3">Download</button>
                  <button @click="deleteItem(item)" 
                          class="text-red-600 hover:text-red-900">Delete</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Upload Modal -->
      <div v-if="showUploadModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Upload Files</h3>
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <div class="mt-4">
                <label class="cursor-pointer">
                  <span class="mt-2 block text-sm font-medium text-gray-900">
                    Drop files here or click to upload
                  </span>
                  <input type="file" multiple class="hidden" @change="handleFileUpload">
                </label>
              </div>
              <p class="mt-2 text-xs text-gray-500">PNG, JPG, PDF up to 10MB each</p>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
              <button @click="showUploadModal = false" 
                      class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                Cancel
              </button>
              <button @click="uploadFiles" 
                      class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700">
                Upload
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import AdminLayout from '@/components/admin/AdminLayout.vue'

const viewMode = ref('grid')
const searchQuery = ref('')
const currentPath = ref('/')
const selectedFiles = ref([])
const showUploadModal = ref(false)

const totalFiles = ref(247)
const imageFiles = ref(156)
const documentFiles = ref(91)
const storageUsed = ref('2.3 GB')

const folders = ref([
  { id: 1, name: 'Event Banners', path: '/event-banners', items: 45, type: 'folder', modified_at: '2025-01-10T10:30:00Z' },
  { id: 2, name: 'Documents', path: '/documents', items: 23, type: 'folder', modified_at: '2025-01-08T15:45:00Z' },
  { id: 3, name: 'Logos', path: '/logos', items: 12, type: 'folder', modified_at: '2025-01-05T09:20:00Z' }
])

const files = ref([
  {
    id: 1,
    name: 'tech-conference-banner.jpg',
    type: 'image',
    extension: 'jpg',
    size: 2048576,
    thumbnail: 'https://via.placeholder.com/150x150/4F46E5/FFFFFF?text=IMG',
    modified_at: '2025-01-12T10:30:00Z'
  },
  {
    id: 2,
    name: 'event-guidelines.pdf',
    type: 'document',
    extension: 'pdf',
    size: 1024000,
    modified_at: '2025-01-11T14:20:00Z'
  },
  {
    id: 3,
    name: 'music-festival-poster.png',
    type: 'image',
    extension: 'png',
    size: 3145728,
    thumbnail: 'https://via.placeholder.com/150x150/10B981/FFFFFF?text=IMG',
    modified_at: '2025-01-10T16:45:00Z'
  }
])

const breadcrumbs = computed(() => {
  const parts = currentPath.value.split('/').filter(Boolean)
  return parts.map((part, index) => ({
    name: part,
    path: '/' + parts.slice(0, index + 1).join('/')
  }))
})

const filteredFolders = computed(() => {
  if (!searchQuery.value) return folders.value
  return folders.value.filter(folder => 
    folder.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const filteredFiles = computed(() => {
  if (!searchQuery.value) return files.value
  return files.value.filter(file => 
    file.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const navigateToFolder = (path) => {
  currentPath.value = path
  // Load folder contents
}

const selectFile = (file) => {
  const index = selectedFiles.value.indexOf(file.id)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(file.id)
  }
}

const toggleFileSelection = (fileId) => {
  const index = selectedFiles.value.indexOf(fileId)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(fileId)
  }
}

const toggleSelectAll = () => {
  if (selectedFiles.value.length === files.value.length) {
    selectedFiles.value = []
  } else {
    selectedFiles.value = files.value.map(f => f.id)
  }
}

const handleFileUpload = (event) => {
  const uploadedFiles = Array.from(event.target.files)
  console.log('Files to upload:', uploadedFiles)
}

const uploadFiles = () => {
  showUploadModal.value = false
  alert('Files uploaded successfully!')
}

const downloadFile = (file) => {
  alert(`Downloading: ${file.name}`)
}

const deleteItem = (item) => {
  if (confirm(`Delete ${item.name}?`)) {
    if (item.type === 'folder') {
      const index = folders.value.findIndex(f => f.id === item.id)
      if (index > -1) folders.value.splice(index, 1)
    } else {
      const index = files.value.findIndex(f => f.id === item.id)
      if (index > -1) files.value.splice(index, 1)
    }
  }
}

const createFolder = () => {
  const name = prompt('Enter folder name:')
  if (name) {
    folders.value.push({
      id: Date.now(),
      name: name,
      path: currentPath.value + '/' + name,
      items: 0,
      type: 'folder',
      modified_at: new Date().toISOString()
    })
  }
}
</script>
