<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\QrCodeService;
use App\Models\Booking;
use App\Models\Event;
use Illuminate\Support\Facades\Validator;

class QrCodeController extends Controller
{
    protected $qrCodeService;

    public function __construct(QrCodeService $qrCodeService)
    {
        $this->qrCodeService = $qrCodeService;
    }

    /**
     * Generate QR code for booking
     */
    public function generateBookingQrCode(Request $request, Booking $booking)
    {
        // Check if user owns this booking or is admin
        if ($booking->user_id !== auth()->id() && !auth()->user()->hasRole(['Super Admin', 'Manager'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $result = $this->qrCodeService->generateBookingQrCode($booking);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 500);
        }

        // Update booking with QR code path
        $booking->update([
            'qr_code_path' => $result['qr_code_path']
        ]);

        return response()->json([
            'success' => true,
            'message' => 'QR code generated successfully',
            'data' => $result
        ]);
    }

    /**
     * Generate QR code for event check-in (admin only)
     */
    public function generateEventCheckinQrCode(Request $request, Event $event)
    {
        $result = $this->qrCodeService->generateEventQrCode($event);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Event check-in QR code generated successfully',
            'data' => $result
        ]);
    }

    /**
     * Generate QR code for event information
     */
    public function generateEventInfoQrCode(Request $request, Event $event)
    {
        $result = $this->qrCodeService->generateEventInfoQrCode($event);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Event info QR code generated successfully',
            'data' => $result
        ]);
    }

    /**
     * Generate custom QR code (admin only)
     */
    public function generateCustomQrCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'data' => 'required|string|max:2000',
            'size' => 'nullable|integer|min:100|max:1000',
            'margin' => 'nullable|integer|min:0|max:50',
            'label' => 'nullable|string|max:100',
            'filename' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $options = [
            'size' => $request->get('size', 300),
            'margin' => $request->get('margin', 10),
            'label' => $request->get('label'),
            'filename' => $request->get('filename'),
        ];

        $result = $this->qrCodeService->generateCustomQrCode($request->data, $options);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Custom QR code generated successfully',
            'data' => $result
        ]);
    }

    /**
     * Verify QR code
     */
    public function verifyQrCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'qr_data' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->qrCodeService->verifyQrCode($request->qr_data);

        return response()->json([
            'success' => $result['success'],
            'message' => $result['message'] ?? 'QR code verified',
            'data' => $result
        ]);
    }

    /**
     * Verify booking QR code
     */
    public function verifyBookingQrCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'qr_data' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->qrCodeService->verifyBookingQrCode($request->qr_data);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 422);
        }

        return response()->json([
            'success' => true,
            'message' => 'Booking QR code verified successfully',
            'data' => [
                'booking' => $result['booking'],
                'is_valid' => $result['is_valid'],
                'qr_data' => $result['qr_data'],
            ]
        ]);
    }

    /**
     * Check-in using QR code (admin only)
     */
    public function checkinWithQrCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'qr_data' => 'required|string',
            'notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Verify booking QR code
        $verification = $this->qrCodeService->verifyBookingQrCode($request->qr_data);

        if (!$verification['success']) {
            return response()->json([
                'success' => false,
                'message' => $verification['message']
            ], 422);
        }

        $booking = $verification['booking'];

        // Check if booking is valid for check-in
        if ($booking->status !== 'confirmed') {
            return response()->json([
                'success' => false,
                'message' => 'Booking is not confirmed'
            ], 422);
        }

        if ($booking->checked_in_at) {
            return response()->json([
                'success' => false,
                'message' => 'Booking already checked in at ' . $booking->checked_in_at->format('Y-m-d H:i:s')
            ], 422);
        }

        // Check-in the booking
        $booking->update([
            'checked_in_at' => now(),
            'checked_in_by' => auth()->id(),
            'checkin_notes' => $request->get('notes'),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Check-in successful',
            'data' => [
                'booking' => $booking->fresh(),
                'checked_in_at' => $booking->checked_in_at,
            ]
        ]);
    }

    /**
     * Get QR code statistics (admin only)
     */
    public function getQrCodeStatistics()
    {
        $stats = $this->qrCodeService->getQrCodeStats();

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Delete QR code (admin only)
     */
    public function deleteQrCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $path = $request->get('path');

        // Security check - ensure path is within qr-codes directory
        if (!str_starts_with($path, 'qr-codes/')) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid QR code path'
            ], 403);
        }

        $success = $this->qrCodeService->deleteQrCode($path);

        return response()->json([
            'success' => $success,
            'message' => $success ? 'QR code deleted successfully' : 'Failed to delete QR code'
        ]);
    }

    /**
     * Bulk generate QR codes for event bookings (admin only)
     */
    public function bulkGenerateBookingQrCodes(Request $request, Event $event)
    {
        $bookings = Booking::where('event_id', $event->id)
                          ->where('status', 'confirmed')
                          ->whereNull('qr_code_path')
                          ->get();

        if ($bookings->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No bookings found that need QR codes'
            ], 422);
        }

        $generated = 0;
        $failed = 0;

        foreach ($bookings as $booking) {
            $result = $this->qrCodeService->generateBookingQrCode($booking);
            
            if ($result['success']) {
                $booking->update([
                    'qr_code_path' => $result['qr_code_path']
                ]);
                $generated++;
            } else {
                $failed++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "QR codes generated: {$generated}, Failed: {$failed}",
            'data' => [
                'generated' => $generated,
                'failed' => $failed,
                'total' => $bookings->count(),
            ]
        ]);
    }
}
