import { createApp } from 'vue'
import { createRouter, createWeb<PERSON><PERSON><PERSON> } from 'vue-router'
import { createP<PERSON> } from 'pinia'
import axios from 'axios'
import './bootstrap'

// Import components
import App from './App.vue'
import HomePage from './pages/HomePage.vue'
import EventsPage from './pages/EventsPage.vue'
import EventDetailPage from './pages/EventDetailPage.vue'
import BookingPage from './pages/BookingPage.vue'
import MyTicketsPage from './pages/MyTicketsPage.vue'
import LoginPage from './pages/LoginPage.vue'
import AdminDashboard from './admin/Dashboard.vue'

// Configure axios
axios.defaults.baseURL = '/api'
axios.defaults.headers.common['Accept'] = 'application/json'
axios.defaults.headers.common['Content-Type'] = 'application/json'

// Add auth token if available
const token = localStorage.getItem('auth_token')
if (token) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
}

// Router configuration
const routes = [
    { path: '/', name: 'home', component: HomePage },
    { path: '/events', name: 'events', component: EventsPage },
    { path: '/events/:slug', name: 'event-detail', component: EventDetailPage },
    { path: '/booking/:eventId', name: 'booking', component: BookingPage },
    { path: '/my-tickets', name: 'my-tickets', component: MyTicketsPage },
    { path: '/login', name: 'login', component: LoginPage },
    { path: '/admin', name: 'admin', component: AdminDashboard, meta: { requiresAuth: true, requiresAdmin: true } },
]

const router = createRouter({
    history: createWebHistory(),
    routes,
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition
        } else {
            return { top: 0 }
        }
    }
})

// Navigation guards
router.beforeEach((to, from, next) => {
    const token = localStorage.getItem('auth_token')
    const user = JSON.parse(localStorage.getItem('user') || '{}')

    if (to.meta.requiresAuth && !token) {
        next('/login')
    } else if (to.meta.requiresAdmin && !user.roles?.some(role => ['Super Admin', 'Manager'].includes(role.name))) {
        next('/')
    } else {
        next()
    }
})

// Create app
const app = createApp(App)
app.use(createPinia())
app.use(router)

// Global properties
app.config.globalProperties.$http = axios

app.mount('#app')
