import './bootstrap.js';
import '../css/app.css';

import { createApp } from 'vue';

console.log('App.js loaded');

// Simple test component
const TestApp = {
  template: `
    <div class="min-h-screen bg-gray-100">
      <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 text-center">
          <h1 class="text-5xl font-bold mb-6">🎉 Event Manager</h1>
          <p class="text-xl mb-8">Vue.js Application is Running!</p>
          <div class="space-x-4">
            <button @click="showMessage" class="bg-white text-indigo-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100">
              Test Vue.js
            </button>
          </div>
          <div class="mt-8" v-if="message">
            <p class="text-lg bg-white text-indigo-600 inline-block px-4 py-2 rounded">{{ message }}</p>
          </div>
          <div class="mt-4">
            <p class="text-lg">Counter: {{ counter }}</p>
            <button @click="counter++" class="bg-yellow-500 text-black px-4 py-2 rounded mt-2">
              Increment
            </button>
          </div>
        </div>
      </div>
      <div class="max-w-7xl mx-auto px-4 py-16">
        <div class="text-center">
          <h2 class="text-3xl font-bold mb-8">Quick Links</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-white p-6 rounded-lg shadow-lg">
              <h3 class="text-xl font-bold mb-4">Browse Events</h3>
              <p class="text-gray-600 mb-4">Discover amazing events near you</p>
              <a href="/events" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">View Events</a>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg">
              <h3 class="text-xl font-bold mb-4">User Login</h3>
              <p class="text-gray-600 mb-4">Access your account and bookings</p>
              <a href="/login" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Login</a>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg">
              <h3 class="text-xl font-bold mb-4">Admin Panel</h3>
              <p class="text-gray-600 mb-4">Manage events and users</p>
              <a href="/admin" class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Admin</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  data() {
    return {
      counter: 0,
      message: ''
    }
  },
  methods: {
    showMessage() {
      this.message = 'Vue.js is working perfectly! 🚀';
      setTimeout(() => {
        this.message = '';
      }, 3000);
    }
  },
  mounted() {
    console.log('Test app mounted successfully');
    // Hide loading screen
    setTimeout(() => {
      const loadingScreen = document.getElementById('loading-screen');
      if (loadingScreen) {
        loadingScreen.style.display = 'none';
      }
    }, 500);
  }
};

// Create and mount app
try {
  console.log('Creating Vue app...');
  const app = createApp(TestApp);
  console.log('Mounting app...');
  app.mount('#app');
  console.log('App mounted successfully!');
} catch (error) {
  console.error('Failed to create/mount Vue app:', error);
}
