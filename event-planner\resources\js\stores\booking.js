import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { bookingAPI } from '@/services/api'

export const useBookingStore = defineStore('booking', () => {
  // State
  const bookings = ref([])
  const currentBooking = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const paymentStatus = ref(null)

  // Getters
  const userBookings = computed(() => bookings.value)
  const isProcessing = computed(() => isLoading.value)
  const hasError = computed(() => !!error.value)

  // Actions
  const createBooking = async (bookingData) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await bookingAPI.create(bookingData)
      currentBooking.value = response.data
      
      // Add to bookings list
      bookings.value.unshift(response.data)
      
      return { success: true, booking: response.data }
    } catch (err) {
      error.value = err.response?.data?.message || 'Booking failed'
      console.error('Booking creation error:', err)
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const processPayment = async (paymentData) => {
    isLoading.value = true
    error.value = null
    paymentStatus.value = 'processing'

    try {
      // Simulate payment processing for demo
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // In real implementation, this would call payment gateway
      const response = await bookingAPI.processPayment(currentBooking.value.id, paymentData)
      
      paymentStatus.value = 'completed'
      currentBooking.value = { ...currentBooking.value, ...response.data }
      
      // Update booking in list
      const index = bookings.value.findIndex(b => b.id === currentBooking.value.id)
      if (index !== -1) {
        bookings.value[index] = currentBooking.value
      }
      
      return { success: true, booking: currentBooking.value }
    } catch (err) {
      error.value = err.response?.data?.message || 'Payment failed'
      paymentStatus.value = 'failed'
      console.error('Payment processing error:', err)
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const fetchUserBookings = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await bookingAPI.getUserBookings()
      bookings.value = response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to fetch bookings'
      console.error('Fetch bookings error:', err)
      
      // Fallback to mock data for demo
      bookings.value = [
        {
          id: 1,
          event: {
            id: 1,
            title: 'Tech Conference 2025',
            start_date: '2025-02-15T09:00:00Z',
            venue_name: 'Convention Center'
          },
          ticket_type: {
            name: 'Regular',
            price: 2500
          },
          quantity: 2,
          total_amount: 5000,
          status: 'confirmed',
          booking_reference: 'TC2025-001',
          created_at: '2025-01-12T10:30:00Z'
        },
        {
          id: 2,
          event: {
            id: 2,
            title: 'Music Festival',
            start_date: '2025-03-20T18:00:00Z',
            venue_name: 'City Park'
          },
          ticket_type: {
            name: 'VIP',
            price: 3500
          },
          quantity: 1,
          total_amount: 3500,
          status: 'pending',
          booking_reference: 'MF2025-002',
          created_at: '2025-01-11T15:45:00Z'
        }
      ]
    } finally {
      isLoading.value = false
    }
  }

  const getBookingById = async (id) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await bookingAPI.getById(id)
      currentBooking.value = response.data
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to fetch booking'
      console.error('Fetch booking error:', err)
      return null
    } finally {
      isLoading.value = false
    }
  }

  const cancelBooking = async (id) => {
    isLoading.value = true
    error.value = null

    try {
      await bookingAPI.cancel(id)
      
      // Update booking status in list
      const index = bookings.value.findIndex(b => b.id === id)
      if (index !== -1) {
        bookings.value[index].status = 'cancelled'
      }
      
      // Update current booking if it's the same
      if (currentBooking.value?.id === id) {
        currentBooking.value.status = 'cancelled'
      }
      
      return { success: true }
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to cancel booking'
      console.error('Cancel booking error:', err)
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentBooking = () => {
    currentBooking.value = null
    paymentStatus.value = null
  }

  const downloadTicket = async (bookingId) => {
    try {
      const response = await bookingAPI.downloadTicket(bookingId)
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `ticket-${bookingId}.pdf`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      
      return { success: true }
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to download ticket'
      console.error('Download ticket error:', err)
      return { success: false, error: error.value }
    }
  }

  // Demo payment simulation
  const simulatePayment = async (paymentData) => {
    isLoading.value = true
    paymentStatus.value = 'processing'
    
    try {
      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Simulate success/failure based on card number
      const isSuccess = !paymentData.cardNumber.startsWith('4000')
      
      if (isSuccess) {
        paymentStatus.value = 'completed'
        
        // Update current booking
        if (currentBooking.value) {
          currentBooking.value.status = 'confirmed'
          currentBooking.value.payment_status = 'paid'
          currentBooking.value.booking_reference = `BK${Date.now()}`
        }
        
        return { success: true, booking: currentBooking.value }
      } else {
        throw new Error('Payment declined. Please check your card details.')
      }
    } catch (err) {
      paymentStatus.value = 'failed'
      error.value = err.message || 'Payment failed'
      return { success: false, error: error.value }
    } finally {
      isLoading.value = false
    }
  }

  return {
    // State
    bookings,
    currentBooking,
    isLoading,
    error,
    paymentStatus,
    
    // Getters
    userBookings,
    isProcessing,
    hasError,
    
    // Actions
    createBooking,
    processPayment,
    fetchUserBookings,
    getBookingById,
    cancelBooking,
    clearError,
    clearCurrentBooking,
    downloadTicket,
    simulatePayment
  }
})
