import{d as y,r as v,c as g}from"./vue-vendor-BupLktX_.js";import{b as d}from"./api-D9eN0dN9.js";const I=y("booking",()=>{const u=v([]),n=v(null),s=v(!1),t=v(null),i=v(null),f=g(()=>u.value),m=g(()=>s.value),k=g(()=>!!t.value);return{bookings:u,currentBooking:n,isLoading:s,error:t,paymentStatus:i,userBookings:f,isProcessing:m,hasError:k,createBooking:async o=>{var r,a;s.value=!0,t.value=null;try{const e=await d.create(o);return n.value=e.data,u.value.unshift(e.data),{success:!0,booking:e.data}}catch(e){return t.value=((a=(r=e.response)==null?void 0:r.data)==null?void 0:a.message)||"Booking failed",console.error("Booking creation error:",e),{success:!1,error:t.value}}finally{s.value=!1}},processPayment:async o=>{var r,a;s.value=!0,t.value=null,i.value="processing";try{await new Promise(c=>setTimeout(c,2e3));const e=await d.processPayment(n.value.id,o);i.value="completed",n.value={...n.value,...e.data};const l=u.value.findIndex(c=>c.id===n.value.id);return l!==-1&&(u.value[l]=n.value),{success:!0,booking:n.value}}catch(e){return t.value=((a=(r=e.response)==null?void 0:r.data)==null?void 0:a.message)||"Payment failed",i.value="failed",console.error("Payment processing error:",e),{success:!1,error:t.value}}finally{s.value=!1}},fetchUserBookings:async()=>{var o,r;s.value=!0,t.value=null;try{const a=await d.getUserBookings();u.value=a.data}catch(a){t.value=((r=(o=a.response)==null?void 0:o.data)==null?void 0:r.message)||"Failed to fetch bookings",console.error("Fetch bookings error:",a),u.value=[{id:1,event:{id:1,title:"Tech Conference 2025",start_date:"2025-02-15T09:00:00Z",venue_name:"Convention Center"},ticket_type:{name:"Regular",price:2500},quantity:2,total_amount:5e3,status:"confirmed",booking_reference:"TC2025-001",created_at:"2025-01-12T10:30:00Z"},{id:2,event:{id:2,title:"Music Festival",start_date:"2025-03-20T18:00:00Z",venue_name:"City Park"},ticket_type:{name:"VIP",price:3500},quantity:1,total_amount:3500,status:"pending",booking_reference:"MF2025-002",created_at:"2025-01-11T15:45:00Z"}]}finally{s.value=!1}},getBookingById:async o=>{var r,a;s.value=!0,t.value=null;try{const e=await d.getById(o);return n.value=e.data,e.data}catch(e){return t.value=((a=(r=e.response)==null?void 0:r.data)==null?void 0:a.message)||"Failed to fetch booking",console.error("Fetch booking error:",e),null}finally{s.value=!1}},cancelBooking:async o=>{var r,a,e;s.value=!0,t.value=null;try{await d.cancel(o);const l=u.value.findIndex(c=>c.id===o);return l!==-1&&(u.value[l].status="cancelled"),((r=n.value)==null?void 0:r.id)===o&&(n.value.status="cancelled"),{success:!0}}catch(l){return t.value=((e=(a=l.response)==null?void 0:a.data)==null?void 0:e.message)||"Failed to cancel booking",console.error("Cancel booking error:",l),{success:!1,error:t.value}}finally{s.value=!1}},clearError:()=>{t.value=null},clearCurrentBooking:()=>{n.value=null,i.value=null},downloadTicket:async o=>{var r,a;try{const e=await d.downloadTicket(o),l=window.URL.createObjectURL(new Blob([e.data])),c=document.createElement("a");return c.href=l,c.setAttribute("download",`ticket-${o}.pdf`),document.body.appendChild(c),c.click(),c.remove(),window.URL.revokeObjectURL(l),{success:!0}}catch(e){return t.value=((a=(r=e.response)==null?void 0:r.data)==null?void 0:a.message)||"Failed to download ticket",console.error("Download ticket error:",e),{success:!1,error:t.value}}},simulatePayment:async o=>{s.value=!0,i.value="processing";try{if(await new Promise(a=>setTimeout(a,3e3)),!o.cardNumber.startsWith("4000"))return i.value="completed",n.value&&(n.value.status="confirmed",n.value.payment_status="paid",n.value.booking_reference=`BK${Date.now()}`),{success:!0,booking:n.value};throw new Error("Payment declined. Please check your card details.")}catch(r){return i.value="failed",t.value=r.message||"Payment failed",{success:!1,error:t.value}}finally{s.value=!1}}}});export{I as u};
