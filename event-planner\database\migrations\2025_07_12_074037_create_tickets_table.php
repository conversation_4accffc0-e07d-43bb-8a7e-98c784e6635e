<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->string('ticket_number')->unique();
            $table->foreignId('booking_id')->constrained('bookings')->onDelete('cascade');
            $table->foreignId('event_id')->constrained('events')->onDelete('cascade');
            $table->foreignId('ticket_type_id')->constrained('ticket_types')->onDelete('cascade');

            // Ticket holder details
            $table->string('holder_name');
            $table->string('holder_email')->nullable();
            $table->string('holder_phone')->nullable();

            // Ticket details
            $table->decimal('price', 10, 2);
            $table->string('qr_code')->unique(); // QR code for scanning
            $table->string('qr_code_image_path')->nullable(); // Path to QR code image

            // Ticket status
            $table->enum('status', ['active', 'used', 'cancelled', 'expired'])->default('active');
            $table->datetime('scanned_at')->nullable();
            $table->foreignId('scanned_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('scan_notes')->nullable();

            // Additional info
            $table->json('custom_fields')->nullable();

            $table->timestamps();

            $table->index(['booking_id']);
            $table->index(['event_id', 'status']);
            $table->index(['qr_code']);
            $table->index(['ticket_number']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
