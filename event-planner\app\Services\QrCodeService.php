<?php

namespace App\Services;

use Endroid\QrCode\Builder\Builder;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel;
use Endroid\QrCode\Label\LabelAlignment;
use Endroid\QrCode\Logo\Logo;
use Endroid\QrCode\RoundBlockSizeMode;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Support\Facades\Storage;
use App\Models\Booking;
use App\Models\Event;

class QrCodeService
{
    /**
     * Generate QR code for booking
     */
    public function generateBookingQrCode(Booking $booking): array
    {
        try {
            // Create QR code data
            $qrData = [
                'type' => 'booking',
                'booking_id' => $booking->id,
                'booking_number' => $booking->booking_number,
                'event_id' => $booking->event_id,
                'user_id' => $booking->user_id,
                'verification_url' => url("/verify-booking/{$booking->booking_number}"),
                'generated_at' => now()->toISOString(),
            ];

            $qrContent = json_encode($qrData);

            // Generate QR code
            $result = Builder::create()
                ->writer(new PngWriter())
                ->data($qrContent)
                ->encoding(new Encoding('UTF-8'))
                ->errorCorrectionLevel(ErrorCorrectionLevel::Medium)
                ->size(300)
                ->margin(10)
                ->roundBlockSizeMode(RoundBlockSizeMode::Margin)
                ->labelText("Booking: {$booking->booking_number}")
                ->labelAlignment(LabelAlignment::Center)
                ->build();

            // Save QR code to storage
            $filename = "qr-codes/booking-{$booking->booking_number}.png";
            $qrCodeData = $result->getString();
            
            Storage::disk('public')->put($filename, $qrCodeData);

            return [
                'success' => true,
                'qr_code_path' => $filename,
                'qr_code_url' => Storage::disk('public')->url($filename),
                'qr_data' => $qrData,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate QR code: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Generate QR code for event check-in
     */
    public function generateEventQrCode(Event $event): array
    {
        try {
            // Create QR code data
            $qrData = [
                'type' => 'event_checkin',
                'event_id' => $event->id,
                'event_slug' => $event->slug,
                'checkin_url' => url("/admin/events/{$event->id}/checkin"),
                'generated_at' => now()->toISOString(),
            ];

            $qrContent = json_encode($qrData);

            // Generate QR code
            $result = Builder::create()
                ->writer(new PngWriter())
                ->data($qrContent)
                ->encoding(new Encoding('UTF-8'))
                ->errorCorrectionLevel(ErrorCorrectionLevel::Medium)
                ->size(400)
                ->margin(15)
                ->roundBlockSizeMode(RoundBlockSizeMode::Margin)
                ->labelText("Event Check-in: {$event->title}")
                ->labelAlignment(LabelAlignment::Center)
                ->build();

            // Save QR code to storage
            $filename = "qr-codes/event-{$event->slug}-checkin.png";
            $qrCodeData = $result->getString();
            
            Storage::disk('public')->put($filename, $qrCodeData);

            return [
                'success' => true,
                'qr_code_path' => $filename,
                'qr_code_url' => Storage::disk('public')->url($filename),
                'qr_data' => $qrData,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate QR code: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Generate QR code for event information
     */
    public function generateEventInfoQrCode(Event $event): array
    {
        try {
            // Create QR code data - simple URL for event page
            $eventUrl = url("/events/{$event->slug}");

            // Generate QR code
            $result = Builder::create()
                ->writer(new PngWriter())
                ->data($eventUrl)
                ->encoding(new Encoding('UTF-8'))
                ->errorCorrectionLevel(ErrorCorrectionLevel::Medium)
                ->size(300)
                ->margin(10)
                ->roundBlockSizeMode(RoundBlockSizeMode::Margin)
                ->labelText($event->title)
                ->labelAlignment(LabelAlignment::Center)
                ->build();

            // Save QR code to storage
            $filename = "qr-codes/event-{$event->slug}-info.png";
            $qrCodeData = $result->getString();
            
            Storage::disk('public')->put($filename, $qrCodeData);

            return [
                'success' => true,
                'qr_code_path' => $filename,
                'qr_code_url' => Storage::disk('public')->url($filename),
                'qr_data' => ['url' => $eventUrl],
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate QR code: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Generate custom QR code
     */
    public function generateCustomQrCode(string $data, array $options = []): array
    {
        try {
            $size = $options['size'] ?? 300;
            $margin = $options['margin'] ?? 10;
            $label = $options['label'] ?? null;
            $filename = $options['filename'] ?? 'custom-qr-' . time() . '.png';

            $builder = Builder::create()
                ->writer(new PngWriter())
                ->data($data)
                ->encoding(new Encoding('UTF-8'))
                ->errorCorrectionLevel(ErrorCorrectionLevel::Medium)
                ->size($size)
                ->margin($margin)
                ->roundBlockSizeMode(RoundBlockSizeMode::Margin);

            if ($label) {
                $builder->labelText($label)
                       ->labelAlignment(LabelAlignment::Center);
            }

            // Add logo if provided
            if (isset($options['logo_path']) && file_exists($options['logo_path'])) {
                $logo = Logo::create($options['logo_path'])
                           ->setResizeToWidth($size / 5);
                $builder->logo($logo);
            }

            $result = $builder->build();

            // Save QR code to storage
            $filepath = "qr-codes/{$filename}";
            $qrCodeData = $result->getString();
            
            Storage::disk('public')->put($filepath, $qrCodeData);

            return [
                'success' => true,
                'qr_code_path' => $filepath,
                'qr_code_url' => Storage::disk('public')->url($filepath),
                'qr_data' => $data,
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to generate QR code: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify QR code data
     */
    public function verifyQrCode(string $qrData): array
    {
        try {
            // Try to decode as JSON first
            $decodedData = json_decode($qrData, true);
            
            if (json_last_error() === JSON_ERROR_NONE && is_array($decodedData)) {
                // It's JSON data
                return [
                    'success' => true,
                    'type' => $decodedData['type'] ?? 'unknown',
                    'data' => $decodedData,
                ];
            } else {
                // It's plain text/URL
                return [
                    'success' => true,
                    'type' => 'url',
                    'data' => $qrData,
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Invalid QR code data: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify booking QR code
     */
    public function verifyBookingQrCode(string $qrData): array
    {
        try {
            $verification = $this->verifyQrCode($qrData);
            
            if (!$verification['success']) {
                return $verification;
            }

            $data = $verification['data'];
            
            if (!is_array($data) || ($data['type'] ?? '') !== 'booking') {
                return [
                    'success' => false,
                    'message' => 'Invalid booking QR code',
                ];
            }

            // Find booking
            $booking = Booking::where('booking_number', $data['booking_number'])
                             ->with(['event', 'user'])
                             ->first();

            if (!$booking) {
                return [
                    'success' => false,
                    'message' => 'Booking not found',
                ];
            }

            return [
                'success' => true,
                'booking' => $booking,
                'qr_data' => $data,
                'is_valid' => $booking->status === 'confirmed',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'QR code verification failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Delete QR code file
     */
    public function deleteQrCode(string $path): bool
    {
        try {
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
                return true;
            }
            return false;

        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get QR code statistics
     */
    public function getQrCodeStats(): array
    {
        try {
            $qrCodeFiles = Storage::disk('public')->files('qr-codes');
            
            $stats = [
                'total_qr_codes' => count($qrCodeFiles),
                'booking_qr_codes' => 0,
                'event_qr_codes' => 0,
                'custom_qr_codes' => 0,
                'total_size' => 0,
            ];

            foreach ($qrCodeFiles as $file) {
                $filename = basename($file);
                $stats['total_size'] += Storage::disk('public')->size($file);

                if (str_contains($filename, 'booking-')) {
                    $stats['booking_qr_codes']++;
                } elseif (str_contains($filename, 'event-')) {
                    $stats['event_qr_codes']++;
                } else {
                    $stats['custom_qr_codes']++;
                }
            }

            return $stats;

        } catch (\Exception $e) {
            return [
                'total_qr_codes' => 0,
                'booking_qr_codes' => 0,
                'event_qr_codes' => 0,
                'custom_qr_codes' => 0,
                'total_size' => 0,
            ];
        }
    }
}
