<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\Booking;
use App\Models\Event;

class SmsService
{
    protected $provider;
    protected $config;

    public function __construct()
    {
        $this->provider = config('services.sms.default_provider', 'twilio');
        $this->config = config('services.sms.providers.' . $this->provider, []);
    }

    /**
     * Send SMS message
     */
    public function sendSms(string $to, string $message): array
    {
        try {
            switch ($this->provider) {
                case 'twilio':
                    return $this->sendViaTwilio($to, $message);
                case 'msg91':
                    return $this->sendViaMsg91($to, $message);
                case 'textlocal':
                    return $this->sendViaTextLocal($to, $message);
                default:
                    throw new \Exception('Unsupported SMS provider: ' . $this->provider);
            }

        } catch (\Exception $e) {
            Log::error('SMS sending failed', [
                'provider' => $this->provider,
                'to' => $to,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send SMS: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Send booking confirmation SMS
     */
    public function sendBookingConfirmationSms(Booking $booking): bool
    {
        $phone = $booking->customer_info['phone'] ?? null;
        
        if (!$phone) {
            return false;
        }

        $message = "🎉 Booking Confirmed!\n\n";
        $message .= "Event: {$booking->event->title}\n";
        $message .= "Date: {$booking->event->start_date->format('M j, Y g:i A')}\n";
        $message .= "Venue: {$booking->event->venue_name}\n";
        $message .= "Booking Ref: {$booking->booking_number}\n\n";
        $message .= "Show this SMS at the venue for entry.\n";
        $message .= "Event Manager";

        $result = $this->sendSms($phone, $message);

        if ($result['success']) {
            Log::info('Booking confirmation SMS sent', [
                'booking_id' => $booking->id,
                'phone' => $phone,
            ]);
        }

        return $result['success'];
    }

    /**
     * Send event reminder SMS
     */
    public function sendEventReminderSms(Event $event, array $attendees): array
    {
        $sent = 0;
        $failed = 0;

        $message = "⏰ Event Reminder\n\n";
        $message .= "Event: {$event->title}\n";
        $message .= "Date: {$event->start_date->format('M j, Y g:i A')}\n";
        $message .= "Venue: {$event->venue_name}\n";
        $message .= "Address: {$event->venue_address}\n\n";
        $message .= "Please arrive 15-30 minutes early.\n";
        $message .= "Event Manager";

        foreach ($attendees as $attendee) {
            if (!empty($attendee['phone'])) {
                $result = $this->sendSms($attendee['phone'], $message);
                
                if ($result['success']) {
                    $sent++;
                } else {
                    $failed++;
                }
            } else {
                $failed++;
            }
        }

        Log::info('Event reminder SMS campaign completed', [
            'event_id' => $event->id,
            'sent' => $sent,
            'failed' => $failed,
        ]);

        return [
            'sent' => $sent,
            'failed' => $failed,
            'total' => count($attendees),
        ];
    }

    /**
     * Send OTP SMS
     */
    public function sendOtp(string $phone, string $otp): bool
    {
        $message = "Your Event Manager verification code is: {$otp}\n\n";
        $message .= "This code will expire in 10 minutes.\n";
        $message .= "Do not share this code with anyone.\n\n";
        $message .= "Event Manager";

        $result = $this->sendSms($phone, $message);

        if ($result['success']) {
            Log::info('OTP SMS sent', [
                'phone' => $phone,
                'otp' => $otp,
            ]);
        }

        return $result['success'];
    }

    /**
     * Send via Twilio
     */
    protected function sendViaTwilio(string $to, string $message): array
    {
        $accountSid = $this->config['account_sid'] ?? '';
        $authToken = $this->config['auth_token'] ?? '';
        $fromNumber = $this->config['from_number'] ?? '';

        if (!$accountSid || !$authToken || !$fromNumber) {
            throw new \Exception('Twilio configuration incomplete');
        }

        $response = Http::withBasicAuth($accountSid, $authToken)
            ->asForm()
            ->post("https://api.twilio.com/2010-04-01/Accounts/{$accountSid}/Messages.json", [
                'From' => $fromNumber,
                'To' => $to,
                'Body' => $message,
            ]);

        if ($response->successful()) {
            return [
                'success' => true,
                'message_id' => $response->json('sid'),
                'data' => $response->json(),
            ];
        }

        throw new \Exception('Twilio API error: ' . $response->body());
    }

    /**
     * Send via MSG91
     */
    protected function sendViaMsg91(string $to, string $message): array
    {
        $authKey = $this->config['auth_key'] ?? '';
        $senderId = $this->config['sender_id'] ?? '';
        $route = $this->config['route'] ?? '4';

        if (!$authKey || !$senderId) {
            throw new \Exception('MSG91 configuration incomplete');
        }

        $response = Http::post('https://api.msg91.com/api/sendhttp.php', [
            'authkey' => $authKey,
            'mobiles' => $to,
            'message' => $message,
            'sender' => $senderId,
            'route' => $route,
        ]);

        if ($response->successful()) {
            $responseData = $response->json();
            
            if (isset($responseData['type']) && $responseData['type'] === 'success') {
                return [
                    'success' => true,
                    'message_id' => $responseData['message'] ?? '',
                    'data' => $responseData,
                ];
            }
        }

        throw new \Exception('MSG91 API error: ' . $response->body());
    }

    /**
     * Send via TextLocal
     */
    protected function sendViaTextLocal(string $to, string $message): array
    {
        $apiKey = $this->config['api_key'] ?? '';
        $sender = $this->config['sender'] ?? '';

        if (!$apiKey || !$sender) {
            throw new \Exception('TextLocal configuration incomplete');
        }

        $response = Http::asForm()->post('https://api.textlocal.in/send/', [
            'apikey' => $apiKey,
            'numbers' => $to,
            'message' => $message,
            'sender' => $sender,
        ]);

        if ($response->successful()) {
            $responseData = $response->json();
            
            if (isset($responseData['status']) && $responseData['status'] === 'success') {
                return [
                    'success' => true,
                    'message_id' => $responseData['message_id'] ?? '',
                    'data' => $responseData,
                ];
            }
        }

        throw new \Exception('TextLocal API error: ' . $response->body());
    }

    /**
     * Test SMS configuration
     */
    public function testSmsConfiguration(string $testPhone): bool
    {
        $message = "This is a test message from Event Manager SMS service.\n\n";
        $message .= "Test sent at: " . now()->format('Y-m-d H:i:s') . "\n";
        $message .= "Provider: " . ucfirst($this->provider);

        $result = $this->sendSms($testPhone, $message);

        Log::info('SMS configuration test', [
            'provider' => $this->provider,
            'phone' => $testPhone,
            'success' => $result['success'],
        ]);

        return $result['success'];
    }

    /**
     * Get available SMS providers
     */
    public function getAvailableProviders(): array
    {
        return [
            'twilio' => [
                'name' => 'Twilio',
                'description' => 'Global SMS service with high delivery rates',
                'enabled' => !empty($this->config['account_sid']),
            ],
            'msg91' => [
                'name' => 'MSG91',
                'description' => 'Indian SMS service provider',
                'enabled' => !empty($this->config['auth_key']),
            ],
            'textlocal' => [
                'name' => 'TextLocal',
                'description' => 'UK-based SMS service provider',
                'enabled' => !empty($this->config['api_key']),
            ],
        ];
    }

    /**
     * Format phone number
     */
    protected function formatPhoneNumber(string $phone): string
    {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Add country code if not present (assuming India +91)
        if (strlen($phone) === 10) {
            $phone = '91' . $phone;
        }
        
        // Add + prefix
        if (!str_starts_with($phone, '+')) {
            $phone = '+' . $phone;
        }
        
        return $phone;
    }
}
