<template>
  <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
    <!-- Event Image -->
    <div class="relative h-48 bg-gray-200 overflow-hidden">
      <img
        v-if="event.featured_image"
        :src="event.featured_image"
        :alt="event.title"
        class="w-full h-full object-cover"
      />
      <div
        v-else
        class="w-full h-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center"
      >
        <span class="text-white text-3xl font-bold">{{ event.title.charAt(0) }}</span>
      </div>
      
      <!-- Category Badge -->
      <div class="absolute top-3 left-3">
        <span class="bg-white/90 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
          {{ event.category?.name || 'Event' }}
        </span>
      </div>
      
      <!-- Featured Badge -->
      <div v-if="event.is_featured" class="absolute top-3 right-3">
        <span class="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-bold">
          Featured
        </span>
      </div>
      
      <!-- Price Badge -->
      <div class="absolute bottom-3 right-3">
        <span class="bg-indigo-600 text-white px-2 py-1 rounded-full text-sm font-semibold">
          {{ formatPrice(getMinPrice()) }}
        </span>
      </div>
    </div>

    <!-- Event Details -->
    <div class="p-6">
      <h3 class="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
        {{ event.title }}
      </h3>
      <p class="text-gray-600 mb-4 line-clamp-2">
        {{ event.short_description || event.description }}
      </p>
      
      <!-- Event Info -->
      <div class="space-y-2 mb-6">
        <div class="flex items-center text-gray-500">
          <CalendarIcon class="w-4 h-4 mr-2" />
          <span class="text-sm">{{ formatDate(event.start_date) }}</span>
        </div>
        <div class="flex items-center text-gray-500">
          <MapPinIcon class="w-4 h-4 mr-2" />
          <span class="text-sm">{{ event.venue_name || event.location }}</span>
        </div>
        <div v-if="event.available_tickets !== undefined" class="flex items-center text-gray-500">
          <TicketIcon class="w-4 h-4 mr-2" />
          <span class="text-sm">
            {{ event.available_tickets > 0 ? `${event.available_tickets} tickets left` : 'Sold out' }}
          </span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex gap-3">
        <router-link
          :to="`/events/${event.slug || event.id}`"
          class="flex-1 bg-indigo-600 text-white text-center py-2 px-4 rounded-md font-medium hover:bg-indigo-700 transition-colors"
        >
          View Details
        </router-link>
        <button
          v-if="event.available_tickets > 0"
          @click="quickBook"
          class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-200 transition-colors"
        >
          Quick Book
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { CalendarIcon, MapPinIcon, TicketIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
  event: {
    type: Object,
    required: true
  }
})

const router = useRouter()

const getMinPrice = () => {
  if (!props.event.ticket_types || props.event.ticket_types.length === 0) {
    return props.event.price || 0
  }
  return Math.min(...props.event.ticket_types.map(t => t.price))
}

const formatPrice = (price) => {
  if (price === 0) return 'Free'
  return `₹${price.toLocaleString()}`
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const quickBook = () => {
  router.push(`/booking/${props.event.id}`)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
