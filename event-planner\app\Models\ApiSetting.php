<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ApiSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'service_type',
        'provider',
        'name',
        'description',
        'credentials',
        'configuration',
        'is_active',
        'is_default',
        'is_sandbox',
        'last_tested_at',
        'test_results',
    ];

    protected $casts = [
        'credentials' => 'array',
        'configuration' => 'array',
        'test_results' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'is_sandbox' => 'boolean',
        'last_tested_at' => 'datetime',
    ];

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByServiceType($query, $type)
    {
        return $query->where('service_type', $type);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeSandbox($query)
    {
        return $query->where('is_sandbox', true);
    }

    public function scopeProduction($query)
    {
        return $query->where('is_sandbox', false);
    }

    /**
     * Get default provider for a service type
     */
    public static function getDefault($serviceType)
    {
        return static::where('service_type', $serviceType)
                    ->where('is_default', true)
                    ->where('is_active', true)
                    ->first();
    }

    /**
     * Get active providers for a service type
     */
    public static function getActiveProviders($serviceType)
    {
        return static::where('service_type', $serviceType)
                    ->where('is_active', true)
                    ->get();
    }
}
