<template>
  <div :class="containerClasses">
    <div :class="spinnerClasses">
      <div class="animate-spin rounded-full border-2 border-current border-t-transparent" :class="sizeClasses"></div>
    </div>
    <p v-if="message" :class="messageClasses">{{ message }}</p>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
  },
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'white', 'gray'].includes(value)
  },
  message: {
    type: String,
    default: ''
  },
  fullscreen: {
    type: Boolean,
    default: false
  },
  center: {
    type: Boolean,
    default: true
  }
})

const sizeClasses = computed(() => {
  const sizes = {
    xs: 'w-4 h-4',
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }
  return sizes[props.size]
})

const spinnerClasses = computed(() => {
  const variants = {
    primary: 'text-indigo-600',
    secondary: 'text-gray-600',
    white: 'text-white',
    gray: 'text-gray-400'
  }
  return variants[props.variant]
})

const containerClasses = computed(() => {
  let classes = []
  
  if (props.fullscreen) {
    classes.push('fixed inset-0 z-50 bg-white bg-opacity-75 flex items-center justify-center')
  } else if (props.center) {
    classes.push('flex flex-col items-center justify-center')
  }
  
  if (props.message) {
    classes.push('space-y-3')
  }
  
  return classes.join(' ')
})

const messageClasses = computed(() => {
  const variants = {
    primary: 'text-gray-700',
    secondary: 'text-gray-600',
    white: 'text-white',
    gray: 'text-gray-500'
  }
  
  let classes = ['text-sm font-medium']
  classes.push(variants[props.variant])
  
  return classes.join(' ')
})
</script>
