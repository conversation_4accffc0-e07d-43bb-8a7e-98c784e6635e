<template>
  <header class="bg-white shadow-sm border-b border-gray-200">
    <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo and Brand -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 2L3 7v11a1 1 0 001 1h3v-6h6v6h3a1 1 0 001-1V7l-7-5z"/>
              </svg>
            </div>
            <span class="text-xl font-bold text-gray-900">EventManager</span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-8">
          <router-link
            to="/"
            class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors"
            :class="{ 'text-indigo-600': $route.name === 'home' }"
          >
            Home
          </router-link>
          <router-link
            to="/events"
            class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors"
            :class="{ 'text-indigo-600': $route.name === 'events' }"
          >
            Events
          </router-link>

          <!-- Authenticated User Menu -->
          <div v-if="authStore.isAuthenticated" class="relative">
            <div class="flex items-center space-x-4">
              <router-link
                to="/dashboard"
                class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                Dashboard
              </router-link>
              <router-link
                to="/my-tickets"
                class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                My Tickets
              </router-link>
              <span class="text-gray-600 text-sm">{{ authStore.userName }}</span>
              <button
                @click="handleLogout"
                class="text-gray-700 hover:text-red-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                Logout
              </button>
            </div>
          </div>

          <!-- Guest Links -->
          <div v-else class="flex items-center space-x-4">
            <router-link
              to="/login"
              class="text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium"
            >
              Login
            </router-link>
            <router-link
              to="/register"
              class="bg-indigo-600 text-white hover:bg-indigo-700 px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Sign Up
            </router-link>
          </div>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button
            @click="mobileMenuOpen = !mobileMenuOpen"
            class="text-gray-700 hover:text-indigo-600 p-2"
          >
            <svg v-if="!mobileMenuOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
            <svg v-else class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-if="mobileMenuOpen" class="md:hidden border-t border-gray-200 pt-4 pb-3">
        <div class="space-y-1">
          <router-link
            to="/"
            class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600"
            @click="mobileMenuOpen = false"
          >
            Home
          </router-link>
          <router-link
            to="/events"
            class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600"
            @click="mobileMenuOpen = false"
          >
            Events
          </router-link>

          <div v-if="authStore.isAuthenticated" class="border-t border-gray-200 pt-4">
            <div class="px-3 py-2 text-sm text-gray-500">{{ authStore.userName }}</div>
            <router-link
              to="/dashboard"
              class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600"
              @click="mobileMenuOpen = false"
            >
              Dashboard
            </router-link>
            <router-link
              to="/my-tickets"
              class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600"
              @click="mobileMenuOpen = false"
            >
              My Tickets
            </router-link>
            <button
              @click="handleLogout"
              class="block w-full text-left px-3 py-2 text-base font-medium text-red-600 hover:text-red-700"
            >
              Logout
            </button>
          </div>

          <div v-else class="border-t border-gray-200 pt-4 space-y-1">
            <router-link
              to="/login"
              class="block px-3 py-2 text-base font-medium text-gray-700 hover:text-indigo-600"
              @click="mobileMenuOpen = false"
            >
              Login
            </router-link>
            <router-link
              to="/register"
              class="block px-3 py-2 text-base font-medium text-indigo-600 hover:text-indigo-700"
              @click="mobileMenuOpen = false"
            >
              Sign Up
            </router-link>
          </div>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()
const mobileMenuOpen = ref(false)

const handleLogout = async () => {
  await authStore.logout()
  mobileMenuOpen.value = false
  router.push('/')
}
</script>
