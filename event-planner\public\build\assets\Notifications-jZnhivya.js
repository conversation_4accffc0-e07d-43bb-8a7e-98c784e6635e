import{r,B as P,l as z,w as U,h as e,f as a,i as m,k as p,F as x,C as O,x as f,z as H,y as v,g as o,n as y,m as q,t as n}from"./vue-vendor-BupLktX_.js";import{_ as F}from"./admin-9F2yeZU0.js";import"./chart-vendor-Db3utXXw.js";const L={class:"space-y-6"},A={class:"flex justify-between items-center"},J={class:"flex space-x-3"},Y={class:"bg-white shadow-sm rounded-lg border border-gray-200"},G={class:"border-b border-gray-200"},I={class:"-mb-px flex space-x-8 px-6"},K=["onClick"],Q={key:0,class:"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium"},W={key:0,class:"p-6"},X={class:"space-y-6"},ee={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},te={class:"flex justify-between items-start mb-3"},se={class:"text-md font-medium text-gray-900"},ne={class:"text-sm text-gray-600 mb-4"},oe={class:"flex justify-between items-center text-xs text-gray-500 mb-4"},ie={class:"flex space-x-2"},ae=["onClick"],le=["onClick"],re={key:1,class:"p-6"},de={class:"space-y-6"},ce={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ue={class:"flex justify-between items-start mb-3"},me={class:"text-md font-medium text-gray-900"},pe={class:"text-xs text-gray-500"},xe={class:"bg-gray-50 p-3 rounded-md mb-4"},ge={class:"text-sm text-gray-800"},ye={class:"flex justify-between items-center text-xs text-gray-500 mb-4"},fe={class:"flex space-x-2"},ve=["onClick"],be=["onClick"],he={key:2,class:"p-6"},_e={class:"space-y-6"},we={class:"space-y-4"},ke={class:"flex justify-between items-start"},Ce={class:"flex-1"},Te={class:"text-md font-medium text-gray-900"},Se={class:"text-sm text-gray-600 mt-1"},Ee={class:"flex items-center mt-3 text-xs text-gray-500"},Me={key:3,class:"p-6"},Ne={class:"space-y-6"},$e={class:"overflow-x-auto"},je={class:"min-w-full divide-y divide-gray-200"},De={class:"bg-white divide-y divide-gray-200"},Be={class:"px-6 py-4 whitespace-nowrap"},Re={class:"text-sm font-medium text-gray-900"},Ve={class:"text-sm text-gray-500"},Ze={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Pe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},ze={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Ue={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Oe={class:"px-6 py-4 whitespace-nowrap"},He={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},qe=["onClick"],Fe=["onClick"],Le={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ae={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},Je={class:"mt-3"},Ye={class:"space-y-4"},Ge={class:"flex justify-end space-x-3 mt-6"},Xe={__name:"Notifications",setup(Ie){const d=r("email"),c=r(!1),b=r([{key:"email",name:"Email Templates",count:8},{key:"sms",name:"SMS Templates",count:5},{key:"push",name:"Push Notifications",count:12},{key:"campaigns",name:"Campaign History",count:24}]),l=P({type:"",name:"",description:""}),h=r([{id:1,name:"Booking Confirmation",description:"Sent when a user successfully books tickets",status:"active",usage_count:1247,updated_at:"2025-01-10T10:30:00Z"},{id:2,name:"Event Reminder",description:"Reminder sent 24 hours before event",status:"active",usage_count:892,updated_at:"2025-01-08T15:45:00Z"},{id:3,name:"Ticket Cancellation",description:"Confirmation of ticket cancellation",status:"active",usage_count:156,updated_at:"2025-01-05T09:20:00Z"}]),_=r([{id:1,name:"Booking Confirmation",message:"Hi {name}, your booking for {event} is confirmed! Ref: {booking_ref}. Show this SMS at entry.",sent_count:1247,updated_at:"2025-01-10T10:30:00Z"},{id:2,name:"Event Reminder",message:"Reminder: {event} starts tomorrow at {time}. Venue: {venue}. See you there!",sent_count:892,updated_at:"2025-01-08T15:45:00Z"}]),w=r([{id:1,title:"New Event Alert",message:"Tech Conference 2025 tickets are now available!",recipients:5420,click_rate:12.5,status:"sent",sent_at:"2025-01-12T09:00:00Z"},{id:2,title:"Event Reminder",message:"Your event starts in 2 hours. Don't forget!",recipients:1247,click_rate:8.3,status:"sent",sent_at:"2025-01-11T14:00:00Z"}]),k=r([{id:1,name:"January Newsletter",subject:"Exciting Events This Month!",type:"Email",recipients:15420,open_rate:24.5,status:"completed",sent_at:"2025-01-01T10:00:00Z"},{id:2,name:"Event Reminder Campaign",subject:"Don't Miss These Upcoming Events",type:"Email",recipients:8930,open_rate:31.2,status:"completed",sent_at:"2024-12-28T15:30:00Z"}]),g=i=>new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),C=i=>({completed:"bg-green-100 text-green-800",scheduled:"bg-blue-100 text-blue-800",draft:"bg-yellow-100 text-yellow-800",failed:"bg-red-100 text-red-800"})[i]||"bg-gray-100 text-gray-800",T=()=>{c.value=!1,alert(`${l.type} template "${l.name}" created!`),Object.assign(l,{type:"",name:"",description:""})},S=i=>{alert(`Editing template: ${i.name}`)},E=i=>{alert(`Previewing template: ${i.name}`)},M=()=>{l.type="email",c.value=!0},N=()=>{l.type="sms",c.value=!0},$=i=>{alert(`Editing SMS template: ${i.name}`)},j=i=>{alert(`Sending test SMS: ${i.name}`)},D=()=>{alert("Opening push notification composer...")},B=()=>{alert("Opening campaign creator...")},R=()=>{alert("Opening bulk notification sender...")},V=i=>{alert(`Viewing campaign: ${i.name}`)},Z=i=>{alert(`Duplicating campaign: ${i.name}`)};return(i,s)=>(o(),z(F,null,{default:U(()=>[e("div",L,[e("div",A,[s[5]||(s[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Notifications & Communications"),e("p",{class:"text-gray-600"},"Manage email templates, SMS campaigns, and push notifications")],-1)),e("div",J,[e("button",{onClick:s[0]||(s[0]=t=>c.value=!0),class:"bg-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-indigo-700 transition-colors"}," Create Template "),e("button",{onClick:R,class:"bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors"}," Send Bulk Notification ")])]),e("div",Y,[e("div",G,[e("nav",I,[(o(!0),a(x,null,p(b.value,t=>(o(),a("button",{key:t.key,onClick:u=>d.value=t.key,class:y([d.value===t.key?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"])},[q(n(t.name)+" ",1),t.count?(o(),a("span",Q,n(t.count),1)):m("",!0)],10,K))),128))])]),d.value==="email"?(o(),a("div",W,[e("div",X,[e("div",{class:"flex justify-between items-center"},[s[6]||(s[6]=e("h3",{class:"text-lg font-medium text-gray-900"},"Email Templates",-1)),e("button",{onClick:M,class:"bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"}," New Email Template ")]),e("div",ee,[(o(!0),a(x,null,p(h.value,t=>(o(),a("div",{key:t.id,class:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",te,[e("h4",se,n(t.name),1),e("span",{class:y([t.status==="active"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800","inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(t.status),3)]),e("p",ne,n(t.description),1),e("div",oe,[e("span",null,"Used "+n(t.usage_count)+" times",1),e("span",null,n(g(t.updated_at)),1)]),e("div",ie,[e("button",{onClick:u=>S(t),class:"flex-1 bg-indigo-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-indigo-700"}," Edit ",8,ae),e("button",{onClick:u=>E(t),class:"flex-1 bg-gray-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-gray-700"}," Preview ",8,le)])]))),128))])])])):m("",!0),d.value==="sms"?(o(),a("div",re,[e("div",de,[e("div",{class:"flex justify-between items-center"},[s[7]||(s[7]=e("h3",{class:"text-lg font-medium text-gray-900"},"SMS Templates",-1)),e("button",{onClick:N,class:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," New SMS Template ")]),e("div",ce,[(o(!0),a(x,null,p(_.value,t=>(o(),a("div",{key:t.id,class:"border border-gray-200 rounded-lg p-4"},[e("div",ue,[e("h4",me,n(t.name),1),e("span",pe,n(t.message.length)+"/160 chars",1)]),e("div",xe,[e("p",ge,n(t.message),1)]),e("div",ye,[e("span",null,"Sent "+n(t.sent_count)+" times",1),e("span",null,n(g(t.updated_at)),1)]),e("div",fe,[e("button",{onClick:u=>$(t),class:"flex-1 bg-green-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-green-700"}," Edit ",8,ve),e("button",{onClick:u=>j(t),class:"flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-blue-700"}," Test Send ",8,be)])]))),128))])])])):m("",!0),d.value==="push"?(o(),a("div",he,[e("div",_e,[e("div",{class:"flex justify-between items-center"},[s[8]||(s[8]=e("h3",{class:"text-lg font-medium text-gray-900"},"Push Notifications",-1)),e("button",{onClick:D,class:"bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700"}," Send Push Notification ")]),s[11]||(s[11]=e("div",{class:"bg-yellow-50 border border-yellow-200 rounded-md p-4"},[e("div",{class:"flex"},[e("svg",{class:"w-5 h-5 text-yellow-400 mr-3 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})]),e("div",null,[e("h4",{class:"text-sm font-medium text-yellow-800"},"Push Notification Setup Required"),e("p",{class:"text-sm text-yellow-700 mt-1"},"Configure Firebase Cloud Messaging in Settings to enable push notifications.")])])],-1)),e("div",we,[(o(!0),a(x,null,p(w.value,t=>(o(),a("div",{key:t.id,class:"border border-gray-200 rounded-lg p-4"},[e("div",ke,[e("div",Ce,[e("h4",Te,n(t.title),1),e("p",Se,n(t.message),1),e("div",Ee,[e("span",null,"Sent to "+n(t.recipients)+" users",1),s[9]||(s[9]=e("span",{class:"mx-2"},"•",-1)),e("span",null,n(g(t.sent_at)),1),s[10]||(s[10]=e("span",{class:"mx-2"},"•",-1)),e("span",null,n(t.click_rate)+"% click rate",1)])]),e("span",{class:y([t.status==="sent"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800","inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(t.status),3)])]))),128))])])])):m("",!0),d.value==="campaigns"?(o(),a("div",Me,[e("div",Ne,[e("div",{class:"flex justify-between items-center"},[s[12]||(s[12]=e("h3",{class:"text-lg font-medium text-gray-900"},"Campaign History",-1)),e("button",{onClick:B,class:"bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-orange-700"}," Create Campaign ")]),e("div",$e,[e("table",je,[s[13]||(s[13]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Campaign"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Type"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Recipients"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Sent"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Open Rate"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions")])],-1)),e("tbody",De,[(o(!0),a(x,null,p(k.value,t=>(o(),a("tr",{key:t.id,class:"hover:bg-gray-50"},[e("td",Be,[e("div",null,[e("div",Re,n(t.name),1),e("div",Ve,n(t.subject),1)])]),e("td",Ze,n(t.type),1),e("td",Pe,n(t.recipients.toLocaleString()),1),e("td",ze,n(g(t.sent_at)),1),e("td",Ue,n(t.open_rate)+"% ",1),e("td",Oe,[e("span",{class:y([C(t.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},n(t.status),3)]),e("td",He,[e("button",{onClick:u=>V(t),class:"text-indigo-600 hover:text-indigo-900 mr-3"},"View",8,qe),e("button",{onClick:u=>Z(t),class:"text-green-600 hover:text-green-900"},"Duplicate",8,Fe)])]))),128))])])])])])):m("",!0)]),c.value?(o(),a("div",Le,[e("div",Ae,[e("div",Je,[s[19]||(s[19]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Create New Template",-1)),e("form",{onSubmit:O(T,["prevent"])},[e("div",Ye,[e("div",null,[s[15]||(s[15]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Template Type",-1)),f(e("select",{"onUpdate:modelValue":s[1]||(s[1]=t=>l.type=t),required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},s[14]||(s[14]=[e("option",{value:""},"Select Type",-1),e("option",{value:"email"},"Email Template",-1),e("option",{value:"sms"},"SMS Template",-1)]),512),[[H,l.type]])]),e("div",null,[s[16]||(s[16]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Template Name",-1)),f(e("input",{"onUpdate:modelValue":s[2]||(s[2]=t=>l.name=t),type:"text",required:"",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[v,l.name]])]),e("div",null,[s[17]||(s[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Description",-1)),f(e("textarea",{"onUpdate:modelValue":s[3]||(s[3]=t=>l.description=t),rows:"3",class:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[v,l.description]])])]),e("div",Ge,[e("button",{type:"button",onClick:s[4]||(s[4]=t=>c.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"}," Cancel "),s[18]||(s[18]=e("button",{type:"submit",class:"px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"}," Create Template ",-1))])],32)])])])):m("",!0)])]),_:1}))}};export{Xe as default};
