<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\Event;
use App\Models\Booking;
use App\Models\User;
use App\Models\Payment;

class AnalyticsService
{
    /**
     * Get dashboard overview statistics
     */
    public function getDashboardOverview(array $dateRange = []): array
    {
        $startDate = $dateRange['start'] ?? Carbon::now()->subDays(30);
        $endDate = $dateRange['end'] ?? Carbon::now();

        return [
            'total_events' => Event::count(),
            'active_events' => Event::where('status', 'published')
                                  ->where('end_date', '>=', now())
                                  ->count(),
            'total_bookings' => Booking::whereBetween('created_at', [$startDate, $endDate])->count(),
            'confirmed_bookings' => Booking::where('status', 'confirmed')
                                          ->whereBetween('created_at', [$startDate, $endDate])
                                          ->count(),
            'total_revenue' => Payment::where('status', 'completed')
                                     ->whereBetween('created_at', [$startDate, $endDate])
                                     ->sum('amount'),
            'total_users' => User::count(),
            'new_users' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
            'conversion_rate' => $this->calculateConversionRate($startDate, $endDate),
        ];
    }

    /**
     * Get revenue analytics
     */
    public function getRevenueAnalytics(array $dateRange = [], string $groupBy = 'day'): array
    {
        $startDate = $dateRange['start'] ?? Carbon::now()->subDays(30);
        $endDate = $dateRange['end'] ?? Carbon::now();

        $dateFormat = match($groupBy) {
            'hour' => '%Y-%m-%d %H:00:00',
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'year' => '%Y',
            default => '%Y-%m-%d'
        };

        $revenueData = Payment::select(
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as period"),
                DB::raw('SUM(amount) as total_revenue'),
                DB::raw('COUNT(*) as transaction_count'),
                DB::raw('AVG(amount) as average_amount')
            )
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        return [
            'revenue_data' => $revenueData,
            'total_revenue' => $revenueData->sum('total_revenue'),
            'total_transactions' => $revenueData->sum('transaction_count'),
            'average_transaction' => $revenueData->avg('average_amount'),
            'growth_rate' => $this->calculateRevenueGrowthRate($startDate, $endDate),
        ];
    }

    /**
     * Get event analytics
     */
    public function getEventAnalytics(array $dateRange = []): array
    {
        $startDate = $dateRange['start'] ?? Carbon::now()->subDays(30);
        $endDate = $dateRange['end'] ?? Carbon::now();

        // Top performing events
        $topEvents = Event::select('events.*')
            ->selectRaw('COUNT(bookings.id) as booking_count')
            ->selectRaw('SUM(payments.amount) as total_revenue')
            ->leftJoin('bookings', 'events.id', '=', 'bookings.event_id')
            ->leftJoin('payments', function($join) {
                $join->on('bookings.id', '=', 'payments.booking_id')
                     ->where('payments.status', '=', 'completed');
            })
            ->whereBetween('events.created_at', [$startDate, $endDate])
            ->groupBy('events.id')
            ->orderByDesc('booking_count')
            ->limit(10)
            ->get();

        // Events by category
        $eventsByCategory = Event::select('category_id')
            ->selectRaw('COUNT(*) as event_count')
            ->selectRaw('COUNT(bookings.id) as total_bookings')
            ->leftJoin('bookings', 'events.id', '=', 'bookings.event_id')
            ->whereBetween('events.created_at', [$startDate, $endDate])
            ->groupBy('category_id')
            ->with('category')
            ->get();

        // Event status distribution
        $eventStatusDistribution = Event::select('status')
            ->selectRaw('COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('status')
            ->get();

        return [
            'top_events' => $topEvents,
            'events_by_category' => $eventsByCategory,
            'status_distribution' => $eventStatusDistribution,
            'total_events' => Event::whereBetween('created_at', [$startDate, $endDate])->count(),
            'average_bookings_per_event' => $this->calculateAverageBookingsPerEvent($startDate, $endDate),
        ];
    }

    /**
     * Get booking analytics
     */
    public function getBookingAnalytics(array $dateRange = [], string $groupBy = 'day'): array
    {
        $startDate = $dateRange['start'] ?? Carbon::now()->subDays(30);
        $endDate = $dateRange['end'] ?? Carbon::now();

        $dateFormat = match($groupBy) {
            'hour' => '%Y-%m-%d %H:00:00',
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'year' => '%Y',
            default => '%Y-%m-%d'
        };

        // Booking trends
        $bookingTrends = Booking::select(
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as period"),
                DB::raw('COUNT(*) as total_bookings'),
                DB::raw("COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_bookings"),
                DB::raw("COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_bookings"),
                DB::raw("COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_bookings")
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        // Booking status distribution
        $statusDistribution = Booking::select('status')
            ->selectRaw('COUNT(*) as count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('status')
            ->get();

        return [
            'booking_trends' => $bookingTrends,
            'status_distribution' => $statusDistribution,
            'total_bookings' => Booking::whereBetween('created_at', [$startDate, $endDate])->count(),
            'cancellation_rate' => $this->calculateCancellationRate($startDate, $endDate),
            'average_booking_value' => $this->calculateAverageBookingValue($startDate, $endDate),
        ];
    }

    /**
     * Get user analytics
     */
    public function getUserAnalytics(array $dateRange = []): array
    {
        $startDate = $dateRange['start'] ?? Carbon::now()->subDays(30);
        $endDate = $dateRange['end'] ?? Carbon::now();

        // User registration trends
        $registrationTrends = User::select(
                DB::raw("DATE_FORMAT(created_at, '%Y-%m-%d') as date"),
                DB::raw('COUNT(*) as new_users')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Top users by bookings
        $topUsers = User::select('users.*')
            ->selectRaw('COUNT(bookings.id) as booking_count')
            ->selectRaw('SUM(payments.amount) as total_spent')
            ->leftJoin('bookings', 'users.id', '=', 'bookings.user_id')
            ->leftJoin('payments', function($join) {
                $join->on('bookings.id', '=', 'payments.booking_id')
                     ->where('payments.status', '=', 'completed');
            })
            ->whereBetween('users.created_at', [$startDate, $endDate])
            ->groupBy('users.id')
            ->orderByDesc('booking_count')
            ->limit(10)
            ->get();

        return [
            'registration_trends' => $registrationTrends,
            'top_users' => $topUsers,
            'total_users' => User::count(),
            'new_users' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
            'active_users' => $this->calculateActiveUsers($startDate, $endDate),
            'user_retention_rate' => $this->calculateUserRetentionRate($startDate, $endDate),
        ];
    }

    /**
     * Get payment analytics
     */
    public function getPaymentAnalytics(array $dateRange = []): array
    {
        $startDate = $dateRange['start'] ?? Carbon::now()->subDays(30);
        $endDate = $dateRange['end'] ?? Carbon::now();

        // Payment method distribution
        $paymentMethods = Payment::select('gateway')
            ->selectRaw('COUNT(*) as transaction_count')
            ->selectRaw('SUM(amount) as total_amount')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('gateway')
            ->get();

        // Payment status distribution
        $paymentStatus = Payment::select('status')
            ->selectRaw('COUNT(*) as count')
            ->selectRaw('SUM(amount) as total_amount')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('status')
            ->get();

        return [
            'payment_methods' => $paymentMethods,
            'payment_status' => $paymentStatus,
            'success_rate' => $this->calculatePaymentSuccessRate($startDate, $endDate),
            'average_transaction_time' => $this->calculateAverageTransactionTime($startDate, $endDate),
            'refund_rate' => $this->calculateRefundRate($startDate, $endDate),
        ];
    }

    /**
     * Calculate conversion rate
     */
    protected function calculateConversionRate(Carbon $startDate, Carbon $endDate): float
    {
        $totalBookings = Booking::whereBetween('created_at', [$startDate, $endDate])->count();
        $confirmedBookings = Booking::where('status', 'confirmed')
                                   ->whereBetween('created_at', [$startDate, $endDate])
                                   ->count();

        return $totalBookings > 0 ? round(($confirmedBookings / $totalBookings) * 100, 2) : 0;
    }

    /**
     * Calculate revenue growth rate
     */
    protected function calculateRevenueGrowthRate(Carbon $startDate, Carbon $endDate): float
    {
        $currentPeriodRevenue = Payment::where('status', 'completed')
                                      ->whereBetween('created_at', [$startDate, $endDate])
                                      ->sum('amount');

        $previousPeriodStart = $startDate->copy()->subDays($endDate->diffInDays($startDate));
        $previousPeriodRevenue = Payment::where('status', 'completed')
                                       ->whereBetween('created_at', [$previousPeriodStart, $startDate])
                                       ->sum('amount');

        if ($previousPeriodRevenue > 0) {
            return round((($currentPeriodRevenue - $previousPeriodRevenue) / $previousPeriodRevenue) * 100, 2);
        }

        return 0;
    }

    /**
     * Calculate average bookings per event
     */
    protected function calculateAverageBookingsPerEvent(Carbon $startDate, Carbon $endDate): float
    {
        $totalEvents = Event::whereBetween('created_at', [$startDate, $endDate])->count();
        $totalBookings = Booking::whereBetween('created_at', [$startDate, $endDate])->count();

        return $totalEvents > 0 ? round($totalBookings / $totalEvents, 2) : 0;
    }

    /**
     * Calculate cancellation rate
     */
    protected function calculateCancellationRate(Carbon $startDate, Carbon $endDate): float
    {
        $totalBookings = Booking::whereBetween('created_at', [$startDate, $endDate])->count();
        $cancelledBookings = Booking::where('status', 'cancelled')
                                   ->whereBetween('created_at', [$startDate, $endDate])
                                   ->count();

        return $totalBookings > 0 ? round(($cancelledBookings / $totalBookings) * 100, 2) : 0;
    }

    /**
     * Calculate average booking value
     */
    protected function calculateAverageBookingValue(Carbon $startDate, Carbon $endDate): float
    {
        return Booking::whereBetween('created_at', [$startDate, $endDate])
                     ->avg('total_amount') ?? 0;
    }

    /**
     * Calculate active users
     */
    protected function calculateActiveUsers(Carbon $startDate, Carbon $endDate): int
    {
        return User::whereHas('bookings', function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->count();
    }

    /**
     * Calculate user retention rate
     */
    protected function calculateUserRetentionRate(Carbon $startDate, Carbon $endDate): float
    {
        $newUsers = User::whereBetween('created_at', [$startDate, $endDate])->count();
        $returningUsers = User::whereHas('bookings', function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->where('created_at', '<', $startDate)->count();

        $totalUsers = $newUsers + $returningUsers;
        return $totalUsers > 0 ? round(($returningUsers / $totalUsers) * 100, 2) : 0;
    }

    /**
     * Calculate payment success rate
     */
    protected function calculatePaymentSuccessRate(Carbon $startDate, Carbon $endDate): float
    {
        $totalPayments = Payment::whereBetween('created_at', [$startDate, $endDate])->count();
        $successfulPayments = Payment::where('status', 'completed')
                                    ->whereBetween('created_at', [$startDate, $endDate])
                                    ->count();

        return $totalPayments > 0 ? round(($successfulPayments / $totalPayments) * 100, 2) : 0;
    }

    /**
     * Calculate average transaction time
     */
    protected function calculateAverageTransactionTime(Carbon $startDate, Carbon $endDate): float
    {
        $payments = Payment::select('created_at', 'paid_at')
                          ->where('status', 'completed')
                          ->whereNotNull('paid_at')
                          ->whereBetween('created_at', [$startDate, $endDate])
                          ->get();

        if ($payments->isEmpty()) {
            return 0;
        }

        $totalTime = 0;
        foreach ($payments as $payment) {
            $totalTime += $payment->created_at->diffInSeconds($payment->paid_at);
        }

        return round($totalTime / $payments->count(), 2);
    }

    /**
     * Calculate refund rate
     */
    protected function calculateRefundRate(Carbon $startDate, Carbon $endDate): float
    {
        $totalPayments = Payment::where('status', 'completed')
                               ->whereBetween('created_at', [$startDate, $endDate])
                               ->count();
        $refundedPayments = Payment::where('status', 'refunded')
                                  ->whereBetween('created_at', [$startDate, $endDate])
                                  ->count();

        return $totalPayments > 0 ? round(($refundedPayments / $totalPayments) * 100, 2) : 0;
    }
}
