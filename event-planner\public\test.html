<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Manager - Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://unpkg.com/vue-router@4/dist/vue-router.global.prod.js"></script>
</head>
<body>
    <div id="app">
        <div class="min-h-screen bg-gray-100 flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Loading...</p>
            </div>
        </div>
    </div>

    <script>
        console.log('Test page loaded');
        console.log('Vue available:', typeof Vue !== 'undefined');
        console.log('VueRouter available:', typeof VueRouter !== 'undefined');

        const { createApp } = Vue;
        const { createRouter, createWebHistory } = VueRouter;

        const HomePage = {
            template: `
                <div class="min-h-screen bg-gray-100">
                    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-20">
                        <div class="max-w-7xl mx-auto px-4 text-center">
                            <h1 class="text-5xl font-bold mb-6">🎉 Event Manager Works!</h1>
                            <p class="text-xl mb-8">Vue.js is successfully loaded and running</p>
                            <div class="space-x-4">
                                <button @click="showAlert" class="bg-white text-indigo-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100">Test Button</button>
                            </div>
                            <div class="mt-8">
                                <p class="text-lg">Counter: {{ counter }}</p>
                                <button @click="counter++" class="bg-yellow-500 text-black px-4 py-2 rounded mt-2">Increment</button>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            data() {
                return {
                    counter: 0
                }
            },
            methods: {
                showAlert() {
                    alert('Vue.js is working perfectly!');
                }
            }
        };

        const routes = [
            { path: '/', component: HomePage }
        ];

        const router = createRouter({
            history: createWebHistory(),
            routes
        });

        const app = createApp({
            template: '<router-view></router-view>'
        });

        app.use(router);
        app.mount('#app');

        console.log('Vue app mounted successfully');
    </script>
</body>
</html>
