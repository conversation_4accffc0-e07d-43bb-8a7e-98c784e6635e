import{r as i,c as w,l as I,w as Z,h as e,f as n,i as b,t as a,k as x,F as m,n as _,x as N,y as E,g as l}from"./vue-vendor-BupLktX_.js";import{_ as P}from"./admin-9F2yeZU0.js";import"./chart-vendor-Db3utXXw.js";const O={class:"space-y-6"},J={class:"flex justify-between items-center"},K={class:"flex space-x-3"},Q={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},q={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},R={class:"flex items-center"},W={class:"ml-4"},X={class:"text-2xl font-bold text-gray-900"},Y={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},ee={class:"flex items-center"},te={class:"ml-4"},se={class:"text-2xl font-bold text-gray-900"},oe={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},le={class:"flex items-center"},ne={class:"ml-4"},ae={class:"text-2xl font-bold text-gray-900"},re={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},ie={class:"flex items-center"},de={class:"ml-4"},ue={class:"text-2xl font-bold text-gray-900"},ce={class:"bg-white shadow-sm rounded-lg border border-gray-200"},pe={class:"px-6 py-4 border-b border-gray-200"},ge={class:"flex justify-between items-center"},ve={class:"flex items-center space-x-4"},xe={class:"flex","aria-label":"Breadcrumb"},me={class:"flex items-center space-x-2"},he=["onClick"],fe={class:"flex items-center space-x-4"},ye={class:"flex border border-gray-300 rounded-md"},we={class:"relative"},be={key:0,class:"p-6"},_e={class:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4"},ke=["onClick"],Ce={class:"text-center"},Fe={class:"mt-2 text-sm font-medium text-gray-900 truncate"},Me={class:"text-xs text-gray-500"},Be=["onClick"],ze={class:"text-center"},Le={key:0,class:"mx-auto h-12 w-12 bg-gray-100 rounded overflow-hidden"},Ve=["src","alt"],je={key:1,class:"mx-auto h-12 w-12 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},De={class:"mt-2 text-sm font-medium text-gray-900 truncate"},$e={class:"text-xs text-gray-500"},He={key:1,class:"overflow-x-auto"},Se={class:"min-w-full divide-y divide-gray-200"},Te={class:"bg-gray-50"},Ue={class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},Ae={class:"bg-white divide-y divide-gray-200"},Ge={class:"px-6 py-4 whitespace-nowrap"},Ie=["checked","onChange"],Ze={class:"px-6 py-4 whitespace-nowrap"},Ne={class:"flex items-center"},Ee={key:0,class:"w-5 h-5 text-blue-500 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},Pe={key:1,class:"w-8 h-8 bg-gray-100 rounded mr-3 overflow-hidden"},Oe=["src","alt"],Je={key:2,class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},Ke={class:"text-sm font-medium text-gray-900"},Qe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},qe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Re={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},We={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Xe=["onClick"],Ye=["onClick"],et={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},tt={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white"},st={class:"mt-3"},ot={class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},lt={class:"mt-4"},nt={class:"cursor-pointer"},at={class:"flex justify-end space-x-3 mt-6"},ct={__name:"MediaLibrary",setup(rt){const p=i("grid"),u=i(""),h=i("/"),r=i([]),v=i(!1),M=i(247),B=i(156),z=i(91),L=i("2.3 GB"),g=i([{id:1,name:"Event Banners",path:"/event-banners",items:45,type:"folder",modified_at:"2025-01-10T10:30:00Z"},{id:2,name:"Documents",path:"/documents",items:23,type:"folder",modified_at:"2025-01-08T15:45:00Z"},{id:3,name:"Logos",path:"/logos",items:12,type:"folder",modified_at:"2025-01-05T09:20:00Z"}]),c=i([{id:1,name:"tech-conference-banner.jpg",type:"image",extension:"jpg",size:2048576,thumbnail:"https://via.placeholder.com/150x150/4F46E5/FFFFFF?text=IMG",modified_at:"2025-01-12T10:30:00Z"},{id:2,name:"event-guidelines.pdf",type:"document",extension:"pdf",size:1024e3,modified_at:"2025-01-11T14:20:00Z"},{id:3,name:"music-festival-poster.png",type:"image",extension:"png",size:3145728,thumbnail:"https://via.placeholder.com/150x150/10B981/FFFFFF?text=IMG",modified_at:"2025-01-10T16:45:00Z"}]),V=w(()=>{const o=h.value.split("/").filter(Boolean);return o.map((t,s)=>({name:t,path:"/"+o.slice(0,s+1).join("/")}))}),k=w(()=>u.value?g.value.filter(o=>o.name.toLowerCase().includes(u.value.toLowerCase())):g.value),C=w(()=>u.value?c.value.filter(o=>o.name.toLowerCase().includes(u.value.toLowerCase())):c.value),F=o=>{if(o===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB"],d=Math.floor(Math.log(o)/Math.log(t));return parseFloat((o/Math.pow(t,d)).toFixed(2))+" "+s[d]},j=o=>new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),f=o=>{h.value=o},D=o=>{const t=r.value.indexOf(o.id);t>-1?r.value.splice(t,1):r.value.push(o.id)},$=o=>{const t=r.value.indexOf(o);t>-1?r.value.splice(t,1):r.value.push(o)},H=()=>{r.value.length===c.value.length?r.value=[]:r.value=c.value.map(o=>o.id)},S=o=>{const t=Array.from(o.target.files);console.log("Files to upload:",t)},T=()=>{v.value=!1,alert("Files uploaded successfully!")},U=o=>{alert(`Downloading: ${o.name}`)},A=o=>{if(confirm(`Delete ${o.name}?`))if(o.type==="folder"){const t=g.value.findIndex(s=>s.id===o.id);t>-1&&g.value.splice(t,1)}else{const t=c.value.findIndex(s=>s.id===o.id);t>-1&&c.value.splice(t,1)}},G=()=>{const o=prompt("Enter folder name:");o&&g.value.push({id:Date.now(),name:o,path:h.value+"/"+o,items:0,type:"folder",modified_at:new Date().toISOString()})};return(o,t)=>(l(),I(P,null,{default:Z(()=>[e("div",O,[e("div",J,[t[6]||(t[6]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Media Library"),e("p",{class:"text-gray-600"},"Manage event images, documents, and media files")],-1)),e("div",K,[e("button",{onClick:t[0]||(t[0]=s=>v.value=!0),class:"bg-indigo-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-indigo-700 transition-colors"}," Upload Files "),e("button",{onClick:G,class:"bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors"}," New Folder ")])]),e("div",Q,[e("div",q,[e("div",R,[t[8]||(t[8]=e("div",{class:"p-2 bg-blue-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"})])],-1)),e("div",W,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Files",-1)),e("p",X,a(M.value),1)])])]),e("div",Y,[e("div",ee,[t[10]||(t[10]=e("div",{class:"p-2 bg-green-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",te,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600"},"Images",-1)),e("p",se,a(B.value),1)])])]),e("div",oe,[e("div",le,[t[12]||(t[12]=e("div",{class:"p-2 bg-purple-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e("div",ne,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600"},"Documents",-1)),e("p",ae,a(z.value),1)])])]),e("div",re,[e("div",ie,[t[14]||(t[14]=e("div",{class:"p-2 bg-orange-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"})])],-1)),e("div",de,[t[13]||(t[13]=e("p",{class:"text-sm font-medium text-gray-600"},"Storage Used",-1)),e("p",ue,a(L.value),1)])])])]),e("div",ce,[e("div",pe,[e("div",ge,[e("div",ve,[e("nav",xe,[e("ol",me,[e("li",null,[e("button",{onClick:t[1]||(t[1]=s=>f("/")),class:"text-gray-500 hover:text-gray-700"},t[15]||(t[15]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"})],-1)]))]),(l(!0),n(m,null,x(V.value,(s,d)=>(l(),n("li",{key:d,class:"flex items-center"},[t[16]||(t[16]=e("svg",{class:"w-5 h-5 text-gray-400 mx-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)),e("button",{onClick:y=>f(s.path),class:"text-sm font-medium text-gray-500 hover:text-gray-700"},a(s.name),9,he)]))),128))])])]),e("div",fe,[e("div",ye,[e("button",{onClick:t[2]||(t[2]=s=>p.value="grid"),class:_([p.value==="grid"?"bg-gray-100":"","p-2 text-gray-500 hover:text-gray-700"])},t[17]||(t[17]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})],-1)]),2),e("button",{onClick:t[3]||(t[3]=s=>p.value="list"),class:_([p.value==="list"?"bg-gray-100":"","p-2 text-gray-500 hover:text-gray-700"])},t[18]||(t[18]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)]),2)]),e("div",we,[N(e("input",{"onUpdate:modelValue":t[4]||(t[4]=s=>u.value=s),type:"text",placeholder:"Search files...",class:"w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,512),[[E,u.value]]),t[19]||(t[19]=e("svg",{class:"absolute left-3 top-2.5 h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])])])]),p.value==="grid"?(l(),n("div",be,[e("div",_e,[(l(!0),n(m,null,x(k.value,s=>(l(),n("div",{key:s.id,onClick:d=>f(s.path),class:"group cursor-pointer p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"},[e("div",Ce,[t[20]||(t[20]=e("svg",{class:"mx-auto h-12 w-12 text-blue-500 group-hover:text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"})],-1)),e("p",Fe,a(s.name),1),e("p",Me,a(s.items)+" items",1)])],8,ke))),128)),(l(!0),n(m,null,x(C.value,s=>(l(),n("div",{key:s.id,onClick:d=>D(s),class:_([r.value.includes(s.id)?"ring-2 ring-indigo-500":"","group cursor-pointer p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"])},[e("div",ze,[s.type==="image"?(l(),n("div",Le,[e("img",{src:s.thumbnail,alt:s.name,class:"w-full h-full object-cover"},null,8,Ve)])):(l(),n("svg",je,t[21]||(t[21]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"},null,-1)]))),e("p",De,a(s.name),1),e("p",$e,a(F(s.size)),1)])],10,Be))),128))])])):(l(),n("div",He,[e("table",Se,[e("thead",Te,[e("tr",null,[e("th",Ue,[e("input",{type:"checkbox",onChange:H,class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,32)]),t[22]||(t[22]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Name",-1)),t[23]||(t[23]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Type",-1)),t[24]||(t[24]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Size",-1)),t[25]||(t[25]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Modified",-1)),t[26]||(t[26]=e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"},"Actions",-1))])]),e("tbody",Ae,[(l(!0),n(m,null,x([...k.value,...C.value],s=>{var d;return l(),n("tr",{key:s.id,class:"hover:bg-gray-50"},[e("td",Ge,[s.type!=="folder"?(l(),n("input",{key:0,type:"checkbox",checked:r.value.includes(s.id),onChange:y=>$(s.id),class:"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"},null,40,Ie)):b("",!0)]),e("td",Ze,[e("div",Ne,[s.type==="folder"?(l(),n("svg",Ee,t[27]||(t[27]=[e("path",{d:"M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"},null,-1)]))):s.type==="image"?(l(),n("div",Pe,[e("img",{src:s.thumbnail,alt:s.name,class:"w-full h-full object-cover"},null,8,Oe)])):(l(),n("svg",Je,t[28]||(t[28]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z","clip-rule":"evenodd"},null,-1)]))),e("span",Ke,a(s.name),1)])]),e("td",Qe,a(s.type==="folder"?"Folder":((d=s.extension)==null?void 0:d.toUpperCase())||"File"),1),e("td",qe,a(s.type==="folder"?`${s.items} items`:F(s.size)),1),e("td",Re,a(j(s.modified_at)),1),e("td",We,[s.type!=="folder"?(l(),n("button",{key:0,onClick:y=>U(s),class:"text-indigo-600 hover:text-indigo-900 mr-3"},"Download",8,Xe)):b("",!0),e("button",{onClick:y=>A(s),class:"text-red-600 hover:text-red-900"},"Delete",8,Ye)])])}),128))])])]))]),v.value?(l(),n("div",et,[e("div",tt,[e("div",st,[t[32]||(t[32]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Upload Files",-1)),e("div",ot,[t[30]||(t[30]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48"},[e("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),e("div",lt,[e("label",nt,[t[29]||(t[29]=e("span",{class:"mt-2 block text-sm font-medium text-gray-900"}," Drop files here or click to upload ",-1)),e("input",{type:"file",multiple:"",class:"hidden",onChange:S},null,32)])]),t[31]||(t[31]=e("p",{class:"mt-2 text-xs text-gray-500"},"PNG, JPG, PDF up to 10MB each",-1))]),e("div",at,[e("button",{onClick:t[5]||(t[5]=s=>v.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"}," Cancel "),e("button",{onClick:T,class:"px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"}," Upload ")])])])])):b("",!0)])]),_:1}))}};export{ct as default};
