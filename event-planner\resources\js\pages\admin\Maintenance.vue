<template>
  <AdminLayout>
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">System Maintenance</h1>
          <p class="text-gray-600">Database backups, data exports, and system maintenance tools</p>
        </div>
        <div class="flex space-x-3">
          <button @click="runSystemCheck" 
                  class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            System Check
          </button>
          <button @click="createBackup" 
                  class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors">
            Create Backup
          </button>
        </div>
      </div>

      <!-- System Status -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Database</p>
              <p class="text-2xl font-bold text-green-600">Healthy</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v4a2 2 0 01-2 2H5z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Storage</p>
              <p class="text-2xl font-bold text-gray-900">{{ storageUsed }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Last Backup</p>
              <p class="text-2xl font-bold text-gray-900">{{ lastBackup }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-orange-100 rounded-lg">
              <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Uptime</p>
              <p class="text-2xl font-bold text-gray-900">{{ systemUptime }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Backup Management -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Database Backups</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Backup Settings -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-4">Backup Settings</h4>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Automatic Backup Schedule</label>
                  <select v-model="backupSchedule" 
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="disabled">Disabled</option>
                    <option value="daily">Daily at 2:00 AM</option>
                    <option value="weekly">Weekly (Sunday 2:00 AM)</option>
                    <option value="monthly">Monthly (1st day 2:00 AM)</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Retention Period</label>
                  <select v-model="retentionPeriod" 
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="7">7 days</option>
                    <option value="30">30 days</option>
                    <option value="90">90 days</option>
                    <option value="365">1 year</option>
                  </select>
                </div>

                <div class="flex items-center">
                  <input v-model="compressBackups" type="checkbox" 
                         class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                  <label class="ml-2 text-sm text-gray-700">Compress backups</label>
                </div>

                <div class="flex items-center">
                  <input v-model="encryptBackups" type="checkbox" 
                         class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                  <label class="ml-2 text-sm text-gray-700">Encrypt backups</label>
                </div>

                <button @click="saveBackupSettings" 
                        class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md font-medium hover:bg-indigo-700">
                  Save Settings
                </button>
              </div>
            </div>

            <!-- Recent Backups -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-4">Recent Backups</h4>
              <div class="space-y-3">
                <div v-for="backup in recentBackups" :key="backup.id" 
                     class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div>
                    <p class="text-sm font-medium text-gray-900">{{ backup.filename }}</p>
                    <p class="text-xs text-gray-500">{{ formatDate(backup.created_at) }} • {{ formatFileSize(backup.size) }}</p>
                  </div>
                  <div class="flex space-x-2">
                    <button @click="downloadBackup(backup)" 
                            class="text-indigo-600 hover:text-indigo-900 text-sm">Download</button>
                    <button @click="deleteBackup(backup)" 
                            class="text-red-600 hover:text-red-900 text-sm">Delete</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Data Export -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Data Export</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div v-for="exportType in exportTypes" :key="exportType.key" 
                 class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center mb-3">
                <div :class="exportType.color" class="p-2 rounded-lg mr-3">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                  </svg>
                </div>
                <div>
                  <h4 class="text-md font-medium text-gray-900">{{ exportType.name }}</h4>
                  <p class="text-sm text-gray-500">{{ exportType.description }}</p>
                </div>
              </div>
              <div class="space-y-3">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Format</label>
                  <select v-model="exportType.format" 
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option v-for="format in exportType.formats" :key="format" :value="format">
                      {{ format.toUpperCase() }}
                    </option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                  <select v-model="exportType.dateRange" 
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="all">All Time</option>
                    <option value="30d">Last 30 Days</option>
                    <option value="90d">Last 90 Days</option>
                    <option value="1y">Last Year</option>
                    <option value="custom">Custom Range</option>
                  </select>
                </div>
                <button @click="exportData(exportType)" 
                        class="w-full bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                  Export {{ exportType.name }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- System Maintenance -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">System Maintenance</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Cache Management -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-4">Cache Management</h4>
              <div class="space-y-3">
                <div class="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
                  <div>
                    <p class="text-sm font-medium text-gray-900">Application Cache</p>
                    <p class="text-xs text-gray-500">{{ appCacheSize }} • Last cleared {{ lastCacheClear }}</p>
                  </div>
                  <button @click="clearCache('app')" 
                          class="bg-yellow-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-yellow-700">
                    Clear
                  </button>
                </div>

                <div class="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
                  <div>
                    <p class="text-sm font-medium text-gray-900">Database Query Cache</p>
                    <p class="text-xs text-gray-500">{{ dbCacheSize }} • Auto-managed</p>
                  </div>
                  <button @click="clearCache('db')" 
                          class="bg-yellow-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-yellow-700">
                    Clear
                  </button>
                </div>

                <div class="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
                  <div>
                    <p class="text-sm font-medium text-gray-900">Image Cache</p>
                    <p class="text-xs text-gray-500">{{ imageCacheSize }} • Thumbnails & resized images</p>
                  </div>
                  <button @click="clearCache('images')" 
                          class="bg-yellow-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-yellow-700">
                    Clear
                  </button>
                </div>

                <button @click="clearAllCache" 
                        class="w-full bg-red-600 text-white px-4 py-2 rounded-md font-medium hover:bg-red-700">
                  Clear All Cache
                </button>
              </div>
            </div>

            <!-- Database Optimization -->
            <div>
              <h4 class="text-md font-medium text-gray-900 mb-4">Database Optimization</h4>
              <div class="space-y-3">
                <div class="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
                  <div>
                    <p class="text-sm font-medium text-gray-900">Optimize Tables</p>
                    <p class="text-xs text-gray-500">Defragment and optimize database tables</p>
                  </div>
                  <button @click="optimizeTables" 
                          class="bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-blue-700">
                    Optimize
                  </button>
                </div>

                <div class="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
                  <div>
                    <p class="text-sm font-medium text-gray-900">Clean Old Logs</p>
                    <p class="text-xs text-gray-500">Remove logs older than 90 days</p>
                  </div>
                  <button @click="cleanOldLogs" 
                          class="bg-orange-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-orange-700">
                    Clean
                  </button>
                </div>

                <div class="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
                  <div>
                    <p class="text-sm font-medium text-gray-900">Update Statistics</p>
                    <p class="text-xs text-gray-500">Refresh database query statistics</p>
                  </div>
                  <button @click="updateStatistics" 
                          class="bg-purple-600 text-white px-3 py-1 rounded text-sm font-medium hover:bg-purple-700">
                    Update
                  </button>
                </div>

                <button @click="runFullMaintenance" 
                        class="w-full bg-green-600 text-white px-4 py-2 rounded-md font-medium hover:bg-green-700">
                  Run Full Maintenance
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import AdminLayout from '@/components/admin/AdminLayout.vue'

const storageUsed = ref('2.3 GB')
const lastBackup = ref('2 hours ago')
const systemUptime = ref('15 days')
const appCacheSize = ref('45 MB')
const dbCacheSize = ref('128 MB')
const imageCacheSize = ref('1.2 GB')
const lastCacheClear = ref('3 days ago')

const backupSchedule = ref('daily')
const retentionPeriod = ref('30')
const compressBackups = ref(true)
const encryptBackups = ref(false)

const recentBackups = ref([
  {
    id: 1,
    filename: 'backup_2025-01-12_02-00.sql.gz',
    size: 45678901,
    created_at: '2025-01-12T02:00:00Z'
  },
  {
    id: 2,
    filename: 'backup_2025-01-11_02-00.sql.gz',
    size: 44567890,
    created_at: '2025-01-11T02:00:00Z'
  },
  {
    id: 3,
    filename: 'backup_2025-01-10_02-00.sql.gz',
    size: 43456789,
    created_at: '2025-01-10T02:00:00Z'
  }
])

const exportTypes = reactive([
  {
    key: 'events',
    name: 'Events Data',
    description: 'All event information and details',
    color: 'bg-blue-500',
    formats: ['csv', 'json', 'xlsx'],
    format: 'csv',
    dateRange: '30d'
  },
  {
    key: 'bookings',
    name: 'Bookings Data',
    description: 'Booking records and transactions',
    color: 'bg-green-500',
    formats: ['csv', 'json', 'xlsx'],
    format: 'csv',
    dateRange: '30d'
  },
  {
    key: 'users',
    name: 'User Data',
    description: 'User profiles and information',
    color: 'bg-purple-500',
    formats: ['csv', 'json'],
    format: 'csv',
    dateRange: 'all'
  },
  {
    key: 'analytics',
    name: 'Analytics Data',
    description: 'Performance metrics and statistics',
    color: 'bg-orange-500',
    formats: ['csv', 'json', 'xlsx'],
    format: 'xlsx',
    dateRange: '90d'
  },
  {
    key: 'financial',
    name: 'Financial Reports',
    description: 'Revenue and payment data',
    color: 'bg-red-500',
    formats: ['csv', 'xlsx', 'pdf'],
    format: 'xlsx',
    dateRange: '1y'
  },
  {
    key: 'logs',
    name: 'System Logs',
    description: 'Application and error logs',
    color: 'bg-gray-500',
    formats: ['txt', 'json'],
    format: 'txt',
    dateRange: '30d'
  }
])

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const runSystemCheck = () => {
  alert('Running comprehensive system check...')
}

const createBackup = () => {
  alert('Creating database backup...')
}

const saveBackupSettings = () => {
  alert('Backup settings saved successfully!')
}

const downloadBackup = (backup) => {
  alert(`Downloading backup: ${backup.filename}`)
}

const deleteBackup = (backup) => {
  if (confirm(`Delete backup ${backup.filename}?`)) {
    const index = recentBackups.value.findIndex(b => b.id === backup.id)
    if (index > -1) {
      recentBackups.value.splice(index, 1)
    }
  }
}

const exportData = (exportType) => {
  alert(`Exporting ${exportType.name} as ${exportType.format.toUpperCase()}...`)
}

const clearCache = (type) => {
  alert(`Clearing ${type} cache...`)
}

const clearAllCache = () => {
  if (confirm('Clear all cache? This may temporarily slow down the system.')) {
    alert('All cache cleared successfully!')
  }
}

const optimizeTables = () => {
  alert('Optimizing database tables...')
}

const cleanOldLogs = () => {
  if (confirm('Delete logs older than 90 days?')) {
    alert('Old logs cleaned successfully!')
  }
}

const updateStatistics = () => {
  alert('Updating database statistics...')
}

const runFullMaintenance = () => {
  if (confirm('Run full system maintenance? This may take several minutes.')) {
    alert('Full maintenance started. You will be notified when complete.')
  }
}
</script>
