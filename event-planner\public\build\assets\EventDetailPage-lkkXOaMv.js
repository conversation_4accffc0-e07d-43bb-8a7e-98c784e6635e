import{f as d,i as j,h as t,p as E,t as l,w as h,m as b,e as D,F as B,k as C,l as P,r as g,B as S,c as y,o as q,u as N,a as M,g as a}from"./vue-vendor-BupLktX_.js";import{a as V}from"./utils-vendor-Dq7h7Pqt.js";import{_ as F}from"./_plugin-vue_export-helper-DlAUqK2U.js";const L={name:"EventDetailPage",setup(){const u=N();M();const s=g(!0),m=g(null),e=g(null),c=S({}),f=y(()=>Object.values(c).reduce((o,n)=>o+(n||0),0)),_=y(()=>{var o;return(o=e.value)!=null&&o.ticket_types?e.value.ticket_types.reduce((n,r)=>{const v=c[r.id]||0;return n+v*r.price},0):0}),i=async()=>{var o,n;try{s.value=!0;const r=await V.get(`/api/events/${u.params.slug}`);e.value=r.data.data}catch(r){m.value=((n=(o=r.response)==null?void 0:o.data)==null?void 0:n.message)||"Event not found"}finally{s.value=!1}},x=o=>new Date(o).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),p=()=>{var o,n;return(n=(o=e.value)==null?void 0:o.ticket_types)!=null&&n.length?Math.min(...e.value.ticket_types.map(r=>r.price)):0},w=o=>{const n=e.value.ticket_types.find(v=>v.id===o);if(!n)return;const r=c[o]||0;r<n.max_quantity_per_order&&r<n.quantity_remaining&&(c[o]=r+1)},k=o=>{const n=c[o]||0;n>0&&(c[o]=n-1)};return q(()=>{i()}),{loading:s,error:m,event:e,selectedTickets:c,totalTickets:f,totalPrice:_,formatDate:x,getMinPrice:p,incrementTicket:w,decrementTicket:k}}},Q={class:"min-h-screen bg-gray-900"},R={key:0,class:"flex items-center justify-center min-h-screen"},T={key:1,class:"flex items-center justify-center min-h-screen"},A={class:"text-center"},O={class:"text-gray-400 mb-6"},U={key:2,class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},z={class:"bg-gray-800 rounded-xl overflow-hidden mb-8"},G={class:"relative h-64 md:h-96"},H=["src","alt"],J={key:1,class:"w-full h-full bg-gradient-to-r from-pink-500 to-cyan-500 flex items-center justify-center"},K={class:"text-4xl font-bold text-white text-center"},W={class:"absolute bottom-0 left-0 right-0 p-6"},X={class:"text-3xl md:text-4xl font-bold text-white mb-2"},Y={class:"text-gray-200"},Z={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},I={class:"lg:col-span-2"},$={class:"bg-gray-800 rounded-xl p-6 mb-6"},tt={class:"space-y-4"},et={class:"flex items-center text-gray-300"},st={class:"flex items-center text-gray-300"},ot={class:"flex items-center text-gray-300"},nt={class:"bg-gray-800 rounded-xl p-6"},it={class:"text-gray-300 prose prose-invert max-w-none"},lt={class:"lg:col-span-1"},rt={class:"bg-gray-800 rounded-xl p-6 sticky top-8"},at={class:"space-y-4 mb-6"},dt={class:"flex justify-between items-start mb-2"},ct={class:"font-semibold text-white"},_t={class:"text-sm text-gray-400"},mt={class:"text-right"},xt={class:"text-lg font-bold text-white"},vt={class:"flex items-center justify-between"},gt={class:"flex items-center space-x-3"},ut=["onClick","disabled"],ft={class:"w-8 text-center text-white font-semibold"},ht=["onClick","disabled"],bt={class:"text-sm text-gray-400"},yt={class:"border-t border-gray-700 pt-4"},pt={class:"flex justify-between items-center mb-4"},wt={class:"text-xl font-bold text-white"},kt={key:1,disabled:"",class:"w-full bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold opacity-50 cursor-not-allowed"};function jt(u,s,m,e,c,f){const _=D("router-link");return a(),d("div",Q,[e.loading?(a(),d("div",R,s[0]||(s[0]=[t("div",{class:"text-center"},[t("div",{class:"loader"}),t("p",{class:"text-white mt-4"},"Loading event details...")],-1)]))):e.error?(a(),d("div",T,[t("div",A,[s[2]||(s[2]=t("h2",{class:"text-2xl font-bold text-white mb-2"},"Event Not Found",-1)),t("p",O,l(e.error),1),E(_,{to:"/events",class:"bg-pink-500 text-white px-6 py-2 rounded-lg hover:bg-pink-600 transition-colors"},{default:h(()=>s[1]||(s[1]=[b(" Browse Events ")])),_:1,__:[1]})])])):e.event?(a(),d("div",U,[t("div",z,[t("div",G,[e.event.featured_image?(a(),d("img",{key:0,src:e.event.featured_image,alt:e.event.title,class:"w-full h-full object-cover"},null,8,H)):(a(),d("div",J,[t("h1",K,l(e.event.title),1)])),s[3]||(s[3]=t("div",{class:"absolute inset-0 bg-black/50"},null,-1)),t("div",W,[t("h1",X,l(e.event.title),1),t("p",Y,l(e.event.short_description),1)])])]),t("div",Z,[t("div",I,[t("div",$,[s[7]||(s[7]=t("h2",{class:"text-2xl font-bold text-white mb-4"},"Event Details",-1)),t("div",tt,[t("div",et,[s[4]||(s[4]=t("span",{class:"w-5 h-5 mr-3"},"📅",-1)),t("span",null,l(e.formatDate(e.event.start_date)),1)]),t("div",st,[s[5]||(s[5]=t("span",{class:"w-5 h-5 mr-3"},"📍",-1)),t("span",null,l(e.event.venue_name),1)]),t("div",ot,[s[6]||(s[6]=t("span",{class:"w-5 h-5 mr-3"},"💰",-1)),t("span",null,"Starting from ₹"+l(e.getMinPrice()),1)])])]),t("div",nt,[s[8]||(s[8]=t("h3",{class:"text-xl font-bold text-white mb-4"},"About This Event",-1)),t("div",it,[t("p",null,l(e.event.description),1)])])]),t("div",lt,[t("div",rt,[s[11]||(s[11]=t("h3",{class:"text-xl font-bold text-white mb-6"},"Select Tickets",-1)),t("div",at,[(a(!0),d(B,null,C(e.event.ticket_types,i=>(a(),d("div",{key:i.id,class:"border border-gray-700 rounded-lg p-4"},[t("div",dt,[t("div",null,[t("h4",ct,l(i.name),1),t("p",_t,l(i.description),1)]),t("div",mt,[t("div",xt,"₹"+l(i.price),1)])]),t("div",vt,[t("div",gt,[t("button",{onClick:x=>e.decrementTicket(i.id),disabled:!e.selectedTickets[i.id]||e.selectedTickets[i.id]<=0,class:"w-8 h-8 rounded-full bg-gray-700 text-white flex items-center justify-center hover:bg-gray-600 disabled:opacity-50"}," - ",8,ut),t("span",ft,l(e.selectedTickets[i.id]||0),1),t("button",{onClick:x=>e.incrementTicket(i.id),disabled:(e.selectedTickets[i.id]||0)>=i.max_quantity_per_order,class:"w-8 h-8 rounded-full bg-gray-700 text-white flex items-center justify-center hover:bg-gray-600 disabled:opacity-50"}," + ",8,ht)]),t("div",bt,l(i.quantity_remaining)+" left ",1)])]))),128))]),t("div",yt,[t("div",pt,[s[9]||(s[9]=t("span",{class:"text-white font-semibold"},"Total:",-1)),t("span",wt,"₹"+l(e.totalPrice),1)]),e.totalTickets>0?(a(),P(_,{key:0,to:`/booking/${e.event.id}`,class:"w-full bg-gradient-to-r from-pink-500 to-cyan-500 text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all block text-center"},{default:h(()=>s[10]||(s[10]=[b(" Proceed to Book ")])),_:1,__:[10]},8,["to"])):(a(),d("button",kt," Select Tickets "))])])])])])):j("",!0)])}const Ct=F(L,[["render",jt],["__scopeId","data-v-e309de62"]]);export{Ct as default};
