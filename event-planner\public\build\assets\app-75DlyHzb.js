const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/HomePage-Cn35KqR9.js","assets/vue-vendor-BupLktX_.js","assets/admin-9F2yeZU0.js","assets/chart-vendor-Db3utXXw.js","assets/api-D9eN0dN9.js","assets/utils-vendor-Dq7h7Pqt.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/HomePage-Cwj8IFMr.css","assets/EventsPage-CLXayILa.js","assets/EventsPage-BdyEXmNw.css","assets/EventDetailPage-lkkXOaMv.js","assets/EventDetailPage-C5TcrzmS.css","assets/BookingPage-DNgqFchd.js","assets/booking-fYtx-gxu.js","assets/DashboardPage-CcQ_EBRO.js","assets/MyTicketsPage-ClgXSUn3.js","assets/LoginPage-CBHyC_-I.js","assets/RegisterPage-Do8Ih0qf.js","assets/Roles-C48kIWc_.js","assets/Scanner-CsxQNhyv.js","assets/Reports-Bq4Ns-cY.js","assets/CreateEvent-D6HWcHEH.js","assets/Notifications-jZnhivya.js","assets/SystemLogs-C7R7Uldw.js","assets/MediaLibrary-C3U3Ox7o.js","assets/Maintenance-Bo2P4v9G.js"])))=>i.map(i=>d[i]);
import{a as T}from"./utils-vendor-Dq7h7Pqt.js";import{D as I,E as S,c as L,f as A,i as x,g,h as i,r as V,u as O,o as B,p as _,w as c,e as y,n as C,m as f,l as b,S as M,v as U,G as z,H as $}from"./vue-vendor-BupLktX_.js";import{u as R}from"./admin-9F2yeZU0.js";import"./chart-vendor-Db3utXXw.js";window.axios=T;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";const j="modulepreload",H=function(e){return"/build/"+e},q={},n=function(u,r,d){let s=Promise.resolve();if(r&&r.length>0){let t=function(a){return Promise.all(a.map(p=>Promise.resolve(p).then(h=>({status:"fulfilled",value:h}),h=>({status:"rejected",reason:h}))))};document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),v=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));s=t(r.map(a=>{if(a=H(a),a in q)return;q[a]=!0;const p=a.endsWith(".css"),h=p?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${h}`))return;const l=document.createElement("link");if(l.rel=p?"stylesheet":j,p||(l.as="script"),l.crossOrigin="",l.href=a,v&&l.setAttribute("nonce",v),document.head.appendChild(l),p)return new Promise((w,D)=>{l.addEventListener("load",w),l.addEventListener("error",()=>D(new Error(`Unable to preload CSS for ${a}`)))})}))}function m(t){const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=t,window.dispatchEvent(o),!o.defaultPrevented)throw t}return s.then(t=>{for(const o of t||[])o.status==="rejected"&&m(o.reason);return u().catch(m)})},N=()=>n(()=>import("./HomePage-Cn35KqR9.js"),__vite__mapDeps([0,1,2,3,4,5,6,7])),W=()=>n(()=>import("./EventsPage-CLXayILa.js"),__vite__mapDeps([8,1,5,6,9])),X=()=>n(()=>import("./EventDetailPage-lkkXOaMv.js"),__vite__mapDeps([10,1,5,6,11])),G=()=>n(()=>import("./BookingPage-DNgqFchd.js"),__vite__mapDeps([12,1,2,3,13,4,5])),J=()=>n(()=>import("./DashboardPage-CcQ_EBRO.js"),__vite__mapDeps([14,1,2,3])),F=()=>n(()=>import("./MyTicketsPage-ClgXSUn3.js"),__vite__mapDeps([15,1,13,4,5])),K=()=>n(()=>import("./LoginPage-CBHyC_-I.js"),__vite__mapDeps([16,1,2,3])),Q=()=>n(()=>import("./RegisterPage-Do8Ih0qf.js"),__vite__mapDeps([17,1,2,3])),Y=()=>n(()=>import("./admin-9F2yeZU0.js").then(e=>e.D),__vite__mapDeps([2,1,3])),Z=()=>n(()=>import("./admin-9F2yeZU0.js").then(e=>e.E),__vite__mapDeps([2,1,3])),ee=()=>n(()=>import("./admin-9F2yeZU0.js").then(e=>e.B),__vite__mapDeps([2,1,3])),te=()=>n(()=>import("./admin-9F2yeZU0.js").then(e=>e.U),__vite__mapDeps([2,1,3])),ne=()=>n(()=>import("./Roles-C48kIWc_.js"),__vite__mapDeps([18,1,2,3])),ie=()=>n(()=>import("./admin-9F2yeZU0.js").then(e=>e.S),__vite__mapDeps([2,1,3])),oe=()=>n(()=>import("./Scanner-CsxQNhyv.js"),__vite__mapDeps([19,1,2,3])),re=()=>n(()=>import("./Reports-Bq4Ns-cY.js"),__vite__mapDeps([20,1,2,3])),P=()=>n(()=>import("./CreateEvent-D6HWcHEH.js"),__vite__mapDeps([21,1,2,3,4,5])),ae=()=>n(()=>import("./Notifications-jZnhivya.js"),__vite__mapDeps([22,1,2,3])),se=()=>n(()=>import("./SystemLogs-C7R7Uldw.js"),__vite__mapDeps([23,1,2,3])),me=()=>n(()=>import("./MediaLibrary-C3U3Ox7o.js"),__vite__mapDeps([24,1,2,3])),ue=()=>n(()=>import("./Maintenance-Bo2P4v9G.js"),__vite__mapDeps([25,1,2,3])),de=[{path:"/",name:"home",component:N,meta:{title:"Home"}},{path:"/events",name:"events",component:W,meta:{title:"Events"}},{path:"/events/:slug",name:"event-detail",component:X,meta:{title:"Event Details"}},{path:"/booking/:eventId",name:"booking",component:G,meta:{requiresAuth:!0,title:"Book Event"}},{path:"/my-tickets",name:"my-tickets",component:F,meta:{requiresAuth:!0,title:"My Tickets"}},{path:"/dashboard",name:"dashboard",component:J,meta:{requiresAuth:!0,title:"Dashboard"}},{path:"/login",name:"login",component:K,meta:{title:"Login",guest:!0}},{path:"/register",name:"register",component:Q,meta:{title:"Register",guest:!0}},{path:"/admin",name:"admin-dashboard",component:Y,meta:{requiresAuth:!0,requiresRole:["admin","manager"],title:"Admin Dashboard"}},{path:"/admin/events",name:"admin-events",component:Z,meta:{requiresAuth:!0,requiresRole:["admin","manager"],title:"Events Management"}},{path:"/admin/bookings",name:"admin-bookings",component:ee,meta:{requiresAuth:!0,requiresRole:["admin","manager"],title:"Bookings Management"}},{path:"/admin/users",name:"admin-users",component:te,meta:{requiresAuth:!0,requiresRole:["admin"],title:"User Management"}},{path:"/admin/roles",name:"admin-roles",component:ne,meta:{requiresAuth:!0,requiresRole:["admin"],title:"Role Management"}},{path:"/admin/settings",name:"admin-settings",component:ie,meta:{requiresAuth:!0,requiresRole:["admin"],title:"System Settings"}},{path:"/admin/scanner",name:"admin-scanner",component:oe,meta:{requiresAuth:!0,requiresRole:["admin","manager","scanner"],title:"Ticket Scanner"}},{path:"/admin/reports",name:"admin-reports",component:re,meta:{requiresAuth:!0,requiresRole:["admin","manager"],title:"Reports & Analytics"}},{path:"/admin/events/create",name:"admin-create-event",component:P,meta:{requiresAuth:!0,requiresRole:["admin","manager"],title:"Create Event"}},{path:"/admin/events/:id/edit",name:"admin-edit-event",component:P,meta:{requiresAuth:!0,requiresRole:["admin","manager"],title:"Edit Event"}},{path:"/admin/notifications",name:"admin-notifications",component:ae,meta:{requiresAuth:!0,requiresRole:["admin","manager"],title:"Notifications"}},{path:"/admin/logs",name:"admin-logs",component:se,meta:{requiresAuth:!0,requiresRole:["admin"],title:"System Logs"}},{path:"/admin/media",name:"admin-media",component:me,meta:{requiresAuth:!0,requiresRole:["admin","manager"],title:"Media Library"}},{path:"/admin/maintenance",name:"admin-maintenance",component:ue,meta:{requiresAuth:!0,requiresRole:["admin"],title:"System Maintenance"}}],k=I({history:S(),routes:de,scrollBehavior(e,u,r){return r||{top:0}}});k.beforeEach(async(e,u,r)=>{if(document.title=e.meta.title?`${e.meta.title} - Event Manager`:"Event Manager",e.meta.requiresAuth&&!localStorage.getItem("auth_token")){r({name:"login",query:{redirect:e.fullPath}});return}if(e.meta.requiresRole){const d=localStorage.getItem("user_role")||"attendee";if(!e.meta.requiresRole.includes(d)){r({name:"home"});return}}r()});const le={key:0,class:"fixed top-4 right-4 z-50"},ce={__name:"AdminLoginBypass",setup(e){const u=R(),r=L(()=>u.isAuthenticated),d=()=>{const s={id:1,name:"Admin User",email:"<EMAIL>",role:"admin"},m="admin-demo-token-12345";u.user=s,u.token=m,localStorage.setItem("auth_token",m),localStorage.setItem("user",JSON.stringify(s)),window.location.reload()};return(s,m)=>r.value?x("",!0):(g(),A("div",le,[i("div",{class:"bg-white border border-gray-300 rounded-lg shadow-lg p-4"},[m[0]||(m[0]=i("h3",{class:"text-sm font-medium text-gray-900 mb-2"},"Demo Access",-1)),m[1]||(m[1]=i("p",{class:"text-xs text-gray-600 mb-3"},"Click to access admin panel",-1)),i("button",{onClick:d,class:"w-full bg-indigo-600 text-white px-3 py-2 rounded text-sm font-medium hover:bg-indigo-700"}," Login as Admin ")])]))}},_e={id:"app",class:"min-h-screen bg-gray-50"},pe={key:0,class:"bg-white shadow-sm border-b border-gray-200"},ge={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},he={class:"flex justify-between items-center h-16"},ve={class:"flex items-center"},fe={class:"flex items-center space-x-4"},Ae={key:1,class:"fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50"},Ee={__name:"App",setup(e){const u=O(),r=R(),d=V(!0),s=L(()=>["login","register"].includes(u.name));return B(async()=>{r.initializeAuth(),r.isAuthenticated&&await r.fetchUser(),d.value=!1}),(m,t)=>{const o=y("router-link"),v=y("router-view");return g(),A("div",_e,[s.value?x("",!0):(g(),A("header",pe,[i("nav",ge,[i("div",he,[i("div",ve,[_(o,{to:"/",class:"flex items-center space-x-2"},{default:c(()=>t[0]||(t[0]=[i("div",{class:"w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center"},[i("svg",{class:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[i("path",{d:"M10 2L3 7v11a1 1 0 001 1h3v-6h6v6h3a1 1 0 001-1V7l-7-5z"})])],-1),i("span",{class:"text-xl font-bold text-gray-900"},"EventManager",-1)])),_:1,__:[0]})]),i("div",fe,[_(o,{to:"/",class:"text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium"},{default:c(()=>t[1]||(t[1]=[f("Home")])),_:1,__:[1]}),_(o,{to:"/events",class:"text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium"},{default:c(()=>t[2]||(t[2]=[f("Events")])),_:1,__:[2]}),_(o,{to:"/login",class:"text-gray-700 hover:text-indigo-600 px-3 py-2 text-sm font-medium"},{default:c(()=>t[3]||(t[3]=[f("Login")])),_:1,__:[3]}),_(o,{to:"/register",class:"bg-indigo-600 text-white hover:bg-indigo-700 px-4 py-2 rounded-md text-sm font-medium"},{default:c(()=>t[4]||(t[4]=[f("Sign Up")])),_:1,__:[4]})])])])])),i("main",{class:C({"pt-0":s.value,"pt-16":!s.value})},[_(v,null,{default:c(({Component:a})=>[(g(),b(M,null,{default:c(()=>[(g(),b(U(a)))]),fallback:c(()=>t[5]||(t[5]=[i("div",{class:"flex items-center justify-center min-h-screen"},[i("div",{class:"text-center"},[i("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"}),i("p",{class:"text-gray-600"},"Loading...")])],-1)])),_:2},1024))]),_:1})],2),d.value?(g(),A("div",Ae,t[6]||(t[6]=[i("div",{class:"text-center"},[i("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"}),i("p",{class:"mt-4 text-gray-600"},"Loading...")],-1)]))):x("",!0),_(ce)])}}},E=z(Ee),xe=$();E.use(xe);E.use(k);E.mount("#app");E.config.globalProperties.$nextTick(()=>{try{const e=R();e&&e.initializeAuth&&e.initializeAuth()}catch(e){console.warn("Auth initialization skipped:",e)}});
