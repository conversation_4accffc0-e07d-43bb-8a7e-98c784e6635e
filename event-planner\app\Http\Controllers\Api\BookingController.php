<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Booking;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;

class BookingController extends Controller
{
    /**
     * Get user's bookings
     */
    public function myBookings(Request $request)
    {
        $user = $request->user();
        
        $query = Booking::with(['event', 'ticketType'])
            ->where('user_id', $user->id);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('from_date')) {
            $query->whereHas('event', function ($q) use ($request) {
                $q->where('start_date', '>=', $request->from_date);
            });
        }

        if ($request->filled('to_date')) {
            $query->whereHas('event', function ($q) use ($request) {
                $q->where('start_date', '<=', $request->to_date);
            });
        }

        $bookings = $query->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $bookings
        ]);
    }

    /**
     * Get single booking details
     */
    public function show(Request $request, Booking $booking)
    {
        // Check if user owns this booking
        if ($booking->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $booking->load(['event', 'ticketType', 'user']);

        return response()->json([
            'success' => true,
            'data' => $booking
        ]);
    }

    /**
     * Cancel a booking
     */
    public function cancel(Request $request, Booking $booking)
    {
        // Check if user owns this booking
        if ($booking->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check if booking can be cancelled
        if ($booking->status === 'cancelled') {
            return response()->json([
                'success' => false,
                'message' => 'Booking is already cancelled'
            ], 422);
        }

        // Check if event is not too close (24 hours before)
        $eventDate = $booking->event->start_date;
        $now = now();
        $hoursDiff = $now->diffInHours($eventDate, false);

        if ($hoursDiff < 24) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot cancel booking less than 24 hours before the event'
            ], 422);
        }

        // Cancel the booking
        $booking->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $request->get('reason', 'Cancelled by user')
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Booking cancelled successfully',
            'booking' => $booking->fresh()
        ]);
    }

    /**
     * Download ticket as PDF
     */
    public function downloadTicket(Request $request, Booking $booking)
    {
        // Check if user owns this booking
        if ($booking->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Check if booking is confirmed
        if ($booking->status !== 'confirmed') {
            return response()->json([
                'success' => false,
                'message' => 'Ticket not available for this booking status'
            ], 422);
        }

        $booking->load(['event', 'ticketType', 'user']);

        // Generate QR code data
        $qrData = json_encode([
            'booking_id' => $booking->id,
            'reference' => $booking->booking_reference,
            'event_id' => $booking->event_id,
            'user_id' => $booking->user_id,
            'quantity' => $booking->quantity
        ]);

        // Prepare ticket data
        $ticketData = [
            'booking' => $booking,
            'qr_data' => $qrData,
            'generated_at' => now()->format('Y-m-d H:i:s')
        ];

        try {
            // Generate PDF
            $pdf = Pdf::loadView('tickets.pdf', $ticketData);
            
            $filename = "ticket-{$booking->booking_reference}.pdf";
            
            return $pdf->download($filename);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate ticket PDF'
            ], 500);
        }
    }

    /**
     * Get booking statistics for user
     */
    public function statistics(Request $request)
    {
        $user = $request->user();
        
        $stats = [
            'total_bookings' => Booking::where('user_id', $user->id)->count(),
            'confirmed_bookings' => Booking::where('user_id', $user->id)->where('status', 'confirmed')->count(),
            'cancelled_bookings' => Booking::where('user_id', $user->id)->where('status', 'cancelled')->count(),
            'total_spent' => Booking::where('user_id', $user->id)->where('status', 'confirmed')->sum('total_amount'),
            'upcoming_events' => Booking::where('user_id', $user->id)
                ->where('status', 'confirmed')
                ->whereHas('event', function ($q) {
                    $q->where('start_date', '>', now());
                })
                ->count(),
            'past_events' => Booking::where('user_id', $user->id)
                ->where('status', 'confirmed')
                ->whereHas('event', function ($q) {
                    $q->where('start_date', '<=', now());
                })
                ->count()
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Resend booking confirmation email
     */
    public function resendConfirmation(Request $request, Booking $booking)
    {
        // Check if user owns this booking
        if ($booking->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        if ($booking->status !== 'confirmed') {
            return response()->json([
                'success' => false,
                'message' => 'Can only resend confirmation for confirmed bookings'
            ], 422);
        }

        try {
            // Here you would send the confirmation email
            // Mail::to($booking->attendee_email)->send(new BookingConfirmation($booking));

            return response()->json([
                'success' => true,
                'message' => 'Confirmation email sent successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send confirmation email'
            ], 500);
        }
    }
}
