<?php

// Test script for event management APIs
$baseUrl = 'http://localhost:8000/api/admin';
$token = '1|ra6M9U4sDIYT2PEgsHsW5BnViychQNPq4UiwRu0g2542cfa4';

function makeRequest($url, $method = 'GET', $data = null, $token = null) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    $headers = ['Content-Type: application/json'];
    if ($token) {
        $headers[] = 'Authorization: Bearer ' . $token;
    }
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'response' => json_decode($response, true)
    ];
}

echo "=== Testing Event Management System ===\n\n";

// Test 1: Get event categories
echo "1. Testing Event Categories:\n";
$result = makeRequest($baseUrl . '/event-categories', 'GET', null, $token);
echo "HTTP Code: {$result['code']}\n";
if ($result['response']['success']) {
    echo "✓ Categories found: " . count($result['response']['data']['data']) . "\n";
    foreach ($result['response']['data']['data'] as $category) {
        echo "  - {$category['name']} ({$category['color_code']})\n";
    }
} else {
    echo "✗ Failed to get categories\n";
}
echo "\n";

// Test 2: Get events
echo "2. Testing Events:\n";
$result = makeRequest($baseUrl . '/events', 'GET', null, $token);
echo "HTTP Code: {$result['code']}\n";
if ($result['response']['success']) {
    echo "✓ Events found: " . count($result['response']['data']['data']) . "\n";
    foreach ($result['response']['data']['data'] as $event) {
        echo "  - {$event['title']} (Status: {$event['status']})\n";
        echo "    Venue: {$event['venue_name']}\n";
        echo "    Date: {$event['start_date']}\n";
    }
} else {
    echo "✗ Failed to get events\n";
}
echo "\n";

// Test 3: Get ticket types for the first event
echo "3. Testing Ticket Types:\n";
$result = makeRequest($baseUrl . '/ticket-types?event_id=1', 'GET', null, $token);
echo "HTTP Code: {$result['code']}\n";
if ($result['response']['success']) {
    echo "✓ Ticket types found: " . count($result['response']['data']['data']) . "\n";
    foreach ($result['response']['data']['data'] as $ticket) {
        echo "  - {$ticket['name']}: ₹{$ticket['price']} ({$ticket['quantity_available']} available)\n";
        if (!empty($ticket['benefits'])) {
            echo "    Benefits: " . implode(', ', $ticket['benefits']) . "\n";
        }
    }
} else {
    echo "✗ Failed to get ticket types\n";
}
echo "\n";

// Test 4: Get event analytics
echo "4. Testing Event Analytics:\n";
$result = makeRequest($baseUrl . '/events/1/analytics', 'GET', null, $token);
echo "HTTP Code: {$result['code']}\n";
if ($result['response']['success']) {
    $analytics = $result['response']['data'];
    echo "✓ Analytics retrieved:\n";
    echo "  - Total Bookings: {$analytics['total_bookings']}\n";
    echo "  - Confirmed Bookings: {$analytics['confirmed_bookings']}\n";
    echo "  - Total Tickets Sold: {$analytics['total_tickets_sold']}\n";
    echo "  - Total Revenue: ₹{$analytics['total_revenue']}\n";
    echo "  - Capacity Utilization: {$analytics['capacity_utilization']}%\n";
} else {
    echo "✗ Failed to get analytics\n";
}
echo "\n";

echo "=== Event Management System Test Complete ===\n";
