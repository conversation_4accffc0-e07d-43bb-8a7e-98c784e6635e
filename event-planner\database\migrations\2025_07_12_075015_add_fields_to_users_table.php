<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->text('address')->nullable()->after('phone');
            $table->date('date_of_birth')->nullable()->after('address');
            $table->enum('gender', ['male', 'female', 'other'])->nullable()->after('date_of_birth');
            $table->string('avatar')->nullable()->after('gender');
            $table->boolean('is_active')->default(true)->after('avatar');
            $table->enum('login_type', ['email', 'magic_link', 'whatsapp'])->default('email')->after('is_active');
            $table->timestamp('last_login_at')->nullable()->after('login_type');
            $table->json('preferences')->nullable()->after('last_login_at'); // User preferences
            $table->boolean('email_verified')->default(false)->after('preferences');
            $table->boolean('phone_verified')->default(false)->after('email_verified');

            $table->index(['phone']);
            $table->index(['is_active']);
            $table->index(['login_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone', 'address', 'date_of_birth', 'gender', 'avatar',
                'is_active', 'login_type', 'last_login_at', 'preferences',
                'email_verified', 'phone_verified'
            ]);
        });
    }
};
