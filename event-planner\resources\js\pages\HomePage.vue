<template>
  <div class="homepage">
    <!-- Hero Section -->
    <section class="relative h-screen flex items-center justify-center overflow-hidden">
      <!-- Background Video/Image -->
      <div class="absolute inset-0 bg-gradient-to-r from-gray-900 via-purple-900 to-gray-900">
        <div class="absolute inset-0 bg-black/50"></div>
        <!-- Animated background particles -->
        <div class="absolute inset-0">
          <div v-for="i in 50" :key="i" 
               class="absolute w-2 h-2 bg-gradient-to-r from-pink-500 to-cyan-500 rounded-full animate-pulse"
               :style="{ 
                 left: Math.random() * 100 + '%', 
                 top: Math.random() * 100 + '%',
                 animationDelay: Math.random() * 3 + 's'
               }">
          </div>
        </div>
      </div>

      <!-- Hero Content -->
      <div class="relative z-10 text-center max-w-4xl mx-auto px-4">
        <h1 class="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 bg-clip-text text-transparent animate-gradient">
          Liquid N Lights
        </h1>
        <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
          Interactive art installation that combines the nostalgia of Lite Brite with modern Instagram culture
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link to="/events" 
                       class="bg-gradient-to-r from-pink-500 to-cyan-500 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:shadow-lg hover:shadow-pink-500/25 transform hover:scale-105 transition-all">
            Explore Events
          </router-link>
          <button @click="scrollToFeatured"
                  class="border-2 border-white/30 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white/10 transition-all">
            Learn More
          </button>
        </div>
      </div>

      <!-- Scroll indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg class="w-6 h-6 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
      </div>
    </section>

    <!-- Featured Events Section -->
    <section ref="featuredSection" class="py-20 bg-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-white mb-4">Featured Events</h2>
          <p class="text-xl text-gray-400 max-w-2xl mx-auto">
            Discover our upcoming interactive art experiences
          </p>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="i in 3" :key="i" class="bg-gray-700 rounded-xl overflow-hidden animate-pulse">
            <div class="h-48 bg-gray-600"></div>
            <div class="p-6">
              <div class="h-4 bg-gray-600 rounded mb-2"></div>
              <div class="h-4 bg-gray-600 rounded w-3/4 mb-4"></div>
              <div class="h-8 bg-gray-600 rounded"></div>
            </div>
          </div>
        </div>

        <!-- Events Grid -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="event in featuredEvents" :key="event.id" 
               class="bg-gray-700 rounded-xl overflow-hidden hover:transform hover:scale-105 transition-all duration-300 hover:shadow-xl hover:shadow-pink-500/20">
            
            <!-- Event Image -->
            <div class="relative h-48 bg-gradient-to-br from-pink-500 to-cyan-500 overflow-hidden">
              <img v-if="event.featured_image" 
                   :src="event.featured_image" 
                   :alt="event.title"
                   class="w-full h-full object-cover">
              <div v-else class="w-full h-full flex items-center justify-center">
                <span class="text-white text-2xl font-bold">{{ event.title.charAt(0) }}</span>
              </div>
              
              <!-- Category Badge -->
              <div class="absolute top-4 left-4">
                <span class="bg-black/50 text-white px-3 py-1 rounded-full text-sm font-medium">
                  {{ event.category?.name }}
                </span>
              </div>
              
              <!-- Featured Badge -->
              <div v-if="event.is_featured" class="absolute top-4 right-4">
                <span class="bg-gradient-to-r from-pink-500 to-cyan-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                  Featured
                </span>
              </div>
            </div>

            <!-- Event Details -->
            <div class="p-6">
              <h3 class="text-xl font-bold text-white mb-2 line-clamp-2">{{ event.title }}</h3>
              <p class="text-gray-400 mb-4 line-clamp-2">{{ event.short_description }}</p>
              
              <!-- Event Info -->
              <div class="space-y-2 mb-6">
                <div class="flex items-center text-gray-300">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span class="text-sm">{{ formatDate(event.start_date) }}</span>
                </div>
                <div class="flex items-center text-gray-300">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="text-sm">{{ event.venue_name }}</span>
                </div>
                <div class="flex items-center text-gray-300">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                  </svg>
                  <span class="text-sm">Starting from ₹{{ getMinPrice(event.ticket_types) }}</span>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex gap-3">
                <router-link :to="`/events/${event.slug}`" 
                           class="flex-1 bg-gradient-to-r from-pink-500 to-cyan-500 text-white text-center py-2 px-4 rounded-lg font-semibold hover:shadow-lg hover:shadow-pink-500/25 transition-all">
                  View Details
                </router-link>
                <button @click="quickBook(event)" 
                        class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-500 transition-colors">
                  Quick Book
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- View All Events Button -->
        <div class="text-center mt-12">
          <router-link to="/events" 
                       class="inline-block bg-transparent border-2 border-pink-500 text-pink-500 px-8 py-3 rounded-lg font-semibold hover:bg-pink-500 hover:text-white transition-all">
            View All Events
          </router-link>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section class="py-20 bg-gray-900">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h2 class="text-4xl font-bold text-white mb-6">What is Liquid N Lights?</h2>
            <p class="text-lg text-gray-400 mb-6">
              Liquid N Lights is a custom designed art installation that combines the nostalgia of Lite Brite 
              with current Instagram culture. Each glowing bulb is filled with alcoholic or non-alcoholic drinks 
              for the interaction of the guest.
            </p>
            <p class="text-lg text-gray-400 mb-8">
              You send our team your image and we create a customized rendering that transforms your vision 
              into an interactive experience perfect for your event.
            </p>
            <router-link to="/about" 
                         class="inline-block bg-gradient-to-r from-pink-500 to-cyan-500 text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg hover:shadow-pink-500/25 transition-all">
              Learn More
            </router-link>
          </div>
          <div class="relative">
            <div class="bg-gradient-to-br from-pink-500 to-cyan-500 rounded-xl p-1">
              <div class="bg-gray-800 rounded-lg p-8 h-full">
                <div class="grid grid-cols-4 gap-2">
                  <div v-for="i in 16" :key="i" 
                       class="w-8 h-8 rounded-full bg-gradient-to-r from-pink-500 to-cyan-500 animate-pulse"
                       :style="{ animationDelay: (i * 0.1) + 's' }">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'HomePage',
  setup() {
    const router = useRouter()
    const featuredSection = ref(null)
    const featuredEvents = ref([])
    const loading = ref(true)

    const scrollToFeatured = () => {
      featuredSection.value?.scrollIntoView({ behavior: 'smooth' })
    }

    const loadFeaturedEvents = async () => {
      try {
        const response = await axios.get('/public/events/featured')
        featuredEvents.value = response.data.data
      } catch (error) {
        console.error('Failed to load featured events:', error)
        // Fallback to mock data for demo
        featuredEvents.value = [
          {
            id: 1,
            title: 'Liquid N Lights Interactive Art Experience',
            slug: 'liquid-n-lights-interactive-art-experience',
            short_description: 'Interactive art installation with glowing drink-filled bulbs',
            start_date: '2025-08-15T18:00:00.000000Z',
            venue_name: 'Downtown Art Gallery',
            is_featured: true,
            category: { name: 'Art Fairs' },
            ticket_types: [
              { price: 2500 },
              { price: 3500 },
              { price: 5500 }
            ]
          }
        ]
      } finally {
        loading.value = false
      }
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const getMinPrice = (ticketTypes) => {
      if (!ticketTypes || ticketTypes.length === 0) return '0'
      return Math.min(...ticketTypes.map(t => t.price)).toLocaleString()
    }

    const quickBook = (event) => {
      router.push(`/booking/${event.id}`)
    }

    onMounted(() => {
      loadFeaturedEvents()
    })

    return {
      featuredSection,
      featuredEvents,
      loading,
      scrollToFeatured,
      formatDate,
      getMinPrice,
      quickBook
    }
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient {
  background: linear-gradient(-45deg, #ec4899, #06b6d4, #8b5cf6, #f59e0b);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}
</style>
