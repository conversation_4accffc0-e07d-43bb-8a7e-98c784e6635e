<template>
  <div class="bg-white">
    <!-- Hero Section -->
    <div class="relative bg-gradient-to-r from-indigo-600 to-purple-600 overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-20"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
            Discover Amazing
            <span class="block text-yellow-300">Events Near You</span>
          </h1>
          <p class="text-xl text-gray-200 mb-8 max-w-3xl mx-auto">
            Join thousands of people discovering and attending incredible events.
            From concerts to conferences, find your next unforgettable experience.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <router-link
              to="/events"
              class="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Browse Events
            </router-link>
            <router-link
              v-if="!authStore?.isAuthenticated"
              to="/register"
              class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition-colors"
            >
              Get Started
            </router-link>
            <router-link
              v-else-if="authStore?.hasRole && authStore.hasRole(['organizer', 'admin'])"
              to="/organizer/events/create"
              class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition-colors"
            >
              Create Event
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Featured Events Section -->
    <div class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Events</h2>
          <p class="text-lg text-gray-600">Don't miss these amazing upcoming events</p>
        </div>

        <div v-if="loading" class="flex justify-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>

        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="event in featuredEvents" :key="event.id"
               class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
            <!-- Event Image -->
            <div class="relative h-48 bg-gray-200 overflow-hidden">
              <div class="w-full h-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                <span class="text-white text-3xl font-bold">{{ event.title.charAt(0) }}</span>
              </div>

              <!-- Category Badge -->
              <div class="absolute top-3 left-3">
                <span class="bg-white/90 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                  {{ event.category || 'Event' }}
                </span>
              </div>

              <!-- Featured Badge -->
              <div class="absolute top-3 right-3">
                <span class="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-bold">
                  Featured
                </span>
              </div>

              <!-- Price Badge -->
              <div class="absolute bottom-3 right-3">
                <span class="bg-indigo-600 text-white px-2 py-1 rounded-full text-sm font-semibold">
                  {{ formatPrice(event.price) }}
                </span>
              </div>
            </div>

            <!-- Event Details -->
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-2">
                {{ event.title }}
              </h3>
              <p class="text-gray-600 mb-4">
                {{ event.description }}
              </p>

              <!-- Event Info -->
              <div class="space-y-2 mb-6">
                <div class="flex items-center text-gray-500">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <span class="text-sm">{{ formatDate(event.start_date) }}</span>
                </div>
                <div class="flex items-center text-gray-500">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="text-sm">{{ event.venue_name || event.location }}</span>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex gap-3">
                <router-link
                  :to="`/events/${event.slug || event.id}`"
                  class="flex-1 bg-indigo-600 text-white text-center py-2 px-4 rounded-md font-medium hover:bg-indigo-700 transition-colors"
                >
                  View Details
                </router-link>
                <button
                  class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-200 transition-colors"
                >
                  Quick Book
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center mt-12">
          <router-link
            to="/events"
            class="bg-indigo-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-indigo-700 transition-colors"
          >
            View All Events
          </router-link>
        </div>
      </div>
    </div>

    <!-- Features Section -->
    <div class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Why Choose EventManager?</h2>
          <p class="text-lg text-gray-600">Everything you need to discover and manage events</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Easy Discovery</h3>
            <p class="text-gray-600">Find events that match your interests with our powerful search and filtering tools.</p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Secure Booking</h3>
            <p class="text-gray-600">Book tickets safely with our secure payment system and instant confirmation.</p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Community</h3>
            <p class="text-gray-600">Connect with like-minded people and build lasting relationships through events.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- CTA Section -->
    <div class="bg-indigo-600">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <h2 class="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
          <p class="text-xl text-indigo-200 mb-8">
            Join thousands of event enthusiasts and organizers today
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <router-link
              to="/register"
              class="bg-white text-indigo-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Sign Up Free
            </router-link>
            <router-link
              to="/events"
              class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-600 transition-colors"
            >
              Browse Events
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useEventsStore } from '@/stores/events'

// Initialize stores with error handling
let authStore, eventsStore
try {
  authStore = useAuthStore()
  eventsStore = useEventsStore()
} catch (error) {
  console.warn('Store initialization error:', error)
  authStore = { isAuthenticated: false, hasRole: () => false }
  eventsStore = { featuredEvents: [], categories: [], fetchFeaturedEvents: () => {}, fetchCategories: () => {} }
}

const featuredEvents = computed(() => {
  try {
    return eventsStore.featuredEvents?.slice(0, 6) || []
  } catch (error) {
    return []
  }
})
const loading = computed(() => eventsStore.isLoading)

const loadFeaturedEvents = async () => {
  try {
    // Fetch featured events from the store
    await eventsStore.fetchEvents({ featured: true, limit: 6 })
  } catch (error) {
    console.error('Failed to load featured events:', error)
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatPrice = (price) => {
  if (price === 0) return 'Free'
  return `₹${price.toLocaleString()}`
}

onMounted(async () => {
  try {
    await loadFeaturedEvents()
  } catch (error) {
    console.warn('Failed to load page data:', error)
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient {
  background: linear-gradient(-45deg, #ec4899, #06b6d4, #8b5cf6, #f59e0b);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}
</style>
