<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_settings', function (Blueprint $table) {
            $table->id();
            $table->string('service_type'); // payment, sms, whatsapp, email, analytics, etc.
            $table->string('provider'); // razorpay, twilio, gupshup, etc.
            $table->string('name'); // Human readable name
            $table->text('description')->nullable();
            $table->json('credentials'); // Encrypted API keys and settings
            $table->json('configuration')->nullable(); // Additional configuration
            $table->boolean('is_active')->default(false);
            $table->boolean('is_default')->default(false); // Default provider for this service type
            $table->boolean('is_sandbox')->default(false); // Test/sandbox mode
            $table->datetime('last_tested_at')->nullable();
            $table->json('test_results')->nullable(); // Last test results
            $table->timestamps();

            $table->index(['service_type', 'is_active']);
            $table->index(['service_type', 'is_default']);
            $table->unique(['service_type', 'provider', 'is_default']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_settings');
    }
};
