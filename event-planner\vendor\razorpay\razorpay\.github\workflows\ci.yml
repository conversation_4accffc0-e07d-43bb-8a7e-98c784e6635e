name: CI
on:
  push:
    branches:
      - master
    tags:
      - v[0-9]+.[0-9]+.[0-9]+*
  pull_request:
    branches:
      - master
jobs:
  run:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Set up php 8.0
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.0'
      - name: 'Create env file'
        run: |
          touch ${{ github.workspace }}/tests/.env
          echo RAZORPAY_API_KEY=${{ secrets.RAZORPAY_API_KEY }} >> ${{ github.workspace }}/tests/.env
          echo RAZORPAY_API_SECRET=${{ secrets.RAZORPAY_API_SECRET }} >> ${{ github.workspace }}/tests/.env
          cat ${{ github.workspace }}/tests/.env          
      - name: Install dependencies
        run: composer self-update && composer install && composer require vlucas/phpdotenv && composer dump-autoload
      - name: Run tests and collect coverage
        run: vendor/bin/phpunit ./tests/CoverageTest.php --coverage-clover coverage.xml .
        env:
           RAZORPAY_API_KEY: ${{ secrets.RAZORPAY_API_KEY }}
           RAZORPAY_API_SECRET: ${{ secrets.RAZORPAY_API_SECRET }}
           RAZORPAY_PARTNER_API_KEY: ${{ secrets.RAZORPAY_PARTNER_API_KEY }}
           RAZORPAY_PARTNER_API_SECRET: ${{ secrets.RAZORPAY_PARTNER_API_SECRET }}
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
