import{r as c,c as v,o as A,l as N,w as R,h as e,x as S,z as U,f as l,k as y,F as f,n as p,t as n,y as H,L as Q,g as a,i as J}from"./vue-vendor-BupLktX_.js";import{_ as $}from"./admin-9F2yeZU0.js";import"./chart-vendor-Db3utXXw.js";const F={class:"space-y-6"},K={class:"flex justify-between items-center"},O={class:"flex space-x-3"},P=["value"],q={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},G={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},W={class:"flex items-center"},X={class:"ml-4"},Y={class:"text-2xl font-bold text-gray-900"},Z={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},ee={class:"flex items-center"},te={class:"ml-4"},se={class:"text-2xl font-bold text-gray-900"},oe={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},ne={class:"flex items-center"},ae={class:"ml-4"},le={class:"text-2xl font-bold text-gray-900"},ie={class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200"},de={class:"flex items-center"},re={class:"ml-4"},ce={class:"text-2xl font-bold text-gray-900"},ue={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},me={class:"bg-white shadow-sm rounded-lg border border-gray-200"},ge={class:"p-6"},ve={key:0,class:"text-center py-12"},pe={key:1,class:"space-y-4"},xe={class:"border-t border-gray-200 pt-4"},he={class:"flex space-x-2"},ye={class:"bg-white shadow-sm rounded-lg border border-gray-200"},fe={class:"p-6"},we={key:0,class:"text-center py-8"},_e={key:1,class:"space-y-4 max-h-96 overflow-y-auto"},be={class:"flex justify-between items-start"},ke={class:"flex items-center"},Se={class:"ml-2 text-sm text-gray-500"},Ce={class:"mt-1"},Me={class:"text-sm font-medium text-gray-900"},Te={class:"text-xs text-gray-500"},De={key:0,class:"text-xs text-gray-600 mt-1"},Le={class:"text-right"},Ee={key:0,class:"w-5 h-5 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},Be={key:1,class:"w-5 h-5 text-red-500",fill:"currentColor",viewBox:"0 0 20 20"},Ie={key:2,class:"w-5 h-5 text-yellow-500",fill:"currentColor",viewBox:"0 0 20 20"},Ve={class:"bg-white shadow-sm rounded-lg border border-gray-200"},je={class:"overflow-x-auto"},ze={class:"min-w-full divide-y divide-gray-200"},Ae={class:"bg-white divide-y divide-gray-200"},Ne={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Re={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Ue={class:"px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-600"},He={class:"px-6 py-4 whitespace-nowrap"},Qe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900"},Oe={__name:"Scanner",setup(Je){const i=c(!1),m=c(""),u=c(""),w=c([{id:1,title:"Tech Conference 2025"},{id:2,title:"Music Festival"},{id:3,title:"Art Exhibition"}]),r=c([{id:1,ticket_code:"TC2025-001-001",attendee_name:"John Doe",status:"valid",timestamp:new Date(Date.now()-2*60*1e3),message:"Check-in successful"},{id:2,ticket_code:"TC2025-002-001",attendee_name:"Jane Smith",status:"duplicate",timestamp:new Date(Date.now()-5*60*1e3),message:"Already checked in at 10:30 AM"},{id:3,ticket_code:"INVALID-CODE",attendee_name:null,status:"invalid",timestamp:new Date(Date.now()-8*60*1e3),message:"Ticket not found"}]),d=c([{id:1,ticket_code:"TC2025-001-001",attendee_name:"John Doe",status:"valid",timestamp:new Date(Date.now()-2*60*1e3),event_name:"Tech Conference 2025"},{id:2,ticket_code:"TC2025-002-001",attendee_name:"Jane Smith",status:"duplicate",timestamp:new Date(Date.now()-5*60*1e3),event_name:"Tech Conference 2025"},{id:3,ticket_code:"INVALID-CODE",attendee_name:null,status:"invalid",timestamp:new Date(Date.now()-8*60*1e3),event_name:"Tech Conference 2025"}]),C=v(()=>d.value.length),M=v(()=>d.value.filter(o=>o.status==="valid").length),T=v(()=>d.value.filter(o=>o.status==="invalid").length),D=v(()=>d.value.filter(o=>o.status==="duplicate").length),L=()=>{if(!m.value){alert("Please select an event first");return}i.value=!i.value,i.value?E():B()},E=()=>{console.log("Starting QR scanner...");const o=()=>{if(!i.value)return;const t=["TC2025-003-001","TC2025-004-001","INVALID-TICKET","TC2025-001-001"],s=t[Math.floor(Math.random()*t.length)];_(s),setTimeout(o,3e3+Math.random()*5e3)};setTimeout(o,2e3)},B=()=>{console.log("Stopping QR scanner...")},_=o=>{var k;let t="valid",s="Demo User",x="Check-in successful";o.includes("INVALID")?(t="invalid",s=null,x="Ticket not found"):d.value.some(g=>g.ticket_code===o&&g.status==="valid")&&(t="duplicate",x="Already checked in");const h={id:Date.now(),ticket_code:o,attendee_name:s,status:t,timestamp:new Date,message:x,event_name:((k=w.value.find(g=>g.id===parseInt(m.value)))==null?void 0:k.title)||"Unknown Event"};r.value.unshift(h),d.value.unshift(h),r.value.length>10&&(r.value=r.value.slice(0,10)),I(h)},b=()=>{u.value.trim()&&(_(u.value.trim()),u.value="")},I=o=>{const t=o.status==="valid"?`✅ ${o.attendee_name} checked in successfully`:o.status==="duplicate"?`⚠️ ${o.attendee_name} already checked in`:`❌ Invalid ticket: ${o.ticket_code}`;console.log(t)},V=o=>o.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"}),j=o=>o.toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),z=()=>{alert("Exporting scan history to CSV...")};return A(()=>{}),(o,t)=>(a(),N($,null,{default:R(()=>[e("div",F,[e("div",K,[t[3]||(t[3]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900"},"Ticket Scanner"),e("p",{class:"text-gray-600"},"Scan QR codes to check-in attendees at events")],-1)),e("div",O,[S(e("select",{"onUpdate:modelValue":t[0]||(t[0]=s=>m.value=s),class:"border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},[t[2]||(t[2]=e("option",{value:""},"Select Event",-1)),(a(!0),l(f,null,y(w.value,s=>(a(),l("option",{key:s.id,value:s.id},n(s.title),9,P))),128))],512),[[U,m.value]]),e("button",{onClick:L,class:p([i.value?"bg-red-600 hover:bg-red-700":"bg-green-600 hover:bg-green-700","text-white px-4 py-2 rounded-lg font-medium transition-colors"])},n(i.value?"Stop Scanner":"Start Scanner"),3)])]),e("div",q,[e("div",G,[e("div",W,[t[5]||(t[5]=e("div",{class:"p-2 bg-blue-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"})])],-1)),e("div",X,[t[4]||(t[4]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Scanned",-1)),e("p",Y,n(C.value),1)])])]),e("div",Z,[e("div",ee,[t[7]||(t[7]=e("div",{class:"p-2 bg-green-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",te,[t[6]||(t[6]=e("p",{class:"text-sm font-medium text-gray-600"},"Valid Tickets",-1)),e("p",se,n(M.value),1)])])]),e("div",oe,[e("div",ne,[t[9]||(t[9]=e("div",{class:"p-2 bg-red-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})])],-1)),e("div",ae,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-600"},"Invalid Tickets",-1)),e("p",le,n(T.value),1)])])]),e("div",ie,[e("div",de,[t[11]||(t[11]=e("div",{class:"p-2 bg-yellow-100 rounded-lg"},[e("svg",{class:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),e("div",re,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-600"},"Duplicates",-1)),e("p",ce,n(D.value),1)])])])]),e("div",ue,[e("div",me,[t[15]||(t[15]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"QR Code Scanner")],-1)),e("div",ge,[i.value?(a(),l("div",pe,[t[14]||(t[14]=e("div",{class:"relative bg-black rounded-lg overflow-hidden",style:{"aspect-ratio":"1"}},[e("div",{class:"absolute inset-0 flex items-center justify-center"},[e("div",{class:"w-64 h-64 border-2 border-white border-dashed rounded-lg flex items-center justify-center"},[e("span",{class:"text-white text-sm"},"Camera Preview")])]),e("div",{class:"absolute inset-0 flex items-center justify-center"},[e("div",{class:"w-64 h-1 bg-red-500 animate-pulse"})])],-1)),e("div",xe,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Manual Ticket Entry",-1)),e("div",he,[S(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>u.value=s),onKeyup:Q(b,["enter"]),type:"text",placeholder:"Enter ticket code manually",class:"flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"},null,544),[[H,u.value]]),e("button",{onClick:b,class:"bg-indigo-600 text-white px-4 py-2 rounded-md font-medium hover:bg-indigo-700"}," Check In ")])])])):(a(),l("div",ve,t[12]||(t[12]=[e("svg",{class:"mx-auto h-24 w-24 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 16h4.01M12 8h4.01M8 12h.01M16 8h.01M8 16h.01M8 8h.01"})],-1),e("h3",{class:"mt-4 text-lg font-medium text-gray-900"},"Scanner Ready",-1),e("p",{class:"mt-2 text-sm text-gray-500"},'Click "Start Scanner" to begin scanning QR codes',-1)])))])]),e("div",ye,[t[20]||(t[20]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Recent Scans")],-1)),e("div",fe,[r.value.length===0?(a(),l("div",we,t[16]||(t[16]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1),e("p",{class:"mt-2 text-sm text-gray-500"},"No scans yet",-1)]))):(a(),l("div",_e,[(a(!0),l(f,null,y(r.value,s=>(a(),l("div",{key:s.id,class:p(["p-4 rounded-lg border-l-4",s.status==="valid"?"bg-green-50 border-green-400":s.status==="invalid"?"bg-red-50 border-red-400":"bg-yellow-50 border-yellow-400"])},[e("div",be,[e("div",null,[e("div",ke,[e("span",{class:p(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",s.status==="valid"?"bg-green-100 text-green-800":s.status==="invalid"?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"])},n(s.status.toUpperCase()),3),e("span",Se,n(V(s.timestamp)),1)]),e("div",Ce,[e("p",Me,n(s.attendee_name||"Unknown"),1),e("p",Te,n(s.ticket_code),1),s.message?(a(),l("p",De,n(s.message),1)):J("",!0)])]),e("div",Le,[s.status==="valid"?(a(),l("svg",Ee,t[17]||(t[17]=[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"},null,-1)]))):s.status==="invalid"?(a(),l("svg",Be,t[18]||(t[18]=[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"},null,-1)]))):(a(),l("svg",Ie,t[19]||(t[19]=[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"},null,-1)])))])])],2))),128))]))])])]),e("div",Ve,[e("div",{class:"px-6 py-4 border-b border-gray-200 flex justify-between items-center"},[t[21]||(t[21]=e("h3",{class:"text-lg font-medium text-gray-900"},"Scan History",-1)),e("button",{onClick:z,class:"bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700"}," Export History ")]),e("div",je,[e("table",ze,[t[22]||(t[22]=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Time"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Attendee"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Ticket Code"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Status"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Event")])],-1)),e("tbody",Ae,[(a(!0),l(f,null,y(d.value,s=>(a(),l("tr",{key:s.id,class:"hover:bg-gray-50"},[e("td",Ne,n(j(s.timestamp)),1),e("td",Re,n(s.attendee_name||"Unknown"),1),e("td",Ue,n(s.ticket_code),1),e("td",He,[e("span",{class:p(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",s.status==="valid"?"bg-green-100 text-green-800":s.status==="invalid"?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"])},n(s.status),3)]),e("td",Qe,n(s.event_name),1)]))),128))])])])])])]),_:1}))}};export{Oe as default};
