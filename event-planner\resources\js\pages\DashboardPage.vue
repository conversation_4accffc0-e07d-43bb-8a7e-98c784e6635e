<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">
          Welcome back, {{ authStore.userName }}!
        </h1>
        <p class="text-gray-600 mt-2">
          Here's what's happening with your events and bookings.
        </p>
      </div>

      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-indigo-100 rounded-lg">
              <TicketIcon class="w-6 h-6 text-indigo-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">My Tickets</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.totalTickets }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <CalendarIcon class="w-6 h-6 text-green-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Upcoming Events</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.upcomingEvents }}</p>
            </div>
          </div>
        </div>

        <div v-if="authStore.hasRole(['organizer', 'admin'])" class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <PresentationChartBarIcon class="w-6 h-6 text-purple-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">My Events</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.myEvents }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <StarIcon class="w-6 h-6 text-yellow-600" />
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Favorites</p>
              <p class="text-2xl font-bold text-gray-900">{{ stats.favorites }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Quick Actions</h2>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <router-link
              to="/events"
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <MagnifyingGlassIcon class="w-8 h-8 text-indigo-600 mr-3" />
              <div>
                <h3 class="font-medium text-gray-900">Browse Events</h3>
                <p class="text-sm text-gray-600">Discover new events</p>
              </div>
            </router-link>

            <router-link
              to="/my-tickets"
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <TicketIcon class="w-8 h-8 text-green-600 mr-3" />
              <div>
                <h3 class="font-medium text-gray-900">My Tickets</h3>
                <p class="text-sm text-gray-600">View your bookings</p>
              </div>
            </router-link>

            <router-link
              v-if="authStore.hasRole(['organizer', 'admin'])"
              to="/organizer/events/create"
              class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <PlusIcon class="w-8 h-8 text-purple-600 mr-3" />
              <div>
                <h3 class="font-medium text-gray-900">Create Event</h3>
                <p class="text-sm text-gray-600">Start organizing</p>
              </div>
            </router-link>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Upcoming Events -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Upcoming Events</h2>
          </div>
          <div class="p-6">
            <div v-if="upcomingEvents.length === 0" class="text-center py-8">
              <CalendarIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-600">No upcoming events</p>
              <router-link
                to="/events"
                class="text-indigo-600 hover:text-indigo-700 font-medium"
              >
                Browse events
              </router-link>
            </div>
            <div v-else class="space-y-4">
              <div
                v-for="event in upcomingEvents.slice(0, 3)"
                :key="event.id"
                class="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg"
              >
                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <CalendarIcon class="w-6 h-6 text-indigo-600" />
                </div>
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900">{{ event.title }}</h3>
                  <p class="text-sm text-gray-600">{{ formatDate(event.start_date) }}</p>
                </div>
                <router-link
                  :to="`/events/${event.slug || event.id}`"
                  class="text-indigo-600 hover:text-indigo-700 text-sm font-medium"
                >
                  View
                </router-link>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Bookings -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Recent Bookings</h2>
          </div>
          <div class="p-6">
            <div v-if="recentBookings.length === 0" class="text-center py-8">
              <TicketIcon class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-600">No recent bookings</p>
              <router-link
                to="/events"
                class="text-indigo-600 hover:text-indigo-700 font-medium"
              >
                Book an event
              </router-link>
            </div>
            <div v-else class="space-y-4">
              <div
                v-for="booking in recentBookings.slice(0, 3)"
                :key="booking.id"
                class="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg"
              >
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <TicketIcon class="w-6 h-6 text-green-600" />
                </div>
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900">{{ booking.event.title }}</h3>
                  <p class="text-sm text-gray-600">{{ booking.quantity }} tickets</p>
                </div>
                <span
                  :class="[
                    'px-2 py-1 rounded-full text-xs font-medium',
                    booking.status === 'confirmed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  ]"
                >
                  {{ booking.status }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import {
  CalendarIcon,
  TicketIcon,
  PresentationChartBarIcon,
  StarIcon,
  MagnifyingGlassIcon,
  PlusIcon
} from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const stats = ref({
  totalTickets: 0,
  upcomingEvents: 0,
  myEvents: 0,
  favorites: 0
})

const upcomingEvents = ref([])
const recentBookings = ref([])

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(async () => {
  // Load dashboard data
  // This would typically come from API calls
  stats.value = {
    totalTickets: 5,
    upcomingEvents: 3,
    myEvents: authStore.hasRole(['organizer', 'admin']) ? 2 : 0,
    favorites: 8
  }
  
  // Mock data for demonstration
  upcomingEvents.value = [
    {
      id: 1,
      title: 'Tech Conference 2025',
      start_date: '2025-02-15T09:00:00Z',
      slug: 'tech-conference-2025'
    },
    {
      id: 2,
      title: 'Music Festival',
      start_date: '2025-03-20T18:00:00Z',
      slug: 'music-festival'
    }
  ]
  
  recentBookings.value = [
    {
      id: 1,
      event: { title: 'Art Exhibition' },
      quantity: 2,
      status: 'confirmed'
    },
    {
      id: 2,
      event: { title: 'Workshop Series' },
      quantity: 1,
      status: 'pending'
    }
  ]
})
</script>
