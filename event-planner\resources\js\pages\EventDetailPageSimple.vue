<template>
  <div class="min-h-screen bg-gray-900">
    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="loader"></div>
        <p class="text-white mt-4">Loading event details...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <h2 class="text-2xl font-bold text-white mb-2">Event Not Found</h2>
        <p class="text-gray-400 mb-6">{{ error }}</p>
        <router-link to="/events" class="bg-pink-500 text-white px-6 py-2 rounded-lg hover:bg-pink-600 transition-colors">
          Browse Events
        </router-link>
      </div>
    </div>

    <!-- Event Content -->
    <div v-else-if="event" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Event Header -->
      <div class="bg-gray-800 rounded-xl overflow-hidden mb-8">
        <div class="relative h-64 md:h-96">
          <img v-if="event.featured_image" 
               :src="event.featured_image" 
               :alt="event.title"
               class="w-full h-full object-cover">
          <div v-else class="w-full h-full bg-gradient-to-r from-pink-500 to-cyan-500 flex items-center justify-center">
            <h1 class="text-4xl font-bold text-white text-center">{{ event.title }}</h1>
          </div>
          <div class="absolute inset-0 bg-black/50"></div>
          <div class="absolute bottom-0 left-0 right-0 p-6">
            <h1 class="text-3xl md:text-4xl font-bold text-white mb-2">{{ event.title }}</h1>
            <p class="text-gray-200">{{ event.short_description }}</p>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
          <!-- Event Info -->
          <div class="bg-gray-800 rounded-xl p-6 mb-6">
            <h2 class="text-2xl font-bold text-white mb-4">Event Details</h2>
            <div class="space-y-4">
              <div class="flex items-center text-gray-300">
                <span class="w-5 h-5 mr-3">📅</span>
                <span>{{ formatDate(event.start_date) }}</span>
              </div>
              <div class="flex items-center text-gray-300">
                <span class="w-5 h-5 mr-3">📍</span>
                <span>{{ event.venue_name }}</span>
              </div>
              <div class="flex items-center text-gray-300">
                <span class="w-5 h-5 mr-3">💰</span>
                <span>Starting from ₹{{ getMinPrice() }}</span>
              </div>
            </div>
          </div>

          <!-- Description -->
          <div class="bg-gray-800 rounded-xl p-6">
            <h3 class="text-xl font-bold text-white mb-4">About This Event</h3>
            <div class="text-gray-300 prose prose-invert max-w-none">
              <p>{{ event.description }}</p>
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
          <!-- Ticket Selection -->
          <div class="bg-gray-800 rounded-xl p-6 sticky top-8">
            <h3 class="text-xl font-bold text-white mb-6">Select Tickets</h3>
            
            <div class="space-y-4 mb-6">
              <div v-for="ticketType in event.ticket_types" :key="ticketType.id"
                   class="border border-gray-700 rounded-lg p-4">
                <div class="flex justify-between items-start mb-2">
                  <div>
                    <h4 class="font-semibold text-white">{{ ticketType.name }}</h4>
                    <p class="text-sm text-gray-400">{{ ticketType.description }}</p>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-bold text-white">₹{{ ticketType.price }}</div>
                  </div>
                </div>

                <!-- Quantity Selector -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <button @click="decrementTicket(ticketType.id)"
                            :disabled="!selectedTickets[ticketType.id] || selectedTickets[ticketType.id] <= 0"
                            class="w-8 h-8 rounded-full bg-gray-700 text-white flex items-center justify-center hover:bg-gray-600 disabled:opacity-50">
                      -
                    </button>
                    <span class="w-8 text-center text-white font-semibold">{{ selectedTickets[ticketType.id] || 0 }}</span>
                    <button @click="incrementTicket(ticketType.id)"
                            :disabled="(selectedTickets[ticketType.id] || 0) >= ticketType.max_quantity_per_order"
                            class="w-8 h-8 rounded-full bg-gray-700 text-white flex items-center justify-center hover:bg-gray-600 disabled:opacity-50">
                      +
                    </button>
                  </div>
                  <div class="text-sm text-gray-400">
                    {{ ticketType.quantity_remaining }} left
                  </div>
                </div>
              </div>
            </div>

            <!-- Total and Book Button -->
            <div class="border-t border-gray-700 pt-4">
              <div class="flex justify-between items-center mb-4">
                <span class="text-white font-semibold">Total:</span>
                <span class="text-xl font-bold text-white">₹{{ totalPrice }}</span>
              </div>
              
              <router-link v-if="totalTickets > 0"
                           :to="`/booking/${event.id}`"
                           class="w-full bg-gradient-to-r from-pink-500 to-cyan-500 text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all block text-center">
                Proceed to Book
              </router-link>
              <button v-else
                      disabled
                      class="w-full bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold opacity-50 cursor-not-allowed">
                Select Tickets
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'EventDetailPage',
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    const loading = ref(true)
    const error = ref(null)
    const event = ref(null)
    const selectedTickets = reactive({})

    const totalTickets = computed(() => {
      return Object.values(selectedTickets).reduce((sum, qty) => sum + (qty || 0), 0)
    })

    const totalPrice = computed(() => {
      if (!event.value?.ticket_types) return 0
      return event.value.ticket_types.reduce((total, ticketType) => {
        const qty = selectedTickets[ticketType.id] || 0
        return total + (qty * ticketType.price)
      }, 0)
    })

    const fetchEvent = async () => {
      try {
        loading.value = true
        const response = await axios.get(`/api/events/${route.params.slug}`)
        event.value = response.data.data
      } catch (err) {
        error.value = err.response?.data?.message || 'Event not found'
      } finally {
        loading.value = false
      }
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const getMinPrice = () => {
      if (!event.value?.ticket_types?.length) return 0
      return Math.min(...event.value.ticket_types.map(t => t.price))
    }

    const incrementTicket = (ticketTypeId) => {
      const ticketType = event.value.ticket_types.find(t => t.id === ticketTypeId)
      if (!ticketType) return

      const currentQty = selectedTickets[ticketTypeId] || 0
      if (currentQty < ticketType.max_quantity_per_order && currentQty < ticketType.quantity_remaining) {
        selectedTickets[ticketTypeId] = currentQty + 1
      }
    }

    const decrementTicket = (ticketTypeId) => {
      const currentQty = selectedTickets[ticketTypeId] || 0
      if (currentQty > 0) {
        selectedTickets[ticketTypeId] = currentQty - 1
      }
    }

    onMounted(() => {
      fetchEvent()
    })

    return {
      loading,
      error,
      event,
      selectedTickets,
      totalTickets,
      totalPrice,
      formatDate,
      getMinPrice,
      incrementTicket,
      decrementTicket
    }
  }
}
</script>

<style scoped>
.loader {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #ec4899;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
