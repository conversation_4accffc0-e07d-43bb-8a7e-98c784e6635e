<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\EventCategory;

class EventCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Art Fairs',
                'slug' => 'art-fairs',
                'description' => 'Interactive art installations and exhibitions perfect for art fairs and galleries.',
                'color_code' => '#FF6B6B',
                'icon' => 'fas fa-palette',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Corporate Parties',
                'slug' => 'corporate-parties',
                'description' => 'Professional events, team building, and corporate celebrations.',
                'color_code' => '#4ECDC4',
                'icon' => 'fas fa-building',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Galas',
                'slug' => 'galas',
                'description' => 'Elegant gala events and formal celebrations.',
                'color_code' => '#45B7D1',
                'icon' => 'fas fa-crown',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Launch Parties',
                'slug' => 'launch-parties',
                'description' => 'Product launches, brand events, and promotional celebrations.',
                'color_code' => '#F7DC6F',
                'icon' => 'fas fa-rocket',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Birthdays',
                'slug' => 'birthdays',
                'description' => 'Birthday celebrations and personal milestone events.',
                'color_code' => '#BB8FCE',
                'icon' => 'fas fa-birthday-cake',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Weddings',
                'slug' => 'weddings',
                'description' => 'Wedding ceremonies, receptions, and related celebrations.',
                'color_code' => '#F8C471',
                'icon' => 'fas fa-heart',
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'Private Parties',
                'slug' => 'private-parties',
                'description' => 'Intimate gatherings and private celebrations.',
                'color_code' => '#85C1E9',
                'icon' => 'fas fa-users',
                'is_active' => true,
                'sort_order' => 7,
            ],
        ];

        foreach ($categories as $category) {
            EventCategory::firstOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }

        $this->command->info('Event categories created successfully!');
    }
}
