<template>
  <div class="min-h-screen bg-gray-100 flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg text-center">
      <h1 class="text-3xl font-bold text-green-600 mb-4">✅ Vue.js is Working!</h1>
      <p class="text-gray-600 mb-4">The application has loaded successfully.</p>
      <div class="space-y-2">
        <p><strong>Current Time:</strong> {{ currentTime }}</p>
        <p><strong>Counter:</strong> {{ counter }}</p>
        <button @click="counter++" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
          Increment Counter
        </button>
      </div>
      <div class="mt-6">
        <router-link to="/events" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
          Go to Events
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const counter = ref(0)
const currentTime = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleTimeString()
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
})
</script>
