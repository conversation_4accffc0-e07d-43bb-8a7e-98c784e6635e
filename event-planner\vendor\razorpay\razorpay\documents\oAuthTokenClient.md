## OAuth Token Client

### Generate Authorize Url
```php

use <PERSON><PERSON>pay\Api\OAuthTokenClient;
use Razorpay\Api\Utility;

// Initialize client
$oauth = new OAuthTokenClient();
$utility = new Utility();

$attributes = [
   "submerchant_id" => "<SUBMERCHANT_MID>",
   "timestamp" => floor(microtime(true));
]

$onboarding_signature = $utility->generateOnboardingSignature($attributes, "<YOUR_CLIENT_SECRET>");

// Not an promise
$authUrl = $oauth->oauthClient->getAuthURL([
  "client_id" => "<YOUR_CLIENT_ID>",
  "response_type" => "code",
  "redirect_uri" => "https://example.com/razorpay_callback",
  "scopes" => ["read_write"],
  "state" => "NOBYtv8r6c75ex6WZ",
  "onboarding_signature" => $onboarding_signature
]);
```

**Parameters:**

| Name                 | Type | Description                                                                                                                                             |
|----------------------|--|---------------------------------------------------------------------------------------------------------------------------------------------------------|
| client_id*           | string | Unique client identifier.                                                                                                                               |
| redirect_uri*        | string | Callback URL used by Razorpay to redirect after the user approves or denies the authorisation request. The client should whitelist the 'redirect_uri'.  |
| scopes*              | array | Defines what access your application is requesting from the user. You can request one or multiple scopes by adding them to an array as indicated above. |
| state*               | string | A random string generated by your service. This parameter helps prevent cross-site request forgery (CSRF) attacks.                                      |
| onboarding_signature | string | A cryptographic string generated by your service using generateOnboardingSignature method in Utils class. Only applicable for accounts created with pre-fill KYC |

**Response:**
```
"https://auth.razorpay.com/authorize?response_type=code&client_id=<YOUR_CLIENT_ID>&redirect_uri=https:%2F%2Fexample.com%2Frazorpay_callback&scope[]=read_only&scope[]=read_write&state=NOBYtv8r6c75ex6WZ&onboarding_signature=<GENERATED_ONBOARDING_SIGNATURE>"
```

-------------------------------------------------------------------------------------------------------

### Get Access token
```php
$oauth->oauthClient->getAccessToken([
  "client_id" => "<YOUR_CLIENT_ID>",
  "client_secret" => "<YOUR_CLIENT_SECRET>",
  "grant_type" => "authorization_code",
  "redirect_uri" => "https://example.com",
  "code" => "def50200d844dc80cc44dce2c665d07a374d76802",
  "mode" => "test"
]);
```

**Parameters:**

| Name           | Type   | Description                                                                                                                  |
|----------------|--------|------------------------------------------------------------------------------------------------------------------------------|
| client_id*     | string | Unique client identifier.                                                                                                    |
| client_secret* | string | Client secret string.                                                                                                        |
| redirect_uri*  | string | Specifies the same redirect_uri used in the authorisation request.                                                           |
| grant_type*    | string | Defines the grant type for the request. Possible value are:<ul><li>authorization_code</li><li>client_credentials</li></ul>   |
| code*          | string | Decoded authorisation code received in the last step. Note: Pass this parameter only when grant_type is 'authorization_code' |
| mode           | string | The type of mode. Possible values: <ul><li>test</li><li>live (default)</li></ul>                                             |

**Response:**
```json
{
  "public_token": "rzp_test_oauth_9xu1rkZqoXlClS",
  "token_type": "Bearer",
  "expires_in": 7862400,
  "access_token": "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  "refresh_token": "def50200f42e07aded65a323f6c53181d802cc797b62cc5e78dd8038d6dff253e5877da9ad32f463a4da0ad895e3de298cbce40e162202170e763754122a6cb97910a1f58e2378ee3492dc295e1525009cccc45635308cce8575bdf373606c453ebb5eb2bec062ca197ac23810cf9d6cf31fbb9fcf5b7d4de9bf524c89a4aa90599b0151c9e4e2fa08acb6d2fe17f30a6cfecdfd671f090787e821f844e5d36f5eacb7dfb33d91e83b18216ad0ebeba2bef7721e10d436c3984daafd8654ed881c581d6be0bdc9ebfaee0dc5f9374d7184d60aae5aa85385690220690e21bc93209fb8a8cc25a6abf1108d8277f7c3d38217b47744d7",
  "razorpay_account_id": "acc_Dhk2qDbmu6FwZH"
}
```
-------------------------------------------------------------------------------------------------------

### Get Access token using refresh token
```php
$oauth->oauthClient->getRefreshToken([
  "client_id" => "<YOUR_CLIENT_ID>",
  "client_secret" => "<YOUR_CLIENT_SECRET>",
  "grant_type" => "authorization_code",
  "refresh_token" => "def50200d844dc80cc44dce2c665d07a374d76802"
]);
```

**Parameters:**

| Name           | Type      | Description                                |
|----------------|-----------|--------------------------------------------|
| client_id*     | string    | Unique client identifier.                  |
| client_secret* | string    | Client secret string.                      | 
| grant_type*    | string | Defines the grant type for the request. Possible value are:<ul><li>authorization_code</li><li>client_credentials</li></ul>   |
| refresh_token* | string    | The previously-stored refresh token value. |


**Response:**
```json
{
  "public_token": "rzp_test_oauth_9xu1rkZqoXlClS",
  "token_type": "Bearer",
  "expires_in": 7862400,
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImp0aSI6Ijl4dTF",
  "refresh_token": "def5020096e1c470c901d34cd60fa53abdaf36620e823ffa53"
}
```

-------------------------------------------------------------------------------------------------------

### Revoke a token
```php
$oauth->oauthClient->revokeToken([
  "client_id" => "<YOUR_CLIENT_ID>",
  "client_secret" => "<YOUR_CLIENT_SECRET>",
  "token" => "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImp0aSI6Ijl4dTF",
  "token_type_hint" => "access_token"
]);
```

**Parameters:**

| Name             | Type     | Description                                                                                              |
|------------------|----------|----------------------------------------------------------------------------------------------------------|
| client_id*       | string   | Unique client identifier.                                                                                |
| client_secret*   | string   | Client secret string.                                                                                    | 
| token_type_hint* | string   | The type of token for the request. Possible values: <ul><li>access_token</li><li>refresh_token</li></ul> | 
| token*           | string   | The token whose access should be revoked.                                                                |

**Response:**
```json
{
  "message": "Token Revoked"
}
```
-------------------------------------------------------------------------------------------------------

**PN: * indicates mandatory fields**
<br>
<br>
**For reference click [here](https://razorpay.com/docs/partners/platform/onboard-businesses/integrate-oauth/integration-steps)**