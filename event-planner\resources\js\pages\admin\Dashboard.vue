<template>
  <AdminLayout>
    <!-- Dashboard Overview -->
    <div class="space-y-6">
      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div v-for="stat in stats" :key="stat.name" 
             class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div :class="stat.iconBackground" class="p-3 rounded-md">
                  <component :is="stat.icon" :class="stat.iconColor" class="h-6 w-6" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">{{ stat.name }}</dt>
                  <dd class="flex items-baseline">
                    <div class="text-2xl font-semibold text-gray-900">{{ stat.value }}</div>
                    <div :class="[
                      stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600',
                      'ml-2 flex items-baseline text-sm font-semibold'
                    ]">
                      <svg v-if="stat.changeType === 'increase'" class="self-center flex-shrink-0 h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                      </svg>
                      <svg v-else class="self-center flex-shrink-0 h-4 w-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                      </svg>
                      {{ stat.change }}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <router-link :to="stat.href" class="font-medium text-indigo-600 hover:text-indigo-500">
                View all
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Bookings Chart -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <div>
              <h3 class="text-lg font-medium text-gray-900">Booking Trends</h3>
              <p class="text-sm text-gray-500">Bookings and revenue over time</p>
            </div>
            <div class="flex space-x-2">
              <button @click="chartTimeRange = '7d'"
                      :class="chartTimeRange === '7d' ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-700'"
                      class="px-3 py-1 rounded text-sm font-medium">7D</button>
              <button @click="chartTimeRange = '30d'"
                      :class="chartTimeRange === '30d' ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-700'"
                      class="px-3 py-1 rounded text-sm font-medium">30D</button>
              <button @click="chartTimeRange = '90d'"
                      :class="chartTimeRange === '90d' ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-700'"
                      class="px-3 py-1 rounded text-sm font-medium">90D</button>
            </div>
          </div>
          <div class="p-6">
            <div class="h-64">
              <BookingTrendsChart :data="bookingTrendsData" :time-range="chartTimeRange" />
            </div>
          </div>
        </div>

        <!-- Event Performance Chart -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Event Performance</h3>
            <p class="text-sm text-gray-500">Top events by bookings this month</p>
          </div>
          <div class="p-6">
            <div class="h-64">
              <EventPerformanceChart :data="eventPerformanceData" />
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
        </div>
        <div class="p-6">
          <div class="flow-root">
            <ul class="-mb-8">
              <li v-for="(activity, index) in recentActivity" :key="activity.id">
                <div class="relative pb-8">
                  <span v-if="index !== recentActivity.length - 1" 
                        class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></span>
                  <div class="relative flex space-x-3">
                    <div>
                      <span :class="[
                        activity.type === 'booking' ? 'bg-green-500' : 
                        activity.type === 'event' ? 'bg-blue-500' : 'bg-gray-500',
                        'h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white'
                      ]">
                        <svg class="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path v-if="activity.type === 'booking'" fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                          <path v-else-if="activity.type === 'event'" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                          <path v-else d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                        </svg>
                      </span>
                    </div>
                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p class="text-sm text-gray-500">{{ activity.description }}</p>
                      </div>
                      <div class="text-right text-sm whitespace-nowrap text-gray-500">
                        {{ formatTimeAgo(activity.timestamp) }}
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <router-link to="/admin/events/create" 
                         class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-500 hover:shadow-md transition-all">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-900">Create Event</p>
                <p class="text-sm text-gray-500">Add a new event</p>
              </div>
            </router-link>

            <router-link to="/admin/users/create" 
                         class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-500 hover:shadow-md transition-all">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-900">Add User</p>
                <p class="text-sm text-gray-500">Create new user account</p>
              </div>
            </router-link>

            <router-link to="/admin/reports" 
                         class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-500 hover:shadow-md transition-all">
              <div class="flex-shrink-0">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-900">View Reports</p>
                <p class="text-sm text-gray-500">Analytics & insights</p>
              </div>
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import AdminLayout from '@/components/admin/AdminLayout.vue'
import BookingTrendsChart from '@/components/admin/charts/BookingTrendsChart.vue'
import EventPerformanceChart from '@/components/admin/charts/EventPerformanceChart.vue'
import { useAuthStore } from '@/stores/auth'

const chartTimeRange = ref('30d')

const bookingTrendsData = computed(() => {
  // This would normally come from an API based on the time range
  const data = {
    '7d': {
      labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      datasets: [
        {
          label: 'Bookings',
          data: [12, 19, 15, 25, 22, 30, 28],
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          fill: true,
          tension: 0.4
        },
        {
          label: 'Revenue (₹000s)',
          data: [8, 12, 10, 18, 15, 22, 20],
          borderColor: 'rgb(16, 185, 129)',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          fill: true,
          tension: 0.4
        }
      ]
    },
    '30d': {
      labels: [
        'Jan 1', 'Jan 5', 'Jan 10', 'Jan 15', 'Jan 20', 'Jan 25', 'Jan 30',
        'Feb 1', 'Feb 5', 'Feb 10', 'Feb 15', 'Feb 20', 'Feb 25', 'Feb 28'
      ],
      datasets: [
        {
          label: 'Bookings',
          data: [12, 19, 15, 25, 22, 30, 28, 35, 32, 45, 38, 52, 48, 55],
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          fill: true,
          tension: 0.4
        },
        {
          label: 'Revenue (₹000s)',
          data: [8, 12, 10, 18, 15, 22, 20, 25, 23, 32, 28, 38, 35, 42],
          borderColor: 'rgb(16, 185, 129)',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          fill: true,
          tension: 0.4
        }
      ]
    },
    '90d': {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7', 'Week 8', 'Week 9', 'Week 10', 'Week 11', 'Week 12'],
      datasets: [
        {
          label: 'Bookings',
          data: [45, 52, 48, 61, 58, 67, 63, 72, 69, 78, 75, 82],
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          fill: true,
          tension: 0.4
        },
        {
          label: 'Revenue (₹000s)',
          data: [32, 38, 35, 45, 42, 48, 45, 52, 49, 56, 53, 59],
          borderColor: 'rgb(16, 185, 129)',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          fill: true,
          tension: 0.4
        }
      ]
    }
  }
  return data[chartTimeRange.value]
})

const eventPerformanceData = ref({
  labels: ['Tech Conference', 'Music Festival', 'Art Exhibition', 'Food Festival', 'Sports Event'],
  datasets: [
    {
      label: 'Bookings',
      data: [156, 134, 98, 87, 76],
      backgroundColor: [
        'rgba(99, 102, 241, 0.8)',
        'rgba(16, 185, 129, 0.8)',
        'rgba(245, 158, 11, 0.8)',
        'rgba(239, 68, 68, 0.8)',
        'rgba(139, 92, 246, 0.8)'
      ],
      borderColor: [
        'rgb(99, 102, 241)',
        'rgb(16, 185, 129)',
        'rgb(245, 158, 11)',
        'rgb(239, 68, 68)',
        'rgb(139, 92, 246)'
      ],
      borderWidth: 2,
      borderRadius: 6
    }
  ]
})

const stats = ref([
  {
    name: 'Total Events',
    value: '24',
    change: '+12%',
    changeType: 'increase',
    href: '/admin/events',
    icon: 'svg',
    iconColor: 'text-indigo-600',
    iconBackground: 'bg-indigo-100'
  },
  {
    name: 'Total Bookings',
    value: '1,247',
    change: '+8%',
    changeType: 'increase',
    href: '/admin/bookings',
    icon: 'svg',
    iconColor: 'text-green-600',
    iconBackground: 'bg-green-100'
  },
  {
    name: 'Revenue',
    value: '₹2,45,680',
    change: '+15%',
    changeType: 'increase',
    href: '/admin/reports',
    icon: 'svg',
    iconColor: 'text-yellow-600',
    iconBackground: 'bg-yellow-100'
  },
  {
    name: 'Active Users',
    value: '89',
    change: '-2%',
    changeType: 'decrease',
    href: '/admin/users',
    icon: 'svg',
    iconColor: 'text-purple-600',
    iconBackground: 'bg-purple-100'
  }
])

const topEvents = ref([
  {
    id: 1,
    title: 'Tech Conference 2025',
    date: '2025-02-15T09:00:00Z',
    bookings: 156
  },
  {
    id: 2,
    title: 'Music Festival',
    date: '2025-03-20T18:00:00Z',
    bookings: 134
  },
  {
    id: 3,
    title: 'Art Exhibition',
    date: '2025-04-10T10:00:00Z',
    bookings: 98
  },
  {
    id: 4,
    title: 'Food Festival',
    date: '2025-05-05T12:00:00Z',
    bookings: 87
  }
])

const recentActivity = ref([
  {
    id: 1,
    type: 'booking',
    description: 'New booking for Tech Conference 2025 by John Doe',
    timestamp: new Date(Date.now() - 5 * 60 * 1000)
  },
  {
    id: 2,
    type: 'event',
    description: 'Music Festival event was updated',
    timestamp: new Date(Date.now() - 15 * 60 * 1000)
  },
  {
    id: 3,
    type: 'booking',
    description: 'Booking cancelled for Art Exhibition by Jane Smith',
    timestamp: new Date(Date.now() - 30 * 60 * 1000)
  },
  {
    id: 4,
    type: 'user',
    description: 'New user registered: Mike Johnson',
    timestamp: new Date(Date.now() - 60 * 60 * 1000)
  }
])

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

const formatTimeAgo = (date) => {
  const now = new Date()
  const diffInMinutes = Math.floor((now - date) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  return `${diffInDays}d ago`
}

onMounted(() => {
  // Auto-login as admin for demo
  const authStore = useAuthStore()

  // Check if user is already authenticated
  if (!authStore.isAuthenticated) {
    // Set demo admin user
    const adminUser = {
      id: 1,
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin'
    }
    const authToken = 'admin-demo-token-12345'

    // Set auth store values
    authStore.user = adminUser
    authStore.token = authToken

    // Store in localStorage
    localStorage.setItem('auth_token', authToken)
    localStorage.setItem('user', JSON.stringify(adminUser))

    console.log('Auto-logged in as admin for demo')
  }

  // Load dashboard data
  console.log('Admin Dashboard loaded')
})
</script>
