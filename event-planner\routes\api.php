<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public authentication routes
Route::prefix('auth')->group(function () {
    Route::post('/admin/login', [AuthController::class, 'adminLogin']);
    Route::post('/magic-link', [AuthController::class, 'requestMagicLink']);
    Route::post('/verify-magic-link', [AuthController::class, 'verifyMagicLink']);
});

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user()->load('roles', 'permissions');
    });
    
    Route::post('/logout', function (Request $request) {
        $request->user()->currentAccessToken()->delete();
        return response()->json(['message' => 'Logged out successfully']);
    });
});

// Admin routes (require authentication and admin roles)
Route::middleware(['auth:sanctum', 'role:Super Admin|Manager|Ticket Booker|Scanner'])->prefix('admin')->group(function () {
    // Dashboard
    Route::get('dashboard/overview', [App\Http\Controllers\Admin\DashboardController::class, 'overview']);
    Route::get('dashboard/activities', [App\Http\Controllers\Admin\DashboardController::class, 'recentActivities']);
    Route::get('dashboard/analytics', [App\Http\Controllers\Admin\DashboardController::class, 'salesAnalytics']);
    Route::get('dashboard/upcoming-events', [App\Http\Controllers\Admin\DashboardController::class, 'upcomingEvents']);

    // Event Categories management
    Route::apiResource('event-categories', App\Http\Controllers\Admin\EventCategoryController::class);
    Route::get('event-categories-active', [App\Http\Controllers\Admin\EventCategoryController::class, 'active']);
    Route::post('event-categories/reorder', [App\Http\Controllers\Admin\EventCategoryController::class, 'reorder']);

    // Events management
    Route::apiResource('events', App\Http\Controllers\Admin\EventController::class);
    Route::post('events/{event}/duplicate', [App\Http\Controllers\Admin\EventController::class, 'duplicate']);
    Route::get('events/{event}/analytics', [App\Http\Controllers\Admin\EventController::class, 'analytics']);

    // Ticket Types management
    Route::apiResource('ticket-types', App\Http\Controllers\Admin\TicketTypeController::class);
    Route::get('events/{event}/ticket-types', [App\Http\Controllers\Admin\TicketTypeController::class, 'byEvent']);

    // Bookings management
    Route::apiResource('bookings', App\Http\Controllers\Admin\BookingController::class);

    // CMS management
    Route::apiResource('cms', App\Http\Controllers\Admin\CmsController::class);
    Route::post('cms/{cms}/duplicate', [App\Http\Controllers\Admin\CmsController::class, 'duplicate']);
    Route::get('cms/menu/pages', [App\Http\Controllers\Admin\CmsController::class, 'menuPages']);
    Route::post('cms/import/liquid-n-lights', [App\Http\Controllers\Admin\CmsController::class, 'importLiquidNLights']);

    // API Settings (Super Admin only)
    Route::middleware('role:Super Admin')->group(function () {
        Route::apiResource('api-settings', App\Http\Controllers\Admin\ApiSettingController::class);
        Route::post('api-settings/{apiSetting}/test', [App\Http\Controllers\Admin\ApiSettingController::class, 'test']);
    });
});

// Public API routes (for frontend)
Route::prefix('public')->group(function () {
    // Public events listing
    Route::get('events', function() {
        return response()->json([
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'Liquid N Lights Interactive Art Experience',
                    'slug' => 'liquid-n-lights-interactive-art-experience',
                    'short_description' => 'Interactive art installation with glowing drink-filled bulbs',
                    'start_date' => '2025-08-15T18:00:00.000000Z',
                    'venue_name' => 'Downtown Art Gallery',
                    'is_featured' => true,
                    'category' => ['name' => 'Art Fairs', 'color_code' => '#FF6B6B'],
                    'ticket_types' => [
                        ['price' => 2500],
                        ['price' => 3500],
                        ['price' => 5500]
                    ]
                ]
            ]
        ]);
    });

    // Featured events
    Route::get('events/featured', function() {
        return response()->json([
            'success' => true,
            'data' => [
                [
                    'id' => 1,
                    'title' => 'Liquid N Lights Interactive Art Experience',
                    'slug' => 'liquid-n-lights-interactive-art-experience',
                    'short_description' => 'Interactive art installation with glowing drink-filled bulbs',
                    'start_date' => '2025-08-15T18:00:00.000000Z',
                    'venue_name' => 'Downtown Art Gallery',
                    'is_featured' => true,
                    'category' => ['name' => 'Art Fairs', 'color_code' => '#FF6B6B'],
                    'ticket_types' => [
                        ['price' => 2500],
                        ['price' => 3500],
                        ['price' => 5500]
                    ]
                ]
            ]
        ]);
    });

    // Public event details
    Route::get('events/{slug}', function($slug) {
        return response()->json([
            'success' => true,
            'data' => [
                'id' => 1,
                'title' => 'Liquid N Lights Interactive Art Experience',
                'slug' => 'liquid-n-lights-interactive-art-experience',
                'description' => 'Experience our signature interactive art installation that combines the nostalgia of Lite Brite with modern Instagram culture. Each glowing bulb is filled with drinks for guest interaction, creating a unique and memorable experience perfect for art fairs, corporate parties, galas, launch parties, birthdays, weddings, and private parties.',
                'short_description' => 'Interactive art installation with glowing drink-filled bulbs',
                'start_date' => '2025-08-15T18:00:00.000000Z',
                'end_date' => '2025-08-15T23:00:00.000000Z',
                'venue_name' => 'Downtown Art Gallery',
                'venue_address' => '123 Art Street, Downtown, City 12345',
                'is_featured' => true,
                'category' => ['name' => 'Art Fairs', 'color_code' => '#FF6B6B'],
                'ticket_types' => [
                    [
                        'id' => 1,
                        'name' => 'Early Bird',
                        'description' => 'Limited early bird tickets with special pricing',
                        'price' => 2500,
                        'quantity_available' => 50,
                        'quantity_sold' => 15,
                        'quantity_remaining' => 35,
                        'max_quantity_per_order' => 4,
                        'benefits' => ['Early access', 'Welcome drink', 'Exclusive photo session']
                    ],
                    [
                        'id' => 2,
                        'name' => 'General Admission',
                        'description' => 'Standard admission to the interactive art experience',
                        'price' => 3500,
                        'quantity_available' => 120,
                        'quantity_sold' => 45,
                        'quantity_remaining' => 75,
                        'max_quantity_per_order' => 6,
                        'benefits' => ['Full access to installation', 'Complimentary drink']
                    ],
                    [
                        'id' => 3,
                        'name' => 'VIP Experience',
                        'description' => 'Premium experience with exclusive perks',
                        'price' => 5500,
                        'quantity_available' => 30,
                        'quantity_sold' => 8,
                        'quantity_remaining' => 22,
                        'max_quantity_per_order' => 2,
                        'benefits' => ['Priority access', 'Premium drinks', 'Artist meet & greet', 'Exclusive merchandise']
                    ]
                ]
            ]
        ]);
    });

    // Public categories
    Route::get('categories', function() {
        return response()->json([
            'success' => true,
            'data' => [
                ['id' => 1, 'name' => 'Art Fairs', 'color_code' => '#FF6B6B'],
                ['id' => 2, 'name' => 'Corporate Parties', 'color_code' => '#4ECDC4'],
                ['id' => 3, 'name' => 'Weddings', 'color_code' => '#F8C471']
            ]
        ]);
    });
});
