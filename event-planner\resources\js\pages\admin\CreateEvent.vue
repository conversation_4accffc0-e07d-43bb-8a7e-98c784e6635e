<template>
  <AdminLayout>
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">{{ isEditing ? 'Edit Event' : 'Create New Event' }}</h1>
          <p class="text-gray-600">{{ isEditing ? 'Update event details and settings' : 'Set up a new event with all the details' }}</p>
        </div>
        <div class="flex space-x-3">
          <router-link to="/admin/events" 
                       class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700 transition-colors">
            Cancel
          </router-link>
          <button @click="saveAsDraft" 
                  class="bg-yellow-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-yellow-700 transition-colors">
            Save as Draft
          </button>
          <button @click="publishEvent" 
                  class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors">
            {{ isEditing ? 'Update & Publish' : 'Create & Publish' }}
          </button>
        </div>
      </div>

      <!-- Form -->
      <form @submit.prevent="submitForm" class="space-y-8">
        <!-- Basic Information -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
          </div>
          <div class="p-6 space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">Event Title *</label>
                <input v-model="eventForm.title" type="text" required 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              </div>
              
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                <textarea v-model="eventForm.description" rows="4" required 
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                <select v-model="eventForm.category" required 
                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  <option value="">Select Category</option>
                  <option value="conference">Conference</option>
                  <option value="festival">Festival</option>
                  <option value="exhibition">Exhibition</option>
                  <option value="workshop">Workshop</option>
                  <option value="sports">Sports</option>
                  <option value="entertainment">Entertainment</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Event Type *</label>
                <select v-model="eventForm.type" required 
                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  <option value="">Select Type</option>
                  <option value="physical">Physical Event</option>
                  <option value="virtual">Virtual Event</option>
                  <option value="hybrid">Hybrid Event</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Start Date & Time *</label>
                <input v-model="eventForm.start_date" type="datetime-local" required 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">End Date & Time *</label>
                <input v-model="eventForm.end_date" type="datetime-local" required 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              </div>
            </div>
          </div>
        </div>

        <!-- Venue Information -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Venue Information</h3>
          </div>
          <div class="p-6 space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Venue Name *</label>
                <input v-model="eventForm.venue_name" type="text" required 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Capacity</label>
                <input v-model="eventForm.capacity" type="number" 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              </div>

              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">Address *</label>
                <textarea v-model="eventForm.address" rows="3" required 
                          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">City *</label>
                <input v-model="eventForm.city" type="text" required 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">State *</label>
                <input v-model="eventForm.state" type="text" required 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              </div>
            </div>
          </div>
        </div>

        <!-- Ticket Types -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">Ticket Types</h3>
            <button type="button" @click="addTicketType" 
                    class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
              Add Ticket Type
            </button>
          </div>
          <div class="p-6">
            <div v-if="eventForm.ticket_types.length === 0" class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
              </svg>
              <p class="mt-2 text-sm text-gray-500">No ticket types added yet</p>
              <button type="button" @click="addTicketType" 
                      class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                Add First Ticket Type
              </button>
            </div>

            <div v-else class="space-y-4">
              <div v-for="(ticket, index) in eventForm.ticket_types" :key="index" 
                   class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start mb-4">
                  <h4 class="text-md font-medium text-gray-900">Ticket Type {{ index + 1 }}</h4>
                  <button type="button" @click="removeTicketType(index)" 
                          class="text-red-600 hover:text-red-900">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                    </svg>
                  </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                    <input v-model="ticket.name" type="text" required 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Price (₹) *</label>
                    <input v-model="ticket.price" type="number" min="0" step="0.01" required 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Quantity *</label>
                    <input v-model="ticket.quantity" type="number" min="1" required 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Sale Start</label>
                    <input v-model="ticket.sale_start" type="datetime-local" 
                           class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  </div>
                </div>
                
                <div class="mt-4">
                  <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <textarea v-model="ticket.description" rows="2" 
                            class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Media & Images -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Media & Images</h3>
          </div>
          <div class="p-6 space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Event Banner</label>
              <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div class="space-y-1 text-center">
                  <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <div class="flex text-sm text-gray-600">
                    <label class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                      <span>Upload a file</span>
                      <input type="file" class="sr-only" accept="image/*" @change="handleImageUpload">
                    </label>
                    <p class="pl-1">or drag and drop</p>
                  </div>
                  <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Event Website</label>
                <input v-model="eventForm.website" type="url" 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                <input v-model="eventForm.contact_email" type="email" 
                       class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              </div>
            </div>
          </div>
        </div>

        <!-- SEO & Meta -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">SEO & Meta Information</h3>
          </div>
          <div class="p-6 space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">URL Slug</label>
              <input v-model="eventForm.slug" type="text" 
                     class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              <p class="text-xs text-gray-500 mt-1">URL: /events/{{ eventForm.slug || 'event-slug' }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
              <input v-model="eventForm.meta_title" type="text" 
                     class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
              <textarea v-model="eventForm.meta_description" rows="3" 
                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
            </div>
          </div>
        </div>

        <!-- Settings -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Event Settings</h3>
          </div>
          <div class="p-6 space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="flex items-center">
                <input v-model="eventForm.is_featured" type="checkbox" 
                       class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                <label class="ml-2 text-sm text-gray-700">Featured Event</label>
              </div>

              <div class="flex items-center">
                <input v-model="eventForm.allow_cancellation" type="checkbox" 
                       class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                <label class="ml-2 text-sm text-gray-700">Allow Ticket Cancellation</label>
              </div>

              <div class="flex items-center">
                <input v-model="eventForm.send_reminders" type="checkbox" 
                       class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                <label class="ml-2 text-sm text-gray-700">Send Event Reminders</label>
              </div>

              <div class="flex items-center">
                <input v-model="eventForm.require_approval" type="checkbox" 
                       class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                <label class="ml-2 text-sm text-gray-700">Require Booking Approval</label>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import AdminLayout from '@/components/admin/AdminLayout.vue'
import { eventsAPI } from '@/services/api'

const route = useRoute()
const router = useRouter()

const isEditing = ref(false)
const isLoading = ref(false)

const eventForm = reactive({
  title: '',
  description: '',
  category: '',
  type: '',
  start_date: '',
  end_date: '',
  venue_name: '',
  capacity: '',
  address: '',
  city: '',
  state: '',
  website: '',
  contact_email: '',
  slug: '',
  meta_title: '',
  meta_description: '',
  is_featured: false,
  allow_cancellation: true,
  send_reminders: true,
  require_approval: false,
  ticket_types: []
})

const addTicketType = () => {
  eventForm.ticket_types.push({
    name: '',
    price: '',
    quantity: '',
    description: '',
    sale_start: ''
  })
}

const removeTicketType = (index) => {
  eventForm.ticket_types.splice(index, 1)
}

const handleImageUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    // Handle image upload
    console.log('Image uploaded:', file.name)
  }
}

const saveAsDraft = () => {
  // Save as draft
  alert('Event saved as draft!')
}

const publishEvent = () => {
  // Publish event
  alert('Event published successfully!')
  router.push('/admin/events')
}

const submitForm = async () => {
  try {
    isLoading.value = true

    // Prepare event data
    const eventData = {
      title: form.value.title,
      description: form.value.description,
      short_description: form.value.shortDescription,
      category_id: form.value.category,
      venue_name: form.value.venueName,
      venue_address: form.value.venueAddress,
      venue_latitude: form.value.venueLatitude,
      venue_longitude: form.value.venueLongitude,
      start_date: form.value.startDate,
      end_date: form.value.endDate,
      booking_start_date: form.value.bookingStartDate,
      booking_end_date: form.value.bookingEndDate,
      featured_image: form.value.featuredImage,
      gallery_images: form.value.galleryImages,
      max_capacity: form.value.maxCapacity,
      is_featured: form.value.isFeatured,
      is_published: form.value.isPublished,
      status: form.value.status,
      seo_meta: {
        title: form.value.seoTitle,
        description: form.value.seoDescription,
        keywords: form.value.seoKeywords
      },
      custom_fields: form.value.customFields,
      ticket_types: form.value.ticketTypes
    }

    // Create or update event
    let response
    if (isEditing.value) {
      response = await eventsAPI.update(eventId.value, eventData)
    } else {
      response = await eventsAPI.create(eventData)
    }

    if (response.success) {
      alert(isEditing.value ? 'Event updated successfully!' : 'Event created successfully!')
      router.push('/admin/events')
    } else {
      throw new Error(response.message || 'Failed to save event')
    }
  } catch (error) {
    console.error('Error saving event:', error)
    alert('Error: ' + (error.message || 'Failed to save event'))
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  // Check if editing
  if (route.params.id) {
    isEditing.value = true
    // Load event data for editing
  }
  
  // Add default ticket type
  if (eventForm.ticket_types.length === 0) {
    addTicketType()
  }
})
</script>
