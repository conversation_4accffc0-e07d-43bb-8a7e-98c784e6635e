import{r as _,c as y,o as F,f as n,h as e,i as r,F as f,k as h,I as L,p as d,e as g,t as o,w as M,m,g as a,n as w}from"./vue-vendor-BupLktX_.js";import{u as U}from"./booking-fYtx-gxu.js";import"./api-D9eN0dN9.js";import"./utils-vendor-Dq7h7Pqt.js";const z={class:"min-h-screen bg-gray-50"},Q={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},H={class:"mb-8"},K={class:"flex space-x-8"},R=["onClick"],G={key:0,class:"flex justify-center py-12"},J={key:1,class:"space-y-6"},O={class:"p-6"},W={class:"flex items-start justify-between"},X={class:"flex-1"},Z={class:"flex items-center space-x-3 mb-2"},ee={class:"text-lg font-semibold text-gray-900"},te={class:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-4"},se={class:"flex items-center"},ne={class:"flex items-center"},oe={class:"flex items-center"},ae={class:"text-sm text-gray-600"},le={key:0},re={class:"ml-6 flex flex-col space-y-2"},ce={key:0,class:"w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center"},ie={class:"flex flex-col space-y-2"},de=["onClick"],ue=["onClick"],me={key:0,class:"mt-6 pt-6 border-t border-gray-200"},ye={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ge={class:"space-y-1 text-sm"},xe={class:"flex justify-between"},pe={class:"text-gray-900"},ve={class:"flex justify-between"},_e={class:"text-gray-900"},fe={key:0,class:"flex justify-between"},he={class:"text-gray-900"},we={key:0},ke={class:"space-y-1 text-sm"},be=["onClick"],Ce={key:2,class:"text-center py-12"},De={class:"text-lg font-medium text-gray-900 mb-2"},Be={class:"text-gray-600 mb-6"},Te={key:0,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},Se={class:"bg-white rounded-lg p-6 max-w-md w-full mx-4"},Ie={class:"flex space-x-4"},$e={__name:"MyTicketsPage",setup(Me){const c=U(),u=_("all"),x=_(!1),p=_(null),N=y(()=>[{key:"all",label:"All Tickets",count:c.userBookings.length},{key:"upcoming",label:"Upcoming",count:k.value.length},{key:"past",label:"Past Events",count:b.value.length},{key:"cancelled",label:"Cancelled",count:C.value.length}]),k=y(()=>{const l=new Date;return c.userBookings.filter(s=>new Date(s.event.start_date)>l&&s.status!=="cancelled")}),b=y(()=>{const l=new Date;return c.userBookings.filter(s=>new Date(s.event.start_date)<=l&&s.status!=="cancelled")}),C=y(()=>c.userBookings.filter(l=>l.status==="cancelled")),D=y(()=>{switch(u.value){case"upcoming":return k.value;case"past":return b.value;case"cancelled":return C.value;default:return bookings.value}}),j=l=>{switch(l){case"confirmed":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},B=l=>new Date(l).toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),P=l=>{const v=(new Date(l.event.start_date)-new Date)/(1e3*60*60);return l.status==="confirmed"&&v>24},E=async l=>{(await c.downloadTicket(l.id)).success||alert("Failed to download ticket. Please try again.")},$=l=>{p.value=l,x.value=!0},q=async()=>{(await c.cancelBooking(p.value.id)).success?(x.value=!1,p.value=null):alert("Failed to cancel booking. Please try again.")},A=()=>{switch(u.value){case"upcoming":return"No upcoming events";case"past":return"No past events";case"cancelled":return"No cancelled bookings";default:return"No tickets found"}},V=()=>{switch(u.value){case"upcoming":return"You don't have any upcoming events. Book some tickets to see them here.";case"past":return"You haven't attended any events yet.";case"cancelled":return"You don't have any cancelled bookings.";default:return"You haven't booked any tickets yet. Start exploring events to book your first ticket."}};return F(async()=>{await c.fetchUserBookings()}),(l,s)=>{const T=g("CalendarIcon"),v=g("MapPinIcon"),S=g("TicketIcon"),Y=g("QrCodeIcon"),I=g("router-link");return a(),n("div",z,[e("div",Q,[s[12]||(s[12]=e("div",{class:"mb-8"},[e("h1",{class:"text-3xl font-bold text-gray-900 mb-4"},"My Tickets"),e("p",{class:"text-gray-600"},"Manage your event bookings and tickets")],-1)),e("div",H,[e("nav",K,[(a(!0),n(f,null,h(N.value,t=>(a(),n("button",{key:t.key,onClick:i=>u.value=t.key,class:w(["py-2 px-1 border-b-2 font-medium text-sm",u.value===t.key?"border-indigo-500 text-indigo-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])},[m(o(t.label)+" ",1),t.count>0?(a(),n("span",{key:0,class:w(["ml-2 py-0.5 px-2 rounded-full text-xs",u.value===t.key?"bg-indigo-100 text-indigo-600":"bg-gray-100 text-gray-900"])},o(t.count),3)):r("",!0)],10,R))),128))])]),L(c).isLoading?(a(),n("div",G,s[1]||(s[1]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"},null,-1)]))):D.value.length>0?(a(),n("div",J,[(a(!0),n(f,null,h(D.value,t=>(a(),n("div",{key:t.id,class:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"},[e("div",O,[e("div",W,[e("div",X,[e("div",Z,[e("h3",ee,o(t.event.title),1),e("span",{class:w(["px-2 py-1 rounded-full text-xs font-medium",j(t.status)])},o(t.status.charAt(0).toUpperCase()+t.status.slice(1)),3)]),e("div",te,[e("div",se,[d(T,{class:"w-4 h-4 mr-2"}),e("span",null,o(B(t.event.start_date)),1)]),e("div",ne,[d(v,{class:"w-4 h-4 mr-2"}),e("span",null,o(t.event.venue_name||t.event.location),1)]),e("div",oe,[d(S,{class:"w-4 h-4 mr-2"}),e("span",null,o(t.quantity)+" ticket"+o(t.quantity>1?"s":""),1)])]),e("div",ae,[e("p",null,[s[2]||(s[2]=e("strong",null,"Booking ID:",-1)),m(" "+o(t.booking_reference),1)]),e("p",null,[s[3]||(s[3]=e("strong",null,"Total Amount:",-1)),m(" ₹"+o(t.total_amount.toLocaleString()),1)]),t.ticket_type?(a(),n("p",le,[s[4]||(s[4]=e("strong",null,"Ticket Type:",-1)),m(" "+o(t.ticket_type.name),1)])):r("",!0)])]),e("div",re,[t.status==="confirmed"?(a(),n("div",ce,[d(Y,{class:"w-12 h-12 text-gray-400"})])):r("",!0),e("div",ie,[t.status==="confirmed"?(a(),n("button",{key:0,onClick:i=>E(t),class:"bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors"}," Download Ticket ",8,de)):r("",!0),d(I,{to:`/events/${t.event.slug||t.event.id}`,class:"bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors text-center"},{default:M(()=>s[5]||(s[5]=[m(" View Event ")])),_:2,__:[5]},1032,["to"]),P(t)?(a(),n("button",{key:1,onClick:i=>$(t),class:"bg-red-100 text-red-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-red-200 transition-colors"}," Cancel Booking ",8,ue)):r("",!0)])])]),t.showDetails?(a(),n("div",me,[e("div",ye,[e("div",null,[s[9]||(s[9]=e("h4",{class:"font-medium text-gray-900 mb-2"},"Booking Details",-1)),e("dl",ge,[e("div",xe,[s[6]||(s[6]=e("dt",{class:"text-gray-600"},"Booked on:",-1)),e("dd",pe,o(B(t.created_at)),1)]),e("div",ve,[s[7]||(s[7]=e("dt",{class:"text-gray-600"},"Payment Method:",-1)),e("dd",_e,o(t.payment_method||"Card"),1)]),t.special_requests?(a(),n("div",fe,[s[8]||(s[8]=e("dt",{class:"text-gray-600"},"Special Requests:",-1)),e("dd",he,o(t.special_requests),1)])):r("",!0)])]),t.attendees&&t.attendees.length>0?(a(),n("div",we,[s[10]||(s[10]=e("h4",{class:"font-medium text-gray-900 mb-2"},"Attendees",-1)),e("ul",ke,[(a(!0),n(f,null,h(t.attendees,i=>(a(),n("li",{key:i.id,class:"text-gray-600"},o(i.name)+" ("+o(i.email)+") ",1))),128))])])):r("",!0)])])):r("",!0),e("button",{onClick:i=>t.showDetails=!t.showDetails,class:"mt-4 text-indigo-600 hover:text-indigo-700 text-sm font-medium"},o(t.showDetails?"Hide Details":"Show Details"),9,be)])]))),128))])):(a(),n("div",Ce,[d(S,{class:"w-16 h-16 text-gray-400 mx-auto mb-4"}),e("h3",De,o(A()),1),e("p",Be,o(V()),1),d(I,{to:"/events",class:"bg-indigo-600 text-white px-6 py-3 rounded-md font-medium hover:bg-indigo-700 transition-colors"},{default:M(()=>s[11]||(s[11]=[m(" Browse Events ")])),_:1,__:[11]})]))]),x.value?(a(),n("div",Te,[e("div",Se,[s[13]||(s[13]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Cancel Booking",-1)),s[14]||(s[14]=e("p",{class:"text-gray-600 mb-6"}," Are you sure you want to cancel this booking? This action cannot be undone. ",-1)),e("div",Ie,[e("button",{onClick:q,class:"flex-1 bg-red-600 text-white px-4 py-2 rounded-md font-medium hover:bg-red-700 transition-colors"}," Yes, Cancel "),e("button",{onClick:s[0]||(s[0]=t=>x.value=!1),class:"flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-200 transition-colors"}," Keep Booking ")])])])):r("",!0)])}}};export{$e as default};
