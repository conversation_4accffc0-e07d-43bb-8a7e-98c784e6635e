<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    // Payment Gateways
    'razorpay' => [
        'key_id' => env('RAZORPAY_KEY_ID'),
        'key_secret' => env('RAZORPAY_KEY_SECRET'),
        'webhook_secret' => env('RAZORPAY_WEBHOOK_SECRET'),
        'enabled' => env('RAZORPAY_ENABLED', false),
    ],

    'stripe' => [
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
        'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
        'enabled' => env('STRIPE_ENABLED', false),
    ],

    'phonepe' => [
        'merchant_id' => env('PHONEPE_MERCHANT_ID'),
        'salt_key' => env('PHONEPE_SALT_KEY'),
        'salt_index' => env('PHONEPE_SALT_INDEX'),
        'enabled' => env('PHONEPE_ENABLED', false),
    ],

    'payu' => [
        'merchant_key' => env('PAYU_MERCHANT_KEY'),
        'merchant_salt' => env('PAYU_MERCHANT_SALT'),
        'enabled' => env('PAYU_ENABLED', false),
    ],

    // SMS Services
    'sms' => [
        'default_provider' => env('SMS_DEFAULT_PROVIDER', 'twilio'),
        'providers' => [
            'twilio' => [
                'account_sid' => env('TWILIO_ACCOUNT_SID'),
                'auth_token' => env('TWILIO_AUTH_TOKEN'),
                'from_number' => env('TWILIO_FROM_NUMBER'),
            ],
            'msg91' => [
                'auth_key' => env('MSG91_AUTH_KEY'),
                'sender_id' => env('MSG91_SENDER_ID'),
                'route' => env('MSG91_ROUTE', '4'),
            ],
            'textlocal' => [
                'api_key' => env('TEXTLOCAL_API_KEY'),
                'sender' => env('TEXTLOCAL_SENDER'),
            ],
        ],
    ],

];
