import{B as b,f as i,h as t,m as c,p as y,w as v,e as w,C as h,i as k,x as u,y as g,A as C,I as d,t as j,j as z,u as L,a as V,g as a}from"./vue-vendor-BupLktX_.js";import{u as _}from"./admin-9F2yeZU0.js";import"./chart-vendor-Db3utXXw.js";const S={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},B={class:"max-w-md w-full space-y-8"},A={class:"mt-2 text-center text-sm text-gray-600"},M={class:"rounded-md shadow-sm -space-y-px"},N={class:"flex items-center justify-between"},D={class:"flex items-center"},q={key:0,class:"bg-red-50 border border-red-200 rounded-md p-4"},E={class:"flex"},P={class:"ml-3"},R={class:"text-sm text-red-800"},T=["disabled"],U={key:0},$={key:1},O={class:"mt-6"},F={class:"mt-6 grid grid-cols-1 gap-3"},K={__name:"LoginPage",setup(H){const x=V(),f=L(),r=_(),s=b({email:"",password:"",remember:!1}),p=async()=>{if((await r.login(s)).success){const e=f.query.redirect||"/dashboard";x.push(e)}},l=async m=>{const n={admin:{email:"<EMAIL>",password:"password"},organizer:{email:"<EMAIL>",password:"password"},attendee:{email:"<EMAIL>",password:"password"}}[m];n&&(s.email=n.email,s.password=n.password,await p())};return(m,e)=>{const n=w("router-link");return a(),i("div",S,[t("div",B,[t("div",null,[e[8]||(e[8]=t("div",{class:"mx-auto h-12 w-12 bg-indigo-600 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-8 h-8 text-white",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M10 2L3 7v11a1 1 0 001 1h3v-6h6v6h3a1 1 0 001-1V7l-7-5z"})])],-1)),e[9]||(e[9]=t("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Sign in to your account ",-1)),t("p",A,[e[7]||(e[7]=c(" Or ")),y(n,{to:"/register",class:"font-medium text-indigo-600 hover:text-indigo-500"},{default:v(()=>e[6]||(e[6]=[c(" create a new account ")])),_:1,__:[6]})])]),t("form",{class:"mt-8 space-y-6",onSubmit:h(p,["prevent"])},[t("div",M,[t("div",null,[e[10]||(e[10]=t("label",{for:"email",class:"sr-only"},"Email address",-1)),u(t("input",{id:"email","onUpdate:modelValue":e[0]||(e[0]=o=>s.email=o),name:"email",type:"email",autocomplete:"email",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"Email address"},null,512),[[g,s.email]])]),t("div",null,[e[11]||(e[11]=t("label",{for:"password",class:"sr-only"},"Password",-1)),u(t("input",{id:"password","onUpdate:modelValue":e[1]||(e[1]=o=>s.password=o),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[g,s.password]])])]),t("div",N,[t("div",D,[u(t("input",{id:"remember-me","onUpdate:modelValue":e[2]||(e[2]=o=>s.remember=o),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,512),[[C,s.remember]]),e[12]||(e[12]=t("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Remember me ",-1))]),e[13]||(e[13]=t("div",{class:"text-sm"},[t("a",{href:"#",class:"font-medium text-indigo-600 hover:text-indigo-500"}," Forgot your password? ")],-1))]),d(r).error?(a(),i("div",q,[t("div",E,[e[14]||(e[14]=t("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),t("div",P,[t("p",R,j(d(r).error),1)])])])):k("",!0),t("div",null,[t("button",{type:"submit",disabled:d(r).isLoading,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"},[e[15]||(e[15]=t("span",{class:"absolute left-0 inset-y-0 flex items-center pl-3"},[t("svg",{class:"h-5 w-5 text-indigo-500 group-hover:text-indigo-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),d(r).isLoading?(a(),i("span",U,"Signing in...")):(a(),i("span",$,"Sign in"))],8,T)]),t("div",O,[e[16]||(e[16]=z('<div class="relative"><div class="absolute inset-0 flex items-center"><div class="w-full border-t border-gray-300"></div></div><div class="relative flex justify-center text-sm"><span class="px-2 bg-gray-50 text-gray-500">Demo Accounts</span></div></div>',1)),t("div",F,[t("button",{type:"button",onClick:e[3]||(e[3]=o=>l("admin")),class:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"}," Login as Admin "),t("button",{type:"button",onClick:e[4]||(e[4]=o=>l("organizer")),class:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"}," Login as Organizer "),t("button",{type:"button",onClick:e[5]||(e[5]=o=>l("attendee")),class:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"}," Login as Attendee ")])])],32)])])}}};export{K as default};
