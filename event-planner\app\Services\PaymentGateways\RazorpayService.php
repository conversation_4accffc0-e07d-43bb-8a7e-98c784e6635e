<?php

namespace App\Services\PaymentGateways;

use Razorpay\Api\Api;
use Illuminate\Support\Facades\Log;

class RazorpayService implements PaymentGatewayInterface
{
    protected $api;
    protected $keyId;
    protected $keySecret;

    public function __construct()
    {
        $this->keyId = config('services.razorpay.key_id');
        $this->keySecret = config('services.razorpay.key_secret');
        
        if (!$this->keyId || !$this->keySecret) {
            throw new \Exception('Razorpay credentials not configured');
        }

        $this->api = new Api($this->keyId, $this->keySecret);
    }

    /**
     * Create payment order
     */
    public function createOrder(array $data): array
    {
        try {
            $orderData = [
                'receipt' => $data['receipt'],
                'amount' => $data['amount'] * 100, // Convert to paise
                'currency' => $data['currency'] ?? 'INR',
                'notes' => $data['notes'] ?? [],
            ];

            $order = $this->api->order->create($orderData);

            return [
                'success' => true,
                'order_id' => $order['id'],
                'amount' => $order['amount'],
                'currency' => $order['currency'],
                'key_id' => $this->keyId,
                'order_data' => $order->toArray(),
            ];

        } catch (\Exception $e) {
            Log::error('Razorpay order creation failed', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('Failed to create Razorpay order: ' . $e->getMessage());
        }
    }

    /**
     * Verify payment
     */
    public function verifyPayment(array $data): array
    {
        try {
            $attributes = [
                'razorpay_order_id' => $data['razorpay_order_id'],
                'razorpay_payment_id' => $data['razorpay_payment_id'],
                'razorpay_signature' => $data['razorpay_signature'],
            ];

            $this->api->utility->verifyPaymentSignature($attributes);

            // Get payment details
            $payment = $this->api->payment->fetch($data['razorpay_payment_id']);

            return [
                'success' => true,
                'data' => [
                    'payment_id' => $payment['id'],
                    'order_id' => $payment['order_id'],
                    'amount' => $payment['amount'] / 100, // Convert from paise
                    'currency' => $payment['currency'],
                    'status' => $payment['status'],
                    'method' => $payment['method'],
                    'captured' => $payment['captured'],
                    'created_at' => $payment['created_at'],
                    'raw_data' => $payment->toArray(),
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Razorpay payment verification failed', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Payment verification failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Process refund
     */
    public function refund(array $data): array
    {
        try {
            $refundData = [
                'amount' => $data['amount'] * 100, // Convert to paise
                'speed' => 'normal',
                'notes' => [
                    'reason' => $data['reason'] ?? 'requested_by_customer',
                ],
            ];

            $refund = $this->api->payment->fetch($data['payment_id'])->refund($refundData);

            return [
                'success' => true,
                'data' => [
                    'refund_id' => $refund['id'],
                    'payment_id' => $refund['payment_id'],
                    'amount' => $refund['amount'] / 100, // Convert from paise
                    'currency' => $refund['currency'],
                    'status' => $refund['status'],
                    'created_at' => $refund['created_at'],
                    'raw_data' => $refund->toArray(),
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Razorpay refund failed', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Refund failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get payment status
     */
    public function getPaymentStatus(string $paymentId): array
    {
        try {
            $payment = $this->api->payment->fetch($paymentId);

            return [
                'success' => true,
                'data' => [
                    'payment_id' => $payment['id'],
                    'order_id' => $payment['order_id'],
                    'amount' => $payment['amount'] / 100,
                    'currency' => $payment['currency'],
                    'status' => $payment['status'],
                    'method' => $payment['method'],
                    'captured' => $payment['captured'],
                    'created_at' => $payment['created_at'],
                ],
            ];

        } catch (\Exception $e) {
            Log::error('Failed to get Razorpay payment status', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to get payment status: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Create customer
     */
    public function createCustomer(array $data): array
    {
        try {
            $customer = $this->api->customer->create([
                'name' => $data['name'],
                'email' => $data['email'],
                'contact' => $data['phone'] ?? null,
                'notes' => $data['notes'] ?? [],
            ]);

            return [
                'success' => true,
                'customer_id' => $customer['id'],
                'data' => $customer->toArray(),
            ];

        } catch (\Exception $e) {
            Log::error('Razorpay customer creation failed', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to create customer: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Test connection
     */
    public function testConnection(): array
    {
        try {
            // Try to fetch a dummy order to test credentials
            $this->api->order->all(['count' => 1]);

            return [
                'success' => true,
                'message' => 'Razorpay connection successful',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Razorpay connection failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get supported payment methods
     */
    public function getSupportedMethods(): array
    {
        return [
            'card' => 'Credit/Debit Cards',
            'netbanking' => 'Net Banking',
            'wallet' => 'Wallets',
            'upi' => 'UPI',
            'emi' => 'EMI',
            'paylater' => 'Pay Later',
        ];
    }

    /**
     * Webhook signature verification
     */
    public function verifyWebhookSignature(string $payload, string $signature, string $secret): bool
    {
        try {
            $expectedSignature = hash_hmac('sha256', $payload, $secret);
            return hash_equals($expectedSignature, $signature);

        } catch (\Exception $e) {
            Log::error('Razorpay webhook signature verification failed', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
