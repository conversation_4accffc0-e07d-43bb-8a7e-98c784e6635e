name: issue.md
description: Create a report to help us improve
labels: ["issue"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this issue report!
  - type: textarea
    id: repro-steps
    attributes:
      label: Steps to reproduce the behavior
      description: 
      placeholder: |
        1. Fetch a '...'
        2. Update the '....'
        3. See error
    validations:
      required: true 
  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected behavior
      description: A clear and concise description of what you expected to happen.
    validations:
      required: true    
  - type: textarea
    id: actual-behavior
    attributes:
      label: Actual behavior
      description: A clear and concise description of what actually happen.
    validations:
      required: true      
  - type: textarea
    id: code-snippets
    attributes:
      label: Code snippets
      description: If applicable, add code snippets to help explain your problem.
      render: Php
    validations:
      required: false
  - type: input
    id: language-version
    attributes:
      label: Php version
      placeholder: Php v7.4
    validations:
      required: true
  - type: input
    id: lib-version
    attributes:
      label: Library version
      placeholder: razorpay-php v2.8.4
    validations:
      required: true
  - type: textarea
    id: additional-context
    attributes:
      label: Additional Information
      description: Add any other information about the problem here.
    validations:
      required: false
