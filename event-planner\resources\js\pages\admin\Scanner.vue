<template>
  <AdminLayout>
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Ticket Scanner</h1>
          <p class="text-gray-600">Scan QR codes to check-in attendees at events</p>
        </div>
        <div class="flex space-x-3">
          <select v-model="selectedEvent" 
                  class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
            <option value="">Select Event</option>
            <option v-for="event in events" :key="event.id" :value="event.id">
              {{ event.title }}
            </option>
          </select>
          <button @click="toggleScanner" 
                  :class="isScanning ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'"
                  class="text-white px-4 py-2 rounded-lg font-medium transition-colors">
            {{ isScanning ? 'Stop Scanner' : 'Start Scanner' }}
          </button>
        </div>
      </div>

      <!-- Scanner Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Scanned</p>
              <p class="text-2xl font-bold text-gray-900">{{ totalScanned }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Valid Tickets</p>
              <p class="text-2xl font-bold text-gray-900">{{ validScans }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-red-100 rounded-lg">
              <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Invalid Tickets</p>
              <p class="text-2xl font-bold text-gray-900">{{ invalidScans }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Duplicates</p>
              <p class="text-2xl font-bold text-gray-900">{{ duplicateScans }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Scanner Interface -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- QR Scanner -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">QR Code Scanner</h3>
          </div>
          <div class="p-6">
            <div v-if="!isScanning" class="text-center py-12">
              <svg class="mx-auto h-24 w-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 16h4.01M12 8h4.01M8 12h.01M16 8h.01M8 16h.01M8 8h.01"/>
              </svg>
              <h3 class="mt-4 text-lg font-medium text-gray-900">Scanner Ready</h3>
              <p class="mt-2 text-sm text-gray-500">Click "Start Scanner" to begin scanning QR codes</p>
            </div>
            
            <div v-else class="space-y-4">
              <!-- Camera Preview -->
              <div class="relative bg-black rounded-lg overflow-hidden" style="aspect-ratio: 1;">
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="w-64 h-64 border-2 border-white border-dashed rounded-lg flex items-center justify-center">
                    <span class="text-white text-sm">Camera Preview</span>
                  </div>
                </div>
                <!-- Scanning Animation -->
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="w-64 h-1 bg-red-500 animate-pulse"></div>
                </div>
              </div>
              
              <!-- Manual Entry -->
              <div class="border-t border-gray-200 pt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Manual Ticket Entry</label>
                <div class="flex space-x-2">
                  <input v-model="manualTicketCode" 
                         @keyup.enter="processManualEntry"
                         type="text" 
                         placeholder="Enter ticket code manually"
                         class="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                  <button @click="processManualEntry" 
                          class="bg-indigo-600 text-white px-4 py-2 rounded-md font-medium hover:bg-indigo-700">
                    Check In
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Scan Results -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Scans</h3>
          </div>
          <div class="p-6">
            <div v-if="recentScans.length === 0" class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
              </svg>
              <p class="mt-2 text-sm text-gray-500">No scans yet</p>
            </div>
            
            <div v-else class="space-y-4 max-h-96 overflow-y-auto">
              <div v-for="scan in recentScans" :key="scan.id" 
                   :class="[
                     'p-4 rounded-lg border-l-4',
                     scan.status === 'valid' ? 'bg-green-50 border-green-400' :
                     scan.status === 'invalid' ? 'bg-red-50 border-red-400' :
                     'bg-yellow-50 border-yellow-400'
                   ]">
                <div class="flex justify-between items-start">
                  <div>
                    <div class="flex items-center">
                      <span :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        scan.status === 'valid' ? 'bg-green-100 text-green-800' :
                        scan.status === 'invalid' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      ]">
                        {{ scan.status.toUpperCase() }}
                      </span>
                      <span class="ml-2 text-sm text-gray-500">{{ formatTime(scan.timestamp) }}</span>
                    </div>
                    <div class="mt-1">
                      <p class="text-sm font-medium text-gray-900">{{ scan.attendee_name || 'Unknown' }}</p>
                      <p class="text-xs text-gray-500">{{ scan.ticket_code }}</p>
                      <p v-if="scan.message" class="text-xs text-gray-600 mt-1">{{ scan.message }}</p>
                    </div>
                  </div>
                  <div class="text-right">
                    <svg v-if="scan.status === 'valid'" class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                    </svg>
                    <svg v-else-if="scan.status === 'invalid'" class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                    <svg v-else class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Scan History -->
      <div class="bg-white shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">Scan History</h3>
          <button @click="exportScanHistory" 
                  class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
            Export History
          </button>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendee</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket Code</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="scan in scanHistory" :key="scan.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatDateTime(scan.timestamp) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ scan.attendee_name || 'Unknown' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-600">
                  {{ scan.ticket_code }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                    scan.status === 'valid' ? 'bg-green-100 text-green-800' :
                    scan.status === 'invalid' ? 'bg-red-100 text-red-800' :
                    'bg-yellow-100 text-yellow-800'
                  ]">
                    {{ scan.status }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ scan.event_name }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import AdminLayout from '@/components/admin/AdminLayout.vue'

const isScanning = ref(false)
const selectedEvent = ref('')
const manualTicketCode = ref('')

const events = ref([
  { id: 1, title: 'Tech Conference 2025' },
  { id: 2, title: 'Music Festival' },
  { id: 3, title: 'Art Exhibition' }
])

const recentScans = ref([
  {
    id: 1,
    ticket_code: 'TC2025-001-001',
    attendee_name: 'John Doe',
    status: 'valid',
    timestamp: new Date(Date.now() - 2 * 60 * 1000),
    message: 'Check-in successful'
  },
  {
    id: 2,
    ticket_code: 'TC2025-002-001',
    attendee_name: 'Jane Smith',
    status: 'duplicate',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    message: 'Already checked in at 10:30 AM'
  },
  {
    id: 3,
    ticket_code: 'INVALID-CODE',
    attendee_name: null,
    status: 'invalid',
    timestamp: new Date(Date.now() - 8 * 60 * 1000),
    message: 'Ticket not found'
  }
])

const scanHistory = ref([
  {
    id: 1,
    ticket_code: 'TC2025-001-001',
    attendee_name: 'John Doe',
    status: 'valid',
    timestamp: new Date(Date.now() - 2 * 60 * 1000),
    event_name: 'Tech Conference 2025'
  },
  {
    id: 2,
    ticket_code: 'TC2025-002-001',
    attendee_name: 'Jane Smith',
    status: 'duplicate',
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    event_name: 'Tech Conference 2025'
  },
  {
    id: 3,
    ticket_code: 'INVALID-CODE',
    attendee_name: null,
    status: 'invalid',
    timestamp: new Date(Date.now() - 8 * 60 * 1000),
    event_name: 'Tech Conference 2025'
  }
])

const totalScanned = computed(() => scanHistory.value.length)
const validScans = computed(() => scanHistory.value.filter(s => s.status === 'valid').length)
const invalidScans = computed(() => scanHistory.value.filter(s => s.status === 'invalid').length)
const duplicateScans = computed(() => scanHistory.value.filter(s => s.status === 'duplicate').length)

const toggleScanner = () => {
  if (!selectedEvent.value) {
    alert('Please select an event first')
    return
  }
  
  isScanning.value = !isScanning.value
  
  if (isScanning.value) {
    // Start camera and QR scanning
    startQRScanner()
  } else {
    // Stop camera
    stopQRScanner()
  }
}

const startQRScanner = () => {
  // In a real implementation, this would initialize the camera and QR scanner
  console.log('Starting QR scanner...')
  
  // Simulate random scans for demo
  const simulateScans = () => {
    if (!isScanning.value) return
    
    const mockCodes = [
      'TC2025-003-001',
      'TC2025-004-001', 
      'INVALID-TICKET',
      'TC2025-001-001' // Duplicate
    ]
    
    const randomCode = mockCodes[Math.floor(Math.random() * mockCodes.length)]
    processTicketScan(randomCode)
    
    setTimeout(simulateScans, 3000 + Math.random() * 5000)
  }
  
  setTimeout(simulateScans, 2000)
}

const stopQRScanner = () => {
  console.log('Stopping QR scanner...')
}

const processTicketScan = (ticketCode) => {
  // Simulate ticket validation
  let status = 'valid'
  let attendeeName = 'Demo User'
  let message = 'Check-in successful'
  
  if (ticketCode.includes('INVALID')) {
    status = 'invalid'
    attendeeName = null
    message = 'Ticket not found'
  } else if (scanHistory.value.some(s => s.ticket_code === ticketCode && s.status === 'valid')) {
    status = 'duplicate'
    message = 'Already checked in'
  }
  
  const scan = {
    id: Date.now(),
    ticket_code: ticketCode,
    attendee_name: attendeeName,
    status: status,
    timestamp: new Date(),
    message: message,
    event_name: events.value.find(e => e.id === parseInt(selectedEvent.value))?.title || 'Unknown Event'
  }
  
  recentScans.value.unshift(scan)
  scanHistory.value.unshift(scan)
  
  // Keep only last 10 recent scans
  if (recentScans.value.length > 10) {
    recentScans.value = recentScans.value.slice(0, 10)
  }
  
  // Show notification
  showScanNotification(scan)
}

const processManualEntry = () => {
  if (!manualTicketCode.value.trim()) return
  
  processTicketScan(manualTicketCode.value.trim())
  manualTicketCode.value = ''
}

const showScanNotification = (scan) => {
  const message = scan.status === 'valid' 
    ? `✅ ${scan.attendee_name} checked in successfully`
    : scan.status === 'duplicate'
    ? `⚠️ ${scan.attendee_name} already checked in`
    : `❌ Invalid ticket: ${scan.ticket_code}`
  
  // In a real app, this would show a toast notification
  console.log(message)
}

const formatTime = (date) => {
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDateTime = (date) => {
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const exportScanHistory = () => {
  alert('Exporting scan history to CSV...')
}

onMounted(() => {
  // Initialize scanner if needed
})
</script>
