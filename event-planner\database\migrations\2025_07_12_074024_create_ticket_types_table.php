<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_types', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained('events')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->integer('quantity_available');
            $table->integer('quantity_sold')->default(0);
            $table->integer('min_quantity_per_order')->default(1);
            $table->integer('max_quantity_per_order')->default(10);
            $table->datetime('sale_start_date')->nullable();
            $table->datetime('sale_end_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_hidden')->default(false); // For special/private tickets
            $table->json('benefits')->nullable(); // List of ticket benefits
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['event_id', 'is_active']);
            $table->index(['sale_start_date', 'sale_end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_types');
    }
};
